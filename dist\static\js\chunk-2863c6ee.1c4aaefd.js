(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2863c6ee","chunk-2d0c51a4"],{"3e51":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"components-container"},[a("el-form",{ref:"pram",attrs:{"label-width":"150px",model:t.pram}},[a("el-form-item",{attrs:{label:"标题",prop:"title",rules:[{required:!0,message:"请填写标题",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"标题",maxlength:"100"},model:{value:t.pram.title,callback:function(e){t.$set(t.pram,"title",e)},expression:"pram.title"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"作者",prop:"author",rules:[{required:!0,message:"请填作者",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"作者",maxlength:"20"},model:{value:t.pram.author,callback:function(e){t.$set(t.pram,"author",e)},expression:"pram.author"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"文章分类",rules:[{required:!0,message:"请选择分类",trigger:["blur","change"]}]}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.pram.cid,callback:function(e){t.$set(t.pram,"cid",e)},expression:"pram.cid"}},t._l(t.categoryTreeData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"图文封面",prop:"cover",rules:[{required:!0,message:"请上传图文封面",trigger:"change"}]}},[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.pram.cover?a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.pram.cover}})]):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),a("el-form-item",{attrs:{label:"文章简介",prop:"synopsis",rules:[{required:!0,message:"请填写文章简介",trigger:["blur","change"]}]}},[a("el-input",{attrs:{maxlength:"100",type:"textarea",rows:2,resize:"none",placeholder:"文章简介"},model:{value:t.pram.synopsis,callback:function(e){t.$set(t.pram,"synopsis",e)},expression:"pram.synopsis"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"文章内容",prop:"content",rules:[{required:!0,message:"请填写文章内容",trigger:["blur","change"]}]}},[a("Tinymce",{model:{value:t.pram.content,callback:function(e){t.$set(t.pram,"content",e)},expression:"pram.content"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{attrs:{min:0,max:10,label:"排序"},model:{value:t.pram.sort,callback:function(e){t.$set(t.pram,"sort",e)},expression:"pram.sort"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否Banner"}},[a("el-switch",{model:{value:t.pram.isBanner,callback:function(e){t.$set(t.pram,"isBanner",e)},expression:"pram.isBanner"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否热门"}},[a("el-switch",{model:{value:t.pram.isHot,callback:function(e){t.$set(t.pram,"isHot",e)},expression:"pram.isHot"}})],1),t._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:article:update","platform:article:save"],expression:"['platform:article:update', 'platform:article:save']"}],attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.handerSubmit("pram")}}},[t._v("保存")])],1)],1)],1)])],1)},s=[],r=a("8256"),l=a("2423"),n=(a("785a"),a("5f87")),o=a("61f7"),c={components:{Tinymce:r["a"]},data:function(){return{loading:!1,constants:this.$constants,categoryTreeData:[],categoryProps:{value:"id",label:"name",children:"child",expandTrigger:"hover",checkStrictly:!0,emitPath:!1},pram:{author:null,cid:null,content:"",cover:"",isBanner:!1,isHot:null,shareSynopsis:null,shareTitle:null,sort:0,synopsis:null,title:null,id:null},editData:{},myHeaders:{"X-Token":Object(n["a"])()},editorContentLaebl:""}},created:function(){this.tempRoute=Object.assign({},this.$route)},mounted:function(){localStorage.getItem("articleClass")?this.categoryTreeData=JSON.parse(localStorage.getItem("articleClass")):this.handlerGetCategoryTreeData(),this.$route.params.id&&(this.getInfo(),this.setTagsViewTitle())},methods:{getInfo:function(){var t=this;l["c"](this.$route.params.id).then((function(e){t.editData=e,t.hadlerInitEditData()}))},modalPicTap:function(t){var e=this;this.$modalUpload((function(t){e.pram.cover=t[0].sattDir}),t,"content")},hadlerInitEditData:function(){if(this.$route.params.id){var t=this.editData,e=t.author,a=t.cid,i=t.content,s=t.cover,r=t.isBanner,l=t.isHot,n=t.shareSynopsis,o=t.shareTitle,c=t.sort,h=t.synopsis,d=t.title,u=t.id;this.pram.author=e,this.pram.cid=Number.parseInt(a),this.pram.content=i,this.pram.cover=s,this.pram.isBanner=r,this.pram.isHot=l,this.pram.shareSynopsis=n,this.pram.shareTitle=o,this.pram.sort=c,this.pram.synopsis=h,this.pram.title=d,this.pram.id=u}},handlerGetCategoryTreeData:function(){var t=this;l["h"]().then((function(e){t.categoryTreeData=e;var a=e.filter((function(t){return t.status}));localStorage.setItem("articleClass",JSON.stringify(a))}))},handerSubmit:Object(o["a"])((function(t){var e=this;this.$refs[t].validate((function(t){t&&(e.$route.params.id?e.handlerUpdate():e.handlerSave())}))})),handlerUpdate:function(){var t=this;this.loading=!0,this.pram.shareTitle=this.pram.title,this.pram.shareSynopsis=this.pram.synopsis,l["e"](this.pram).then((function(e){t.$message.success("编辑文章成功"),t.loading=!1,t.$router.push({path:"/content/articleManager"})})).catch((function(){t.loading=!1}))},handlerSave:function(){var t=this;this.loading=!0,this.pram.shareTitle=this.pram.title,this.pram.shareSynopsis=this.pram.synopsis,l["a"](this.pram).then((function(e){t.$message.success("新增文章成功"),t.loading=!1,t.$router.push({path:"/content/articleManager"})})).catch((function(){t.loading=!1}))},setTagsViewTitle:function(){var t="编辑文章",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)}}},h=c,d=a("2877"),u=Object(d["a"])(h,i,s,!1,null,"3bfcfb68",null);e["default"]=u.exports},"42a4":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:"",size:"small"}},[a("el-form-item",{attrs:{label:"文章分类："}},[a("el-select",{staticClass:"selWidth",attrs:{clearable:"",placeholder:"请选择文章分类"},on:{change:t.handerSearch},model:{value:t.listPram.cid,callback:function(e){t.$set(t.listPram,"cid",e)},expression:"listPram.cid"}},t._l(t.categoryTreeData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"作者："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入作者",size:"small",clearable:""},model:{value:t.author,callback:function(e){t.author=e},expression:"author"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handerSearch},slot:"append"})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"标题："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入标题",size:"small",clearable:""},model:{value:t.title,callback:function(e){t.title=e},expression:"title"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handerSearch},slot:"append"})],1)],1)],1)],1),t._v(" "),a("router-link",{attrs:{to:{path:"/content/articleCreat"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:article:save"],expression:"['platform:article:save']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"}},[t._v("添加文章")])],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:t.listData.list,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.cover,"preview-src-list":[t.row.cover]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"title",label:"标题","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{prop:"visit",label:"文章分类","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("articleTypeFilter")(e.row.cid)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"visit",label:"浏览量","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.visit))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"author",label:"作者","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序","show-overflow-tooltip":"","min-width":"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"启用","inactive-text":"禁用"},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"100",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("router-link",{attrs:{to:{path:"/content/articleCreat/"+e.row.id}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:article:update"],expression:"['platform:article:update']"}],staticClass:"mr10",attrs:{size:"small",type:"text"}},[t._v("编辑")])],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:article:delete"],expression:"['platform:article:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handlerDelete(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":t.constants.page.limit,layout:t.constants.page.layout,total:t.listData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{staticClass:"articleModal",attrs:{visible:t.editDialogConfig.visible,title:0===t.editDialogConfig.isEdit?"创建文章":"编辑文章",top:"1vh",width:"900px","destroy-on-close":"",modal:!1,"close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?a("edit",{attrs:{"is-edit":t.editDialogConfig.isEdit,"edit-data":t.editDialogConfig.editData},on:{hideDialog:t.handlerHideDialog}}):t._e()],1)],1)},s=[],r=a("2423"),l=a("3e51"),n={components:{edit:l["default"]},data:function(){return{constants:this.$constants,listPram:{author:null,cid:null,page:1,title:"",limit:this.$constants.page.limit[0]},author:"",title:"",listData:{list:[],total:0},editDialogConfig:{visible:!1,data:{},isEdit:0},listLoading:!0,categoryTreeData:[],categoryProps:{value:"id",label:"name",children:"child",expandTrigger:"hover",checkStrictly:!0,emitPath:!1}}},created:function(){localStorage.getItem("articleClass")?this.categoryTreeData=JSON.parse(localStorage.getItem("articleClass")):this.handlerGetTreeList(),this.handlerGetListData(this.listPram)},methods:{handleStatusChange:function(t){var e=this;r["k"](t.id).then((function(t){e.$message.success("更新状态成功"),e.handlerGetTreeList()}))},handlerGetTreeList:function(){var t=this;r["h"]().then((function(e){t.categoryTreeData=e;var a=e.filter((function(t){return t.status}));localStorage.setItem("articleClass",JSON.stringify(a))}))},handerSearch:function(){this.listPram.page=1,this.handlerGetListData(this.listPram)},handlerGetListData:function(t){var e=this;this.listLoading=!0,this.listPram.title=encodeURIComponent(this.title),this.listPram.author=encodeURIComponent(this.author),r["d"](t).then((function(t){e.listData=t,e.listLoading=!1}))},handlerOpenEdit:function(t,e){1===t?(this.editDialogConfig.isEdit=1,this.editDialogConfig.editData=e):this.editDialogConfig.isEdit=0,this.editDialogConfig.visible=!0},handlerHideDialog:function(){this.handlerGetListData(this.listPram),this.editDialogConfig.visible=!1},handlerDelete:function(t){var e=this;this.$modalSure("删除当前文章","提示").then((function(a){r["b"](t.id).then((function(t){e.$message.success("删除数据成功"),e.handlerGetListData(e.listPram)}))})).catch((function(){}))},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetListData(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetListData(this.listPram)}}},o=n,c=(a("a787"),a("2877")),h=Object(c["a"])(o,i,s,!1,null,"ea58c046",null);e["default"]=h.exports},"793a":function(t,e,a){},a787:function(t,e,a){"use strict";a("793a")}}]);