(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fc61b96a"],{"292a":function(t,e,r){},"59f4":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"container"},[r("el-form",{attrs:{inline:""}},[r("el-form-item",{attrs:{label:"商品名称："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称",clearable:""},model:{value:t.search,callback:function(e){t.search=e},expression:"search"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1)]),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{prop:"id",label:"Id","min-width":"80"}}),t._v(" "),r("el-table-column",{attrs:{label:"名称",prop:"title","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[r("div",{staticClass:"text_overflow",attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.title))]),t._v(" "),r("div",{staticClass:"pup_card"},[t._v(t._s(e.row.title))])])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"商品图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:JSON.parse(t.row.headImg)[0],"preview-src-list":JSON.parse(t.row.headImg)}})],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"类目","min-width":"150",prop:"thirdCatName",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"获得积分",prop:"giveIntegral","min-width":"100",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{prop:"sales",label:"销量","min-width":"90",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{prop:"stock",label:"库存","min-width":"90",align:"center"}}),t._v(" "),r("el-table-column",{attrs:{label:"微信审核","min-width":"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("editStatusFilter")(e.row.editStatus)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"平台审核","min-width":"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t._f("platformStatusFilter")(e.row.platformEditStatus)))])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"创建时间",prop:"addTime","min-width":"150"}}),t._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[2===e.row.platformEditStatus?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:draft:review"],expression:"['merchant:pay:component:product:draft:review']"}],attrs:{size:"small",type:"text"},on:{click:function(r){return t.handleAudit(e.row.id,!0)}}},[t._v("审核")]):t._e(),t._v(" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:draft:review"],expression:"['merchant:pay:component:product:draft:review']"}],attrs:{size:"small",type:"text"},on:{click:function(r){return t.handleAudit(e.row.id,!1)}}},[t._v("详情")])]}}])})],1),t._v(" "),r("div",{staticClass:"block mb20"},[r("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),r("info-from",{ref:"infoFrom",attrs:{"is-atud":t.isAtud,productId:t.productId},on:{subSuccess:t.subSuccess}})],1)},n=[],i=r("8bbf"),o=r("e350"),l=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"infoBox"},[t.dialogVisible?r("el-drawer",{staticClass:"infoBox",attrs:{visible:t.dialogVisible,title:t.isAtud?"商品审核":"商品详情",direction:t.direction,"append-to-body":!0,"custom-class":"demo-drawer",size:"1000px",wrapperClosable:!t.isAtud,"modal-append-to-body":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[r("div",{staticClass:"demo-drawer__content"},[t.formValidate?r("div",{staticClass:"divBox"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},[r("el-tab-pane",{attrs:{label:"商品信息",name:"0"}}),t._v(" "),r("el-tab-pane",{attrs:{label:"商品详情",name:"1"}})],1)],1),t._v(" "),r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"formValidate",attrs:{model:t.formValidate,"label-width":"111px"},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-row",{directives:[{name:"show",rawName:"v-show",value:"0"===t.currentTab,expression:"currentTab === '0'"}],attrs:{gutter:24}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品名称：",prop:"title"}},[r("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.title))])])],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[r("el-input",{attrs:{placeholder:"请输入单位",readonly:t.isDisabled},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"微信商品类目：",prop:"thirdCatIdList"}},t._l(t.formValidate.thirdCatIdList,(function(e,a){return r("span",[t._v("\n                  "+t._s(e)+" "),r("span",{directives:[{name:"show",rawName:"v-show",value:a<t.formValidate.thirdCatIdList.length-1,expression:"i < formValidate.thirdCatIdList.length - 1"}]},[t._v("/")])])})),0)],1),t._v(" "),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品轮播图："}},[r("div",{staticClass:"acea-row"},t._l(t.formValidate.images,(function(t,e){return r("div",{key:e,staticClass:"pictrue"},[r("img",{attrs:{src:t}})])})),0)])],1),t._v(" "),t.productQualificationType>0?r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品资质图："}},[r("div",{staticClass:"acea-row"},[t._l(t.formValidate.qualificationPicsList,(function(e,a){return r("div",{key:a,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(r){return t.handleDragStart(r,e,"qualificationPics")},dragover:function(r){return r.preventDefault(),t.handleDragOver(r,e,"qualificationPics")},dragenter:function(r){return t.handleDragEnter(r,e,"qualificationPics")},dragend:function(r){return t.handleDragEnd(r,e,"qualificationPics")}}},[r("img",{attrs:{src:e}}),t._v(" "),r("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(a,"qualificationPics")}}})])})),t._v(" "),t.formValidate.qualificationPicsList.length<5?r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("3")}}},[r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2),t._v(" "),r("div",{staticClass:"fontColor3"},[t._v("\n                  资质类型说明："+t._s(t.productQualification)+"。"+t._s(1===t.productQualificationType?"必填项！":"选填项！")+"\n                ")])])],1):t._e(),t._v(" "),r("el-col",t._b({},"el-col",t.grid2,!1),[r("el-form-item",{attrs:{label:"商品规格：",props:"specType"}},[r("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.specType?"多规格":"单规格"))])])],1),t._v(" "),r("el-col",{staticClass:"mt10",attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[[r("el-table",{staticClass:"tabNumWidth",attrs:{data:t.ManyAttrValue,border:"",size:"mini"}},[t.manyTabDate?t._l(t.manyTabDate,(function(e,a){return r("el-table-column",{key:a,attrs:{align:"center",label:t.manyTabTit[a].title},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[a])}})]}}],null,!0)})})):t._e(),t._v(" "),r("el-table-column",{attrs:{align:"center",label:"图片",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("div",{staticClass:"upLoadPicBox"},[e.row.image?r("div",{staticClass:"pictrue tabPic"},[r("img",{attrs:{src:e.row.image}})]):t._e()])]}}],null,!1,1312690362)}),t._v(" "),t._l(t.attrValue,(function(e,a){return r("el-table-column",{key:a,attrs:{label:t.formThead[a].title,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(e.row[a]))])]}}],null,!0)})}))],2)]],2)],1),t._v(" "),r("el-row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab&&!t.isDisabled,expression:"currentTab === '1' && !isDisabled"}]},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品详情："}},[r("div",{staticClass:"contentPic",domProps:{innerHTML:t._s(t.formValidate.content)}})])],1)],1),t._v(" "),r("el-row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab&&t.isDisabled,expression:"currentTab === '1' && isDisabled"}]},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商品详情："}},[r("span",{domProps:{innerHTML:t._s(t.formValidate.descInfo||"无")}})])],1)],1)],1)],1):t._e()]),t._v(" "),t.isAtud?r("div",{staticClass:"from-foot-btn btn-shadow"},[r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"审核状态",prop:"platformEditStatus"}},[r("el-radio-group",{model:{value:t.ruleForm.platformEditStatus,callback:function(e){t.$set(t.ruleForm,"platformEditStatus",e)},expression:"ruleForm.platformEditStatus"}},[r("el-radio",{attrs:{label:4}},[t._v("通过")]),t._v(" "),r("el-radio",{attrs:{label:3}},[t._v("拒绝")])],1)],1),t._v(" "),3===t.ruleForm.platformEditStatus?r("el-form-item",{attrs:{label:"原因",prop:"platformStatusReason"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入原因"},model:{value:t.ruleForm.platformStatusReason,callback:function(e){t.$set(t.ruleForm,"platformStatusReason",e)},expression:"ruleForm.platformStatusReason"}})],1):t._e(),t._v(" "),r("el-form-item",[r("el-button",{on:{click:t.close}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("ruleForm")}}},[t._v(t._s(t.loadingBtn?"提交中 ...":"确 定"))])],1)],1)],1):t._e()]):t._e()],1)},s=[],c=r("8256"),u=(r("61f7"),r("2f62"));function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(P){s=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var i=e&&e.prototype instanceof p?e:p,o=Object.create(i.prototype),l=new C(n||[]);return a(o,"_invoke",{value:V(t,r,l)}),o}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(P){return{type:"throw",arg:P}}}t.wrap=c;var m={};function p(){}function h(){}function v(){}var g={};s(g,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&r.call(y,i)&&(g=y);var w=v.prototype=p.prototype=Object.create(g);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(a,i,o,l){var s=u(t[a],t,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==d(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,o,l)}),(function(t){n("throw",t,o,l)})):e.resolve(f).then((function(t){c.value=t,o(c)}),(function(t){return n("throw",t,o,l)}))}l(s.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function V(t,e,r){var a="suspendedStart";return function(n,i){if("executing"===a)throw new Error("Generator is already running");if("completed"===a){if("throw"===n)throw i;return I()}for(r.method=n,r.arg=i;;){var o=r.delegate;if(o){var l=S(o,r);if(l){if(l===m)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===a)throw a="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a="executing";var s=u(t,e,r);if("normal"===s.type){if(a=r.done?"completed":"suspendedYield",s.arg===m)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(a="completed",r.method="throw",r.arg=s.arg)}}}function S(t,e){var r=e.method,a=t.iterator[r];if(void 0===a)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var n=u(a,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,m;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,m):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,m)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,n=function e(){for(;++a<t.length;)if(r.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:I}}function I(){return{value:void 0,done:!0}}return h.prototype=v,a(w,"constructor",{value:v,configurable:!0}),a(v,"constructor",{value:h,configurable:!0}),h.displayName=s(v,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,l,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},_(O.prototype),s(O.prototype,o,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new O(c(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},_(w),s(w,l,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},t.values=j,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(r,a){return o.type="throw",o.arg=t,e.next=r,a&&(e.method="next",e.arg=void 0),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],o=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;L(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),m}},t}function m(t,e,r,a,n,i,o){try{var l=t[i](o),s=l.value}catch(c){return void r(c)}l.done?e(s):Promise.resolve(s).then(a,n)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){m(i,a,n,o,l,"next",t)}function l(t){m(i,a,n,o,l,"throw",t)}o(void 0)}))}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach((function(e){g(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e,r){return e=b(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=y(t,"string");return"symbol"===d(e)?e:String(e)}function y(t,e){if("object"!==d(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!==d(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var w={image:"",sliderImages:[],videoLink:"",sliderImage:"",storeName:"",storeInfo:"",keyword:"",cateIds:[],couponList:[],cateId:null,unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0}],attr:[],selectRule:"",isSub:!1,content:"",specType:!1,id:void 0,couponIds:[],coupons:[],postage:"1",categoryId:"",guaranteeIds:"",guaranteeIdsList:[],brandId:""},_={price:{title:"售价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},O={name:"ProductProductAdd",props:{isAtud:{type:Boolean,default:!1},productId:{type:[Number,String],default:function(){return null}}},components:{Tinymce:c["a"]},data:function(){return{rules:{platformEditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],platformStatusReason:[{required:!0,message:"请填写拒绝原因",trigger:"blur"}]},isAppend:!0,proId:0,ruleForm:{platformStatusReason:"",platformEditStatus:4,draftProductId:""},direction:"rtl",dialogVisible:!1,isDisabled:!0,props2:{children:"childList",label:"name",value:"id",multiple:!0,emitPath:!1},props1:{children:"childList",label:"name",value:"id",multiple:!1,emitPath:!1},checkboxGroup:[],recommend:[{name:"可能喜欢",value:"isGood",type:"5"},{name:"热卖商品",value:"isHot",type:"2"},{name:"主打商品",value:"isBest",type:"1"}],tabs:[],fullscreenLoading:!1,props:{multiple:!0},active:0,OneattrValue:[Object.assign({},w.attrValue[0])],ManyAttrValue:[Object.assign({},w.attrValue[0])],ruleList:[],merCateList:[],shippingList:[],formThead:Object.assign({},_),formValidate:Object.assign({},w),formDynamics:{ruleName:"",ruleValue:[]},tempData:{page:1,limit:9999},manyTabTit:{},manyTabDate:{},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},formDynamic:{attrsName:"",attrsVal:""},isBtn:!1,manyFormValidate:[],currentTab:0,isChoice:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},attrInfo:{},tableFrom:{page:1,limit:9999,keywords:""},tempRoute:{},keyNum:0,isAttr:!1,showAll:!1,videoLink:"",search:{limit:10,page:1,cid:"",brandName:""},totalPage:0,total:0,loading:!1,loadingBtn:!1,propsCatId:{emitPath:!0},productQualificationType:0,productQualification:""}},computed:v(v({},Object(u["b"])(["adminProductClassify","productBrand"])),{},{attrValue:function(){var t=Object.assign({},w.attrValue[0]);return delete t.image,t},oneFormBatch:function(){var t=[Object.assign({},w.attrValue[0])];return t}}),watch:{"formValidate.attr":{handler:function(t){this.formValidate.specType&&this.isAttr&&this.watCh(t)},immediate:!1,deep:!0}},created:function(){this.tempRoute=Object.assign({},this.$route),this.$route.params.id&&this.formValidate.specType&&this.$watch("formValidate.attr",this.watCh),this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify")},mounted:function(){this.dialogVisible&&(this.setTagsViewTitle(),this.ruleForm.platformStatusReason="",this.ruleForm.platformEditStatus=4)},methods:{close:function(){this.dialogVisible=!1,this.ruleForm.platformStatusReason="",this.ruleForm.platformEditStatus=4},onSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loadingBtn=!0,e.ruleForm.draftProductId=e.productId,Object(i["g"])(e.ruleForm).then((function(t){e.$message.success("操作成功"),e.dialogVisible=!1,e.currentTab="0",e.ruleForm.platformStatusReason="",e.ruleForm.platformEditStatus=4,e.$emit("subSuccess"),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}))},changeNodes:function(t){if(t.length>0)for(var e=0;e<t.length;e++)!t[e].childList||t[e].childList.length<1?t[e].childList=void 0:this.changeNodes(t[e].childList);return t},setTagsViewTitle:function(){var t=this.isDisabled?"商品详情":"编辑商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)},watCh:function(t){var e=this,r={},a={};this.formValidate.attr.forEach((function(t,e){r[t.attrName]={title:t.attrName},a[t.attrName]=""})),this.ManyAttrValue=this.attrFormat(t),this.ManyAttrValue.forEach((function(t,r){var a=Object.values(t.attrValue).sort().join("/");e.attrInfo[a]&&(e.ManyAttrValue[r]=e.attrInfo[a])})),this.attrInfo=[],this.ManyAttrValue.forEach((function(t){e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t})),this.manyTabTit=r,this.manyTabDate=a,this.formThead=Object.assign({},this.formThead,r)},attrFormat:function(t){var e=[],r=[];return a(t);function a(t){if(t.length>1)t.forEach((function(a,n){0===n&&(e=t[n]["attrValue"]);var i=[];e&&(e.forEach((function(e){t[n+1]&&t[n+1]["attrValue"]&&t[n+1]["attrValue"].forEach((function(a){var o=(0!==n?"":t[n]["attrName"]+"_")+e+"$&"+t[n+1]["attrName"]+"_"+a;if(i.push(o),n===t.length-2){var l={image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0};for(var s in o.split("$&").forEach((function(t,e){var r=t.split("_");l["attrValue"]||(l["attrValue"]={}),l["attrValue"][r[0]]=r.length>1?r[1]:""})),l.attrValue)l[s]=l.attrValue[s];r.push(l)}}))})),e=i.length?i:[])}));else{var a=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,n){for(var i in a[n]=t["attrName"]+"_"+e,r[n]={image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,attrValue:g({},t["attrName"],e)},r[n].attrValue)r[n][i]=r[n].attrValue[i]}))})),e.push(a.join("$&"))}return r}},getInfo:function(t){var e=this;this.loading=!0,Object(i["e"])(t).then(function(){var t=p(f().mark((function t(r){var a;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=r,e.formValidate={images:JSON.parse(a.headImg),title:a.title,unitName:a.unitName,tempId:a.tempId,attr:JSON.parse(a.attr),descInfo:a.descInfo,specType:a.specType,primaryProductId:a.primaryProductId,giveIntegral:a.giveIntegral,ficti:a.ficti,thirdCatIdList:[a.catInfo.firstCatName,a.catInfo.secondCatName,a.catInfo.thirdCatName],thirdCatId:a.thirdCatId,brandId:a.brandId,qualificationPicsList:JSON.parse(a.qualificationPics)||[],id:a.id},e.ManyAttrValue=JSON.parse(a.attrValue),e.$nextTick((function(){e.ManyAttrValue.forEach((function(t,r){for(var a in t.attrValue=JSON.parse(t.attrValue),t.attrValue)t[a]=t.attrValue[a];t.id&&(e.$set(t,"price",t.price),e.$set(t,"quota",t.quota))}))})),e.loading=!1;case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1}))},validate:function(t,e,r){!1===e&&this.$message.warning(r)}}},V=O,S=(r("eaa5"),r("2877")),x=Object(S["a"])(V,l,s,!1,null,"2fc6726a",null),L=x.exports,C={name:"videoList",data:function(){return{isAtud:!1,productId:0,listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,proId:"",search:""},search:""}},components:{infoFrom:L},mounted:function(){this.getList(),JSON.parse(sessionStorage.getItem("videoCategory"))||this.getCatList()},methods:{checkPermi:o["a"],getCatList:function(){var t=this;Object(i["d"])().then((function(e){t.options=e,sessionStorage.setItem("videoCategory",JSON.stringify(e))}))},subSuccess:function(){this.getList("")},handleAudit:function(t,e){this.productId=t,this.$refs.infoFrom.dialogVisible=!0,this.isAtud=e,this.$refs.infoFrom.getInfo(t)},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,this.tableFrom.search=encodeURIComponent(this.search),Object(i["f"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},j=C,I=(r("ec04"),Object(S["a"])(j,a,n,!1,null,"57c2b734",null));e["default"]=I.exports},"8bbf":function(t,e,r){"use strict";r.d(e,"h",(function(){return n})),r.d(e,"k",(function(){return i})),r.d(e,"f",(function(){return o})),r.d(e,"n",(function(){return l})),r.d(e,"d",(function(){return s})),r.d(e,"c",(function(){return c})),r.d(e,"o",(function(){return u})),r.d(e,"m",(function(){return d})),r.d(e,"l",(function(){return f})),r.d(e,"e",(function(){return m})),r.d(e,"g",(function(){return p})),r.d(e,"i",(function(){return h})),r.d(e,"b",(function(){return v})),r.d(e,"a",(function(){return g})),r.d(e,"j",(function(){return b}));var a=r("b775");function n(){return Object(a["a"])({url:"/admin/platform/pay/component/register/register/check",method:"get"})}function i(t){return Object(a["a"])({url:"/admin/platform/pay/component/register/apply",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/admin/platform/pay/component/draftproduct/draft/list",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/admin/platform/pay/component/product/list",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/admin/platform/pay/component/cat/get/list",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/category/audit",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/admin/pay/component/product/listing/".concat(t),method:"post"})}function d(t){return Object(a["a"])({url:"/admin/pay/component/product/delisting/".concat(t),method:"post"})}function f(t){return Object(a["a"])({url:"/admin/pay/component/product/delete/".concat(t),method:"post"})}function m(t){return Object(a["a"])({url:"/admin/platform/pay/component/draftproduct/draft/get/".concat(t),method:"get"})}function p(t){return Object(a["a"])({url:"/admin/platform/pay/component/draftproduct/review",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/img/upload",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/brand/list",method:"get",data:t})}function g(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/audit/result",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/brand/audit",method:"post",data:t})}},eaa5:function(t,e,r){"use strict";r("f587")},ec04:function(t,e,r){"use strict";r("292a")},f587:function(t,e,r){}}]);