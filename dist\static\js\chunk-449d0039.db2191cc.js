(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-449d0039"],{"5e0a":function(t,e,a){"use strict";a("e656")},a6b1:function(t,e,a){},ceb4:function(t,e,a){"use strict";a("a6b1")},e656:function(t,e,a){},e6e2:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{ref:"headerBox",staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t.checkPermi(["platform:product:tabs:headers"])?a("el-tabs",{on:{"tab-click":function(e){t.getList(1),t.goodHeade()}},model:{value:t.tableFrom.type,callback:function(e){t.$set(t.tableFrom,"type",e)},expression:"tableFrom.type"}},t._l(t.headeNum,(function(t,e){return a("el-tab-pane",{key:e,attrs:{label:t.name+"("+t.count+")",name:t.type.toString()}})})),1):t._e(),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowSeach,expression:"isShowSeach"}],staticClass:"container mt-1"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"商品分类："}},[a("el-cascader",{ref:"cascader",staticClass:"selWidth",staticStyle:{width:"100%"},attrs:{clearable:"",options:t.adminProductClassify,props:t.categoryProps},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.categoryId,callback:function(e){t.$set(t.tableFrom,"categoryId",e)},expression:"tableFrom.categoryId"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商户类别："}},[a("el-select",{staticClass:"selWidth",attrs:{clearable:"",placeholder:"请选择"},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.isSelf,callback:function(e){t.$set(t.tableFrom,"isSelf",e)},expression:"tableFrom.isSelf"}},[a("el-option",{attrs:{label:"自营",value:1}}),t._v(" "),a("el-option",{attrs:{label:"非自营",value:0}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商户名称："}},[a("merchant-name",{on:{getMerId:t.getMerId}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品搜索："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称，关键字",size:"small",clearable:""},model:{value:t.keywords,callback:function(e){t.keywords=e},expression:"keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:page:list"],expression:"['platform:product:page:list']"}],attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:Number(t.tableFrom.type)<2,expression:"Number(tableFrom.type) < 2"}],staticStyle:{"margin-left":"0px"},attrs:{size:"mini"},on:{click:t.batchOff}},[t._v("批量强制下架")]),t._v(" "),a("el-button",{attrs:{size:"small",icon:"el-icon-upload2"},on:{click:function(e){t.isShowSeach=!t.isShowSeach}}},[t._v("显示隐藏")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}},on:{"selection-change":t.handleSelectionChange}},[Number(t.tableFrom.type)<3?a("el-table-column",{key:"2",attrs:{type:"selection",width:"55"}}):t._e(),t._v(" "),a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"商户类别："}},[a("span",[t._v(t._s(t._f("selfTypeFilter")(e.row.isSelf)))])]),t._v(" "),a("el-form-item",{attrs:{label:"邮费："}},[a("span",[t._v(t._s(e.row.postage))])]),t._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[a("span",[t._v(t._s(e.row.ficti))])]),t._v(" "),7==t.tableFrom.type?a("el-form-item",{attrs:{label:"拒绝原因："}},[a("span",[t._v(t._s(e.row.reason))])]):t._e()],1)]}}])}),t._v(" "),t.checkedCities.includes("ID")?a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}):t._e(),t._v(" "),t.checkedCities.includes("商品图")?a("el-table-column",{attrs:{label:"商品图","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}],null,!1,2331550732)}):t._e(),t._v(" "),t.checkedCities.includes("商品图")?a("el-table-column",{attrs:{label:"商品名称",prop:"name","min-width":"180","show-overflow-tooltip":!0}}):t._e(),t._v(" "),t.checkedCities.includes("商品售价")?a("el-table-column",{attrs:{label:"商品售价",prop:"price","min-width":"100"}}):t._e(),t._v(" "),t.checkedCities.includes("商户名称")?a("el-table-column",{attrs:{prop:"merchantName",label:"商户名称","min-width":"180","show-overflow-tooltip":!0}}):t._e(),t._v(" "),t.checkedCities.includes("商户类别")?a("el-table-column",{attrs:{label:"商户类别","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("selfTypeFilter")(e.row.isSelf)))])]}}],null,!1,611084893)}):t._e(),t._v(" "),t.checkedCities.includes("销量")?a("el-table-column",{attrs:{prop:"sales",label:"销量","min-width":"100"}}):t._e(),t._v(" "),t.checkedCities.includes("库存")?a("el-table-column",{attrs:{prop:"stock",label:"库存","min-width":"70"}}):t._e(),t._v(" "),t.checkedCities.includes("审核状态")?a("el-table-column",{attrs:{label:"审核状态","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("auditStatusFilter")(e.row.auditStatus)))])]}}],null,!1,4246894879)}):t._e(),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"180",fixed:"right",align:"center","render-header":t.renderHeader},scopedSlots:t._u([{key:"default",fn:function(e){return["6"===t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:audit"],expression:"['platform:product:audit']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleAudit(e.row.id,!0)}}},[t._v("审核")]):t._e(),t._v(" "),"6"===t.tableFrom.type||"1"===t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:update"],expression:"['platform:product:update']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleEdit(e.row)}}},[t._v("编辑")]):t._e(),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:info"],expression:"['platform:product:info']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleAudit(e.row.id,!1)}}},[t._v("详情")]),t._v(" "),Number(t.tableFrom.type)<2?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:force:down"],expression:"['platform:product:force:down']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleOff(e.row.id)}}},[t._v("强制下架")]):t._e(),t._v(" "),"5"===t.tableFrom.type?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleRestore(e.row.id,e.$index)}}},[t._v("恢复商品")]):t._e()]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.card_select_show,expression:"card_select_show"}],staticClass:"card_abs"},[[a("div",{staticClass:"cell_ht"},[a("el-checkbox",{attrs:{indeterminate:t.isIndeterminate},on:{change:t.handleCheckAllChange},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}},[t._v("全选")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.checkSave()}}},[t._v("保存")])],1),t._v(" "),a("el-checkbox-group",{on:{change:t.handleCheckedCitiesChange},model:{value:t.checkedCities,callback:function(e){t.checkedCities=e},expression:"checkedCities"}},t._l(t.columnData,(function(e){return a("el-checkbox",{key:e,staticClass:"check_cell",attrs:{label:e}},[t._v(t._s(e))])})),1)]],2),t._v(" "),a("info-from",{ref:"infoFrom",attrs:{componentKey:t.componentKey,"is-atud":t.isAtud,"is-show":t.isShow,productId:t.productId},on:{subSuccess:t.subSuccess}})],1)},i=[],n=a("c4c8"),o=(a("5f87"),a("e350")),s=a("6ed5"),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"infoBox"},[a("el-drawer",{ref:"drawer",staticClass:"infoBox",attrs:{visible:t.dialogVisible,title:t.isAtud?"商品审核":"商品详情",direction:t.direction,"append-to-body":!0,"custom-class":"demo-drawer",size:"1000px",wrapperClosable:!t.isAtud,"modal-append-to-body":!1},on:{"update:visible":function(e){t.dialogVisible=e},close:t.onClose}},[a("div",{staticClass:"demo-drawer__content"},[t.formValidate&&t.isShow?a("div",{staticClass:"divBox"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},[a("el-tab-pane",{attrs:{label:"商品信息",name:"0"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"商品详情",name:"1"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"其他设置",name:"2"}})],1)],1),t._v(" "),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"formValidate",attrs:{model:t.formValidate,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-row",{directives:[{name:"show",rawName:"v-show",value:"0"===t.currentTab,expression:"currentTab === '0'"}],attrs:{gutter:24}},[a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品名称：",prop:"name"}},[a("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.name))])])],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"平台商品分类："}},[a("el-cascader",{staticClass:"selWidth",attrs:{options:t.adminProductClassify,props:t.props1,"show-all-levels":!1,disabled:t.isDisabled},model:{value:t.formValidate.categoryId,callback:function(e){t.$set(t.formValidate,"categoryId",e)},expression:"formValidate.categoryId"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"品牌：",prop:"brandId"}},[a("el-select",{staticClass:"selWidth",attrs:{filterable:"",loading:t.loading,remote:"",disabled:t.isDisabled,placeholder:"请选择品牌"},model:{value:t.formValidate.brandId,callback:function(e){t.$set(t.formValidate,"brandId",e)},expression:"formValidate.brandId"}},t._l(t.brandList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品关键字：",prop:"keyword"}},[a("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.keyword))])])],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[a("el-input",{attrs:{placeholder:"请输入单位",readonly:t.isDisabled},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品简介：",prop:"intro"}},[a("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.intro))])])],1),t._v(" "),a("el-col",t._b({staticClass:"mb10"},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品封面图："}},[a("div",{staticClass:"upLoadPicBox",attrs:{disabled:t.isDisabled},on:{click:function(e){return t.modalPicTap("1")}}},[t.formValidate.image?a("div",{staticClass:"pictrue"},[a("el-image",{attrs:{src:t.formValidate.image,"preview-src-list":[t.formValidate.image]}})],1):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"保障服务："}},[a("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择保障服务",multiple:"",disabled:t.isDisabled},model:{value:t.formValidate.guaranteeIdsList,callback:function(e){t.$set(t.formValidate,"guaranteeIdsList",e)},expression:"formValidate.guaranteeIdsList"}},t._l(t.guaranteeList,(function(t,e){return a("el-option",{key:e,attrs:{value:t.id,label:t.name}})})),1)],1)],1),t._v(" "),a("el-col",{staticClass:"mb10",attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品轮播图："}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.sliderImages,(function(e,r){return a("div",{key:r,staticClass:"pictrue"},[a("el-image",{attrs:{src:e,"preview-src-list":t.formValidate.sliderImages}})],1)})),t._v(" "),t.formValidate.sliderImages.length<10&&!t.isDisabled?a("div",{staticClass:"upLoadPicBox"},[a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2)])],1),t._v(" "),a("el-col",t._b({staticClass:"mb10"},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品规格：",props:"specType"}},[a("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.specType?"多规格":"单规格"))])])],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"佣金设置：",props:"specType"}},[a("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.isSub?"单独设置":"默认设置"))])])],1),t._v(" "),a("el-col",{staticClass:"mt10",attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品属性：",props:"specType"}},[!1===t.formValidate.specType?[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.OneattrValue,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox"},[t.formValidate.image?a("div",{staticClass:"pictrue tabPic"},[a("el-image",{attrs:{src:e.row.image,"preview-src-list":[e.row.image]}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,3603157363)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row[r]))])]}}],null,!0)})}))],2)]:t._e(),t._v(" "),t.formValidate.attr.length>0&&t.formValidate.specType?[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.ManyAttrValue,border:"",size:"mini"}},[t.manyTabDate?t._l(t.manyTabDate,(function(e,r){return a("el-table-column",{key:r,attrs:{align:"center",label:t.manyTabTit[r].title},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[r])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片",width:"60"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"upLoadPicBox"},[t.row.image?a("div",{staticClass:"pictrue tabPic"},[a("el-image",{attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,2565633037)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row[r]))])]}}],null,!0)})}))],2)]:t._e()],2)],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab&&!t.isDisabled,expression:"currentTab === '1' && !isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("div",{staticClass:"contentPic",domProps:{innerHTML:t._s(t.formValidate.content)}})])],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab&&t.isDisabled,expression:"currentTab === '1' && isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("span",{domProps:{innerHTML:t._s(t.formValidate.content||"无")}})])],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:"2"===t.currentTab,expression:"currentTab === '2'"}]},[a("el-col",{attrs:{span:24}},[a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"排序："}},[a("span",{staticClass:"spfont"},[t._v(t._s(t.formValidate.sort))])])],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"proCoupon",attrs:{label:"优惠券："}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.couponList,(function(e,r){return a("el-tag",{key:r,staticClass:"mr10 mb10",attrs:{closable:!t.isDisabled,"disable-transitions":!1}},[t._v("\n                    "+t._s(e.name)+"\n                  ")])})),t._v(" "),0===t.formValidate.couponList.length?a("span",[t._v("无")]):t._e()],2)])],1)],1)],1)],1):t._e()]),t._v(" "),t.isAtud?a("div",{staticClass:"from-foot-btn btn-shadow"},[a("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[a("el-radio-group",{model:{value:t.ruleForm.auditStatus,callback:function(e){t.$set(t.ruleForm,"auditStatus",e)},expression:"ruleForm.auditStatus"}},[a("el-radio",{attrs:{label:"success"}},[t._v("通过")]),t._v(" "),a("el-radio",{attrs:{label:"fail"}},[t._v("拒绝")])],1)],1),t._v(" "),"fail"===t.ruleForm.auditStatus?a("el-form-item",{attrs:{label:"原因",prop:"reason"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入原因"},model:{value:t.ruleForm.reason,callback:function(e){t.$set(t.ruleForm,"reason",e)},expression:"ruleForm.reason"}})],1):t._e(),t._v(" "),a("el-form-item",[a("el-button",{on:{click:t.close}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("ruleForm")}}},[t._v(t._s(t.loadingBtn?"提交中 ...":"确 定"))])],1)],1)],1):t._e()])],1)},c=[],u=a("8256"),d=(a("61f7"),a("2f62"));function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var t={},e=Object.prototype,a=e.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},n=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(P){l=function(t,e,a){return t[e]=a}}function c(t,e,a,i){var n=e&&e.prototype instanceof h?e:h,o=Object.create(n.prototype),s=new L(i||[]);return r(o,"_invoke",{value:V(t,a,s)}),o}function u(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(P){return{type:"throw",arg:P}}}t.wrap=c;var d={};function h(){}function p(){}function g(){}var b={};l(b,n,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(I([])));y&&y!==e&&a.call(y,n)&&(b=y);var w=g.prototype=h.prototype=Object.create(b);function _(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function i(r,n,o,s){var l=u(t[r],t,n);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==m(d)&&a.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(d).then((function(t){c.value=t,o(c)}),(function(t){return i("throw",t,o,s)}))}s(l.arg)}var n;r(this,"_invoke",{value:function(t,a){function r(){return new e((function(e,r){i(t,a,e,r)}))}return n=n?n.then(r,r):r()}})}function V(t,e,a){var r="suspendedStart";return function(i,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw n;return O()}for(a.method=i,a.arg=n;;){var o=a.delegate;if(o){var s=S(o,a);if(s){if(s===d)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=u(t,e,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function S(t,e){var a=e.method,r=t.iterator[a];if(void 0===r)return e.delegate=null,"throw"===a&&t.iterator.return&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),d;var i=u(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,d;var n=i.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,d):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function I(t){if(t){var e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=g,r(w,"constructor",{value:g,configurable:!0}),r(g,"constructor",{value:p,configurable:!0}),p.displayName=l(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},_(k.prototype),l(k.prototype,o,(function(){return this})),t.AsyncIterator=k,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var o=new k(c(e,a,r,i),n);return t.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},_(w),l(w,s,"Generator"),l(w,n,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=I,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(a,r){return o.type="throw",o.arg=t,e.next=a,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],o=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var s=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,d):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),x(a),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var i=r.arg;x(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:I(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),d}},t}function h(t,e,a,r,i,n,o){try{var s=t[n](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(r,i)}function p(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var n=t.apply(e,a);function o(t){h(n,r,i,o,s,"next",t)}function s(t){h(n,r,i,o,s,"throw",t)}o(void 0)}))}}function g(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function b(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?g(Object(a),!0).forEach((function(e){v(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):g(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function v(t,e,a){return e=y(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function y(t){var e=w(t,"string");return"symbol"===m(e)?e:String(e)}function w(t,e){if("object"!==m(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==m(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var _={image:"",sliderImages:[],videoLink:"",sliderImage:"",name:"",intro:"",keyword:"",couponList:[],unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,brokerage:0,brokerageTwo:0}],attr:[],selectRule:"",isSub:!1,content:"",specType:!1,id:void 0,couponIds:[],coupons:[],postage:"1",categoryId:"",guaranteeIds:"",guaranteeIdsList:[],brandId:""},k={price:{title:"售价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"},brokerage:{title:"一级返佣(%)"},brokerageTwo:{title:"二级返佣(%)"}},V={name:"ProductProductAdd",props:{isShow:{type:Boolean,default:!0},isAtud:{type:Boolean,default:!1},productId:{type:[Number,String],default:function(){return null}},componentKey:{type:Number,default:function(){return 0}}},components:{Tinymce:u["a"]},data:function(){return{rules:{auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],reason:[{required:!0,message:"请填写拒绝原因",trigger:"blur"}]},isAppend:!0,proId:0,ruleForm:{reason:"",auditStatus:"success",id:""},direction:"rtl",dialogVisible:!1,isDisabled:!0,props2:{children:"childList",label:"name",value:"id",multiple:!0,emitPath:!1},props1:{children:"childList",label:"name",value:"id",multiple:!1,emitPath:!1},checkboxGroup:[],recommend:[{name:"可能喜欢",value:"isGood",type:"5"},{name:"热卖商品",value:"isHot",type:"2"},{name:"主打商品",value:"isBest",type:"1"}],tabs:[],fullscreenLoading:!1,props:{multiple:!0},active:0,OneattrValue:[Object.assign({},_.attrValue[0])],ManyAttrValue:[Object.assign({},_.attrValue[0])],ruleList:[],merCateList:[],shippingList:[],formThead:Object.assign({},k),formValidate:Object.assign({},_),formDynamics:{ruleName:"",ruleValue:[]},tempData:{page:1,limit:9999},manyTabTit:{},manyTabDate:{},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},formDynamic:{attrsName:"",attrsVal:""},isBtn:!1,manyFormValidate:[],currentTab:"0",isChoice:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},ruleValidate:{name:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cateIds:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array",min:"1"}],keyword:[{required:!0,message:"请输入商品关键字",trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],intro:[{required:!0,message:"请输入商品简介",trigger:"blur"}],postage:[{required:!0,message:"请输入运费",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],sliderImages:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}],brandId:[{required:!0,message:"请选择商品品牌",trigger:"change"}]},attrInfo:{},tableFrom:{page:1,limit:9999,keywords:""},tempRoute:{},keyNum:0,isAttr:!1,showAll:!1,videoLink:"",guaranteeList:[],brandList:[],search:{limit:10,page:1,cid:"",brandName:""},totalPage:0,total:0,loading:!1,loadingBtn:!1}},computed:b(b({},Object(d["b"])(["adminProductClassify","productBrand"])),{},{attrValue:function(){var t=Object.assign({},_.attrValue[0]);return delete t.image,this.formValidate.isSub||(delete t.brokerage,delete t.brokerageTwo),t}}),watch:{"formValidate.attr":{handler:function(t){this.formValidate.specType&&this.isAttr&&this.watCh(t)},immediate:!1,deep:!0},componentKey:{handler:function(t){this.currentTab="0",this.dialogVisible=!0},immediate:!1,deep:!0}},created:function(){this.tempRoute=Object.assign({},this.$route),this.currentTab="0",this.$route.params.id&&this.formValidate.specType&&this.$watch("formValidate.attr",this.watCh),this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify")},mounted:function(){this.getbrandList(),this.getProductGuarantee(),this.$route.params.id&&(this.$set("currentTab","0"),this.currentTab="0",this.setTagsViewTitle(),this.getInfo())},methods:{close:function(){this.dialogVisible=!1,this.currentTab="0"},onSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loadingBtn=!0,e.ruleForm.id=e.productId,Object(n["n"])(e.ruleForm).then((function(t){e.$message.success("操作成功"),e.dialogVisible=!1,e.currentTab="0",e.$emit("subSuccess"),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}))},changeNodes:function(t){if(t.length>0)for(var e=0;e<t.length;e++)!t[e].childList||t[e].childList.length<1?t[e].childList=void 0:this.changeNodes(t[e].childList);return t},getProductGuarantee:function(){var t=this;Object(n["i"])().then((function(e){t.guaranteeList=e,t.guaranteeList.unshift({id:0,name:"无"})}))},getbrandList:function(){var t=this;this.search.cid=this.formValidate.categoryId,Object(n["d"])(this.search).then((function(e){t.brandList=e.list,t.brandList.unshift({id:0,name:"其他"})}))},setTagsViewTitle:function(){var t=this.isDisabled?"商品详情":"编辑商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)},watCh:function(t){var e=this,a={},r={};this.formValidate.attr.forEach((function(t,e){a[t.attrName]={title:t.attrName},r[t.attrName]=""})),this.ManyAttrValue=this.attrFormat(t),this.ManyAttrValue.forEach((function(t,a){var r=Object.values(t.attrValue).sort().join("/");e.attrInfo[r]&&(e.ManyAttrValue[a]=e.attrInfo[r])})),this.attrInfo=[],this.ManyAttrValue.forEach((function(t){e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t})),this.manyTabTit=a,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,a)},attrFormat:function(t){var e=[],a=[];return r(t);function r(t){if(t.length>1)t.forEach((function(r,i){0===i&&(e=t[i]["attrValue"]);var n=[];e&&(e.forEach((function(e){t[i+1]&&t[i+1]["attrValue"]&&t[i+1]["attrValue"].forEach((function(r){var o=(0!==i?"":t[i]["attrName"]+"_")+e+"$&"+t[i+1]["attrName"]+"_"+r;if(n.push(o),i===t.length-2){var s={image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,brokerage:0,brokerageTwo:0};for(var l in o.split("$&").forEach((function(t,e){var a=t.split("_");s["attrValue"]||(s["attrValue"]={}),s["attrValue"][a[0]]=a.length>1?a[1]:""})),s.attrValue)s[l]=s.attrValue[l];a.push(s)}}))})),e=n.length?n:[])}));else{var r=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,i){for(var n in r[i]=t["attrName"]+"_"+e,a[i]={image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,brokerage:0,brokerageTwo:0,attrValue:v({},t["attrName"],e)},a[i].attrValue)a[i][n]=a[i].attrValue[n]}))})),e.push(r.join("$&"))}return a}},getInfo:function(t){var e=this;this.loading=!0,Object(n["v"])(t).then(function(){var t=p(f().mark((function t(a){var r,i,n,o,s,l;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:r=a,e.formValidate={image:e.$selfUtil.setDomain(r.image),sliderImage:r.sliderImage,sliderImages:JSON.parse(r.sliderImage),name:r.name,intro:r.intro,keyword:r.keyword,cateIds:r.cateId.split(","),cateId:r.cateId,unitName:r.unitName,sort:r.sort,isShow:r.isShow,tempId:r.tempId,attr:r.attr,attrValue:r.attrValue,selectRule:r.selectRule,isSub:r.isSub,content:e.$selfUtil.replaceImgSrcHttps(r.content),specType:r.specType,id:r.id,giveIntegral:r.giveIntegral,ficti:r.ficti,coupons:r.coupons,couponIds:r.couponIds,postage:r.postage,brandId:r.brandId,categoryId:r.categoryId,guaranteeIds:r.guaranteeIds,couponList:r.couponList||[],guaranteeIdsList:r.guaranteeIds.split(",").map(Number)},e.getbrandList(),i=JSON.parse(r.sliderImage),n=[],Object.keys(i).map((function(t){n.push(e.$selfUtil.setDomain(i[t]))})),e.formValidate.sliderImages=[].concat(n),"video"==e.getFileType(e.formValidate.sliderImages[0])&&(e.$set(e.formValidate,"videoLink",e.formValidate.sliderImages[0]),e.formValidate.sliderImages.splice(0,1)),r.specType?(e.formValidate.attr=r.attr.map((function(t){return{attrName:t.attrName,attrValue:t.attrValues.split(",")}})),e.ManyAttrValue=r.attrValue,e.ManyAttrValue.forEach((function(t){t.image=e.$selfUtil.setDomain(t.image),t.attrValue=JSON.parse(t.attrValue),e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t})),o=e.attrFormat(e.formValidate.attr),o.length!==e.ManyAttrValue.length?(e.$set(e,"showAll",!0),e.isAttr=!1):e.isAttr=!0,s={},l={},e.formValidate.attr.forEach((function(t,e){s[t.attrName]={title:t.attrName},l[t.attrName]=""})),e.formValidate.attrValue.forEach((function(t){for(var e in t.attrValue)t[e]=t.attrValue[e]})),e.manyTabTit=s,e.manyTabDate=l,e.formThead=Object.assign({},e.formThead,s)):e.OneattrValue=r.attrValue,e.loading=!1;case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loading=!1}))},validate:function(t,e,a){!1===e&&this.$message.warning(a)},zh_uploadFile:function(){this.videoLink&&this.$set(this.formValidate,"videoLink",this.videoLink)},getFileType:function(t){var e="",a="";try{var r=t.split(".");e=r[r.length-1]}catch(o){e=""}if(!e)return!1;e=e.toLocaleLowerCase();var i=["png","jpg","jpeg","bmp","gif"];if(a=i.find((function(t){return t===e})),a)return"image";var n=["mp4","m2v","mkv","rmvb","wmv","avi","flv","mov","m4v"];return a=n.find((function(t){return t===e})),a?"video":"other"},onClose:function(){this.ruleForm.auditStatus="success",this.ruleForm.reason=""}}},S=V,C=(a("ceb4"),a("2877")),x=Object(C["a"])(S,l,c,!1,null,"6bc4ed2e",null),L=x.exports;function I(t){return I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},I(t)}function O(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function P(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?O(Object(a),!0).forEach((function(e){j(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):O(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function j(t,e,a){return e=T(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function T(t){var e=N(t,"string");return"symbol"===I(e)?e:String(e)}function N(t,e){if("object"!==I(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==I(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var F=[{name:"出售中商品",type:1},{name:"仓库中商品",type:2},{name:"待审核商品",type:6},{name:"审核未通过商品",type:7}],$={name:"ProductList",directives:{selectLoadMore:{bind:function(t,e){var a=t.querySelector(".el-select-dropdown .el-select-dropdown__wrap");a.addEventListener("scroll",(function(){this.scrollHeight-this.scrollTop<this.clientHeight+1&&e.value()}))}}},components:{infoFrom:L,merchantName:s["a"]},data:function(){return{componentKey:0,isAtud:!1,isShow:!1,productId:0,categoryProps:{value:"id",label:"name",children:"childList",expandTrigger:"hover",checkStrictly:!1,emitPath:!1,multiple:!1},props:{children:"child",label:"name",value:"id",emitPath:!1},isShowSeach:!0,categoryIdData:[],headeNum:[],listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,categoryId:null,keywords:"",type:"1",isSelf:null,merId:null},keywords:"",categoryList:[],merCateList:[],objectUrl:"http://127.0.0.1:8080",dialogVisible:!1,card_select_show:!1,checkAll:!1,checkedCities:["ID","商品图","商品名称","商品售价","商户名称","商户类别","销量","库存","审核状态"],columnData:["ID","商品图","商品名称","商品售价","商户名称","商户类别","销量","库存","审核状态"],isIndeterminate:!0,merchantList:[],search:{limit:10,page:1,keywords:""},loading:!1,totalPage:0,total:0,keyNum:0,multipleSelection:[],OffId:[]}},mounted:function(){this.goodHeade(),this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify"),this.getList(),this.checkedCities=this.$cache.local.has("goods_stroge")?this.$cache.local.getJSON("goods_stroge"):this.checkedCities},computed:P(P({},Object(d["b"])(["adminProductClassify"])),{},{heightBoxs:function(){var t=this;this.$nextTick((function(){return Number(t.$refs.headerBox.offsetHeight)-Number(document.documentElement.clientHeight)}))}}),methods:{checkPermi:o["a"],getMerId:function(t){this.tableFrom.merId=t,this.subSuccess()},batchOff:function(){if(0===this.multipleSelection.length)return this.$message.warning("请先选择商品");this.handleOff(this.OffId)},handleOff:function(t){var e=this;this.$modalSure("强制下架吗").then((function(){Object(n["l"])({ids:t.toString()}).then((function(t){e.$message({type:"success",message:"提交成功"}),e.subSuccess()}))})).catch((function(){}))},handleSelectionChange:function(t){this.multipleSelection=t;var e=[];this.multipleSelection.map((function(t){e.push(t.id)})),this.OffId=e},handleEdit:function(t){var e=this;this.$modalParserFrom("编辑商品",37,1,{ficti:t.ficti||"0",id:t.id,rank:t.rank},(function(a){e.submit(a,t.id)}),this.keyNum+=5)},submit:function(t,e){var a=this,r={id:e,ficti:t.ficti,rank:t.rank};Object(n["D"])(r).then((function(t){a.$message.success("操作成功"),a.$msgbox.close(),a.getList(1)})).catch((function(){a.loading=!1}))},subSuccess:function(){this.getList(""),this.goodHeade()},handleAudit:function(t,e){this.productId=t,this.$refs.infoFrom.dialogVisible=!0,this.isShow=!0,this.isAtud=e,this.componentKey+=1,this.$refs.infoFrom.getInfo(t)},handleRestore:function(t){var e=this;this.$modalSure("恢复商品").then((function(){Object(n["C"])(t).then((function(t){e.$message.success("操作成功"),e.goodHeade(),e.getList(1)}))})).catch((function(){}))},handleClose:function(){this.dialogVisible=!1},exports:function(){Object(n["w"])({cateId:this.tableFrom.cateId,keywords:this.tableFrom.keywords,type:this.tableFrom.type}).then((function(t){window.location.href=t.fileName}))},goodHeade:function(){var t=this;Object(n["x"])().then((function(e){e.map((function(t,e){t.type===F[e].type&&(t.name=F[e].name)})),t.headeNum=e})).catch((function(e){t.$message.error(e.message)}))},getList:function(t){var e=this;this.tableFrom.page=t||this.tableFrom.page,this.tableFrom.keywords=encodeURIComponent(this.keywords),this.listLoading=!0,Object(n["y"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error(t.message)}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},handleDelete:function(t,e){var a=this;this.$modalSure("删除 id 为 ".concat(t," 的商品")).then((function(){var r=5==e?"delete":"recycle";Object(n["u"])(t,r).then((function(){a.$message.success("删除成功"),a.getList(),a.goodHeade()}))})).catch((function(){}))},onchangeIsShow:function(t){var e=this;t.isShow?Object(n["z"])(t.id).then((function(){e.$message.success("上架成功"),e.getList(),e.goodHeade()})).catch((function(){t.isShow=!t.isShow})):Object(n["l"])(t.id).then((function(){e.$message.success("下架成功"),e.getList(),e.goodHeade()})).catch((function(){t.isShow=!t.isShow}))},renderHeader:function(t){var e=this;return t("p",[t("span",{style:"padding-right:5px;"},["操作"]),t("i",{class:"el-icon-setting",on:{click:function(){return e.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCloseMod:function(t){this.dialogVisible=t,this.goodHeade(),this.getList()},handleCheckAllChange:function(t){this.checkedCities=t?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(t){var e=t.length;this.checkAll=e===this.columnData.length,this.isIndeterminate=e>0&&e<this.columnData.length},checkSave:function(){this.$set(this,"card_select_show",!1),this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("goods_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)}}},D=$,E=(a("5e0a"),Object(C["a"])(D,r,i,!1,null,"1f040914",null));e["default"]=E.exports}}]);