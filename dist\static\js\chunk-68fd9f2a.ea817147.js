(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-68fd9f2a"],{"8bbf":function(e,t,a){"use strict";a.d(t,"h",(function(){return r})),a.d(t,"k",(function(){return s})),a.d(t,"f",(function(){return i})),a.d(t,"n",(function(){return o})),a.d(t,"d",(function(){return c})),a.d(t,"c",(function(){return _})),a.d(t,"o",(function(){return d})),a.d(t,"m",(function(){return u})),a.d(t,"l",(function(){return p})),a.d(t,"e",(function(){return l})),a.d(t,"g",(function(){return m})),a.d(t,"i",(function(){return f})),a.d(t,"b",(function(){return v})),a.d(t,"a",(function(){return h})),a.d(t,"j",(function(){return g}));var n=a("b775");function r(){return Object(n["a"])({url:"/admin/platform/pay/component/register/register/check",method:"get"})}function s(e){return Object(n["a"])({url:"/admin/platform/pay/component/register/apply",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/admin/platform/pay/component/draftproduct/draft/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/admin/platform/pay/component/product/list",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/admin/platform/pay/component/cat/get/list",method:"get",params:e})}function _(e){return Object(n["a"])({url:"/admin/platform/pay/component/shop/category/audit",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/admin/pay/component/product/listing/".concat(e),method:"post"})}function u(e){return Object(n["a"])({url:"/admin/pay/component/product/delisting/".concat(e),method:"post"})}function p(e){return Object(n["a"])({url:"/admin/pay/component/product/delete/".concat(e),method:"post"})}function l(e){return Object(n["a"])({url:"/admin/platform/pay/component/draftproduct/draft/get/".concat(e),method:"get"})}function m(e){return Object(n["a"])({url:"/admin/platform/pay/component/draftproduct/review",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/admin/platform/pay/component/shop/img/upload",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/admin/platform/pay/component/shop/brand/list",method:"get",data:e})}function h(e){return Object(n["a"])({url:"/admin/platform/pay/component/shop/audit/result",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/admin/platform/pay/component/shop/brand/audit",method:"post",data:e})}},c297:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e._v("接入微信视频号步骤")]),e._v(" "),a("div",[1040002===e.toRegister.errcode?a("h1",[e._v("已经接入自定义交易组件")]):a("h1",[e._v("接入中"+e._s(e.toRegister))])]),e._v(" "),a("el-timeline",[a("el-timeline-item",{attrs:{timestamp:"创建视频号",placement:"top"}},[a("el-card",[a("el-form",{attrs:{inline:""}},[a("el-form-item",[a("p",[e._v("在微信平台中设置, 申请自定义交易组件，如果平台已有自定义交易组件跳过此步")])]),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){}}},[e._v("去申请(跳转文档链接)")])],1)],1)],1)],1),e._v(" "),a("el-timeline-item",{attrs:{timestamp:"申请开通自定义交易组件",placement:"top"}},[a("el-card",[a("el-form",{attrs:{inline:""}},[a("el-form-item",[a("p",[e._v("\n                完成自定义版交易组件接入后，小程序即可在视频号中实现商品展示和带货等功能，进一步提升经营能力。若您已开通标准化交易组件，则暂不支持切换\n              ")])]),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(t){return e.handleRegisterCheck()}}},[e._v("完成")])],1)],1)],1)],1),e._v(" "),a("el-timeline-item",{attrs:{timestamp:"自定义版交易组件申请通过，接口调用场景检测",placement:"top"}},[a("el-card",[a("el-form",[a("el-form-item",[a("p",[e._v("\n                自定义交易组件开通后，小程序版本必须大于等于4.1.5，如果不是 需要更新小程序\n                发布新的小程序(跳转小程序下载页面)\n              ")]),e._v(" "),a("router-link",{attrs:{to:"/videoChannel/draftList"}},[a("el-tag",{attrs:{type:"warning"}},[e._v("初次需要去审核商家提审的视频号商品")])],1)],1),e._v(" "),a("el-form-item",[a("el-form",[a("el-form-item",[a("span",[e._v("接入场景\n                    "+e._s(1===e.registerCheckData.data.scene_group_list[0].group_id?"视频号":"公众号场景"))])]),e._v(" "),a("el-form-item",[a("span",[e._v("场景名称 "+e._s(e.registerCheckData.data.scene_group_list[0].name))])]),e._v(" "),a("el-form-item",[a("span",[e._v("审核状态\n                    "),0===e.registerCheckData.data.scene_group_list[0].status?a("span",[e._v("审核中")]):e._e(),e._v(" "),1===e.registerCheckData.data.scene_group_list[0].status?a("span",[e._v("审核完成")]):e._e(),e._v(" "),2===e.registerCheckData.data.scene_group_list[0].status?a("span",[e._v("审核失败")]):e._e()])]),e._v(" "),a("el-form-item",[a("span",[e._v("场景审核结果\n                    "),e._l(e.registerCheckData.data.scene_group_list[0].scene_group_ext_list,(function(t,n){return a("span",{key:n},[1===t.ext_id?a("span",[e._v("客服售后 -》")]):e._e(),e._v(" "),2===t.ext_id?a("span",[e._v("电商平台 -》")]):e._e(),e._v(" "),a("el-tag",[0===t.status?a("span",[e._v("审核中")]):e._e(),e._v(" "),1===t.status?a("span",[e._v("审核成功")]):e._e(),e._v(" "),2===t.status?a("span",[e._v("审核失败")]):e._e(),e._v(" "),3===t.status?a("span",[e._v("未审核")]):e._e()])],1)}))],2)]),e._v(" "),a("el-form-item",[a("span",[e._v("审核理由 "+e._s(e.registerCheckData.data.scene_group_list[0].reason))])]),e._v(" "),a("el-form-item",[a("span",[e._v("上传商品并审核成功 ")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.spu_audit_success?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("商品接口调试完成")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.spu_audit_finished?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("发起一笔订单并支付成功")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.ec_order_success?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("订单接口调试完成")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.ec_order_finished?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("物流接口调用成功")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.send_delivery_success?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("物流接口调试完成")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.send_delivery_finished?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("售后接口调用成功")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.ec_after_sale_success?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("售后接口调试完成")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.ec_after_sale_finished?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("测试完成")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.test_api_finished?"未成功":"成功"))])],1),e._v(" "),a("el-form-item",[a("span",[e._v("发版完成")]),e._v(" "),a("el-tag",[e._v(e._s(0===e.registerCheckData.data.access_info.deploy_wxa_finished?"未成功":"成功"))])],1)],1)],1)],1)],1)],1),e._v(" "),a("el-timeline-item",{attrs:{timestamp:"自定义版交易组件开通成功",placement:"top"}})],1)],1)],1)},r=[],s=a("8bbf"),i={name:"index",data:function(){return{toRegister:{errcode:0,errmsg:""},registerCheckData:{errcode:0,errmsg:null,data:{status:2,reject_reason:"",access_info:{spu_audit_success:0,spu_audit_finished:0,ec_order_success:0,ec_order_finished:0,send_delivery_finished:0,send_delivery_success:0,ec_after_sale_finished:0,ec_after_sale_success:0,test_api_finished:0,deploy_wxa_finished:0,open_product_task_finished:0},scene_group_list:[{group_id:1,reason:"",name:"",status:0,scene_group_ext_list:[{ext_id:1,status:1}]}]}}}},created:function(){this.registerApply(),this.handleRegisterCheck()},methods:{registerApply:function(){var e=this;Object(s["k"])().then((function(t){e.toRegister=t}))},handleRegisterCheck:function(){var e=this;Object(s["h"])().then((function(t){e.registerCheckData=t})).finally((function(){e.$message.success("检查接入状态已更新")}))}}},o=i,c=a("2877"),_=Object(c["a"])(o,n,r,!1,null,"6002ddc8",null);t["default"]=_.exports}}]);