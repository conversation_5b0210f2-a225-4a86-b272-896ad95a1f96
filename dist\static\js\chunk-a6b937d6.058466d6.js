(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a6b937d6"],{"07fb":function(e,t,i){"use strict";i("3d5c")},"0902":function(e,t,i){},"21d2":function(e,t,i){"use strict";i.d(t,"a",(function(){return r})),i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return l})),i.d(t,"e",(function(){return n})),i.d(t,"f",(function(){return o})),i.d(t,"d",(function(){return c}));var a=i("b775");function r(){return Object(a["a"])({url:"/admin/platform/retail/store/config/get",method:"get"})}function s(e){return Object(a["a"])({url:"/admin/platform/retail/store/config/save",method:"post",data:e})}function l(e){return Object(a["a"])({url:"/admin/platform/retail/store/people/list",method:"get",params:e})}function n(e){return Object(a["a"])({url:"/admin/platform/retail/store/sub/user/list",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/admin/platform/retail/store/promotion/order/list",method:"get",params:e})}function c(e){return Object(a["a"])({url:"/admin/platform/retail/store/clean/spread/".concat(e),method:"get"})}},"2f2c":function(e,t,i){"use strict";i.d(t,"a",(function(){return u})),i.d(t,"d",(function(){return d})),i.d(t,"e",(function(){return v})),i.d(t,"g",(function(){return m})),i.d(t,"f",(function(){return f})),i.d(t,"b",(function(){return h})),i.d(t,"c",(function(){return p}));var a=i("b775");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function s(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,a)}return i}function l(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?s(Object(i),!0).forEach((function(t){n(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):s(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function n(e,t,i){return t=o(t),t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function o(e){var t=c(e,"string");return"symbol"===r(t)?t:String(t)}function c(e,t){if("object"!==r(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var a=i.call(e,t||"default");if("object"!==r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function u(){return Object(a["a"])({url:"/admin/platform/city/region/city/tree",method:"get"})}function d(e){return Object(a["a"])({url:"/admin/platform/express/list",method:"get",params:l({},e)})}function v(){return Object(a["a"])({url:"/admin/platform/express/sync/express",method:"post"})}function m(e){return Object(a["a"])({url:"/admin/platform/express/update/show",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/admin/platform/express/update",method:"post",data:e})}function h(e){return Object(a["a"])({url:"/admin/express/delete",method:"GET",params:l({},e)})}function p(e){return Object(a["a"])({url:"admin/platform/express/info",method:"get",params:l({},e)})}},"3d32":function(e,t,i){"use strict";i("0902")},"3d5c":function(e,t,i){},"99a7":function(e,t,i){},b9c2:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox relative"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"container"},[i("el-form",{ref:"userFrom",attrs:{inline:"",size:"small",model:e.userFrom,"label-position":e.labelPosition,"label-width":"100px"}},[i("el-row",[i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"用户昵称："}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户昵称",clearable:""},model:{value:e.keywords,callback:function(t){e.keywords=t},expression:"keywords"}})],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"手机号："}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入手机号",clearable:""},model:{value:e.phone,callback:function(t){e.phone=t},expression:"phone"}})],1)],1)],1),e._v(" "),e.collapse?[i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"用户标签："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.labelData,callback:function(t){e.labelData=t},expression:"labelData"}},e._l(e.labelLists,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.name}})})),1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"消费情况："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},model:{value:e.userFrom.payCount,callback:function(t){e.$set(e.userFrom,"payCount",t)},expression:"userFrom.payCount"}},[i("el-option",{attrs:{value:"",label:"全部"}}),e._v(" "),i("el-option",{attrs:{value:"0",label:"0"}}),e._v(" "),i("el-option",{attrs:{value:"1",label:"1+"}}),e._v(" "),i("el-option",{attrs:{value:"2",label:"2+"}}),e._v(" "),i("el-option",{attrs:{value:"3",label:"3+"}}),e._v(" "),i("el-option",{attrs:{value:"4",label:"4+"}}),e._v(" "),i("el-option",{attrs:{value:"5",label:"5+"}})],1)],1)],1)],1),e._v(" "),i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"性别："}},[i("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:e.userFrom.sex,callback:function(t){e.$set(e.userFrom,"sex",t)},expression:"userFrom.sex"}},[i("el-radio-button",{attrs:{label:""}},[i("span",[e._v("全部")])]),e._v(" "),i("el-radio-button",{attrs:{label:"0"}},[i("span",[e._v("未知")])]),e._v(" "),i("el-radio-button",{attrs:{label:"1"}},[i("span",[e._v("男")])]),e._v(" "),i("el-radio-button",{attrs:{label:"2"}},[i("span",[e._v("女")])]),e._v(" "),i("el-radio-button",{attrs:{label:"3"}},[i("span",[e._v("保密")])])],1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"身份："}},[i("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:e.userFrom.isPromoter,callback:function(t){e.$set(e.userFrom,"isPromoter",t)},expression:"userFrom.isPromoter"}},[i("el-radio-button",{attrs:{label:""}},[i("span",[e._v("全部")])]),e._v(" "),i("el-radio-button",{attrs:{label:"1"}},[i("span",[e._v("推广员")])]),e._v(" "),i("el-radio-button",{attrs:{label:"0"}},[i("span",[e._v("普通用户")])])],1)],1)],1)],1),e._v(" "),i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-form-item",{staticClass:"timeBox",attrs:{label:"时间选择："}},[i("el-date-picker",{staticClass:"selWidth",attrs:{align:"right","unlink-panels":"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间","picker-options":e.pickerOptions},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"注册类型："}},[i("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:function(t){return e.getList(1)}},model:{value:e.userFrom.registerType,callback:function(t){e.$set(e.userFrom,"registerType",t)},expression:"userFrom.registerType"}},e._l(e.registerTypeList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)]:e._e(),e._v(" "),i("el-col",{staticClass:"text-right userFrom userbtn",attrs:{xs:24,sm:24,md:24,lg:6,xl:6}},[i("el-form-item",[i("el-button",{staticClass:"mr15",attrs:{type:"primary",icon:"ios-search",label:"default",size:"small"},on:{click:e.userSearchs}},[e._v("搜索")]),e._v(" "),i("el-button",{staticClass:"ResetSearch mr10",attrs:{size:"small"},on:{click:function(t){return e.reset("userFrom")}}},[e._v("重置")]),e._v(" "),i("a",{staticClass:"ivu-ml-8",on:{click:function(t){e.collapse=!e.collapse}}},[e.collapse?[e._v(" 收起 "),i("i",{staticClass:"el-icon-arrow-up"})]:[e._v(" 展开 "),i("i",{staticClass:"el-icon-arrow-down"})]],2)],1)],1)],2)],1)],1),e._v(" "),i("div",{staticClass:"btn_bt"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:user:tag"],expression:"['admin:user:tag']"}],staticClass:"mr10",attrs:{size:"small"},on:{click:function(t){return e.setBatch("label")}}},[e._v("批量设置标签")])],1)]),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini","highlight-current-row":""},on:{"selection-change":e.onSelectTab}},[i("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[i("el-form-item",{attrs:{label:"身份："}},[i("span",[e._v(e._s(e._f("filterIsPromoter")(t.row.isPromoter)))])]),e._v(" "),i("el-form-item",{attrs:{label:"首次访问："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.createTime)))])]),e._v(" "),i("el-form-item",{attrs:{label:"近次访问："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.lastLoginTime)))])]),e._v(" "),i("el-form-item",{attrs:{label:"标签："}},[i("span",[e._v(e._s(e._f("tagFilter")(t.row.tagId)))])]),e._v(" "),i("el-form-item",{attrs:{label:"地址："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.province+t.row.city)))])]),e._v(" "),i("el-form-item",{staticStyle:{width:"100%",display:"flex","margin-right":"10px"},attrs:{label:"备注："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.mark)))])])],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),e.checkedCities.includes("ID")?i("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"80"}}):e._e(),e._v(" "),e.checkedCities.includes("头像")?i("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{staticClass:"demo-image__preview"},[i("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}],null,!1,3573681484)}):e._e(),e._v(" "),e.checkedCities.includes("姓名")?i("el-table-column",{attrs:{label:"姓名","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",{class:1==t.row.isLogoff?"red":""},[e._v(e._s(e._f("filterEmpty")(t.row.nickname))+" | "+e._s(e._f("sexFilter")(t.row.sex)))]),e._v(" "),1==t.row.isLogoff?i("span",{class:1==t.row.isLogoff?"red":""},[e._v("|")]):e._e(),e._v(" "),1==t.row.isLogoff?i("span",{staticClass:"red"},[e._v("(已注销)")]):e._e()]}}],null,!1,1602246744)}):e._e(),e._v(" "),e.checkedCities.includes("推荐人")?i("el-table-column",{attrs:{label:"推荐人","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.spreadName)))])]}}],null,!1,3565175388)}):e._e(),e._v(" "),e.checkedCities.includes("手机号")?i("el-table-column",{attrs:{label:"手机号","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.phone)))])]}}],null,!1,3743808278)}):e._e(),e._v(" "),e.checkedCities.includes("余额")?i("el-table-column",{attrs:{prop:"nowMoney",label:"余额","min-width":"100"}}):e._e(),e._v(" "),e.checkedCities.includes("积分")?i("el-table-column",{attrs:{prop:"integral",label:"积分","min-width":"100"}}):e._e(),e._v(" "),i("el-table-column",{attrs:{prop:"registerType",label:"注册类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-tag",{attrs:{color:e.filterRegisterType(t.row.registerType)}},[e._v(e._s(e._f("registerTypeFilter")(t.row.registerType)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right",align:"center","render-header":e.renderHeader},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:user:update"],expression:"['platform:user:update']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.editUser(t.row)}}},[e._v("编辑")]),e._v(" "),i("el-dropdown",{attrs:{trigger:"click"}},[i("span",{staticClass:"el-dropdown-link"},[e._v(" 更多"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e.checkPermi(["platform:user:detail"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.onDetails(t.row.id)}}},[e._v("用户详情")]):e._e(),e._v(" "),e.checkPermi(["admin:user:operate:founds"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.editPoint(t.row,"integral")}}},[e._v("修改积分")]):e._e(),e._v(" "),e.checkPermi(["admin:user:operate:founds"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.editPoint(t.row,"balance")}}},[e._v("修改余额")]):e._e(),e._v(" "),e.checkPermi(["platform:user:tag"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.setBatch("label",t.row)}}},[e._v("设置标签")]):e._e(),e._v(" "),e.checkPermi(["admin:user:update:spread"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.setExtension(t.row)}}},[e._v("修改上级推广人")]):e._e(),e._v(" "),t.row.spreadUid&&t.row.spreadUid>0&&e.checkPermi(["admin:retail:spread:clean"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.clearSpread(t.row)}}},[e._v("清除上级推广人")]):e._e()],1)],1)]}}])})],1),e._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"page-sizes":[15,30,45,60],"page-size":e.userFrom.limit,"current-page":e.userFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.card_select_show,expression:"card_select_show"}],staticClass:"card_abs",style:{top:e.collapse?"570px":"270px"}},[[i("div",{staticClass:"cell_ht"},[i("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),i("el-button",{attrs:{type:"text"},on:{click:function(t){return e.checkSave()}}},[e._v("保存")])],1),e._v(" "),i("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.checkedCities,callback:function(t){e.checkedCities=t},expression:"checkedCities"}},e._l(e.columnData,(function(t){return i("el-checkbox",{key:t,staticClass:"check_cell",attrs:{label:t}},[e._v(e._s(t))])})),1)]],2),e._v(" "),i("el-dialog",{attrs:{title:"修改上级推广人",visible:e.userVisible,width:"900px","before-close":e.handleCloseExtension},on:{"update:visible":function(t){e.userVisible=t}}},[e.userVisible?i("user-list",{attrs:{checked:e.currentSpreadUid,isLogoff:0},on:{getTemplateRow:e.getTemplateRow}}):e._e(),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleCloseExtension}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onSubExtension()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"设置",visible:e.dialogVisible,width:"500px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"dynamicValidateForm",staticClass:"demo-dynamic",attrs:{model:e.dynamicValidateForm,"label-width":"100px"}},[i("el-form-item",{attrs:{prop:"groupId",label:"用户标签",rules:[{required:!0,message:"请选择用户标签",trigger:"change"}]}},[i("el-select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择标签",filterable:"",multiple:""},model:{value:e.dynamicValidateForm.groupId,callback:function(t){e.$set(e.dynamicValidateForm,"groupId",t)},expression:"dynamicValidateForm.groupId"}},e._l(e.labelLists,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.name}})})),1)],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("dynamicValidateForm")}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"编辑",visible:e.visible,width:"600px"},on:{"update:visible":function(t){e.visible=t}}},[e.visible?i("edit-from",{attrs:{userInfo:e.userInfo},on:{resetForm:e.resetForm}}):e._e()],1),e._v(" "),i("el-dialog",{attrs:{title:"integral"===e.type?"积分":"余额",visible:e.VisiblePoint,width:"500px","close-on-click-modal":!1,"before-close":e.handlePointClose},on:{"update:visible":function(t){e.VisiblePoint=t}}},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingPoint,expression:"loadingPoint"}],ref:"PointValidateForm",staticClass:"demo-dynamic",attrs:{model:e.PointValidateForm,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"integral"===e.type?"积分":"余额",required:""}},[i("span",[e._v(e._s("integral"===e.type?e.userInfo.integral:e.userInfo.nowMoney))])]),e._v(" "),"integral"===e.type?[i("el-form-item",{attrs:{label:"修改积分",required:""}},[i("el-radio-group",{model:{value:e.PointValidateForm.operateType,callback:function(t){e.$set(e.PointValidateForm,"operateType",t)},expression:"PointValidateForm.operateType"}},[i("el-radio",{attrs:{label:"add"}},[e._v("增加")]),e._v(" "),i("el-radio",{attrs:{label:"sub"}},[e._v("减少")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"积分",required:""}},[i("el-input-number",{attrs:{type:"text","step-strictly":"",min:0,max:999999},model:{value:e.PointValidateForm.integral,callback:function(t){e.$set(e.PointValidateForm,"integral",t)},expression:"PointValidateForm.integral"}})],1)]:[i("el-form-item",{attrs:{label:"修改余额",required:""}},[i("el-radio-group",{model:{value:e.PointValidateForm.operateType,callback:function(t){e.$set(e.PointValidateForm,"operateType",t)},expression:"PointValidateForm.operateType"}},[i("el-radio",{attrs:{label:"add"}},[e._v("增加")]),e._v(" "),i("el-radio",{attrs:{label:"sub"}},[e._v("减少")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"余额",required:""}},[i("el-input-number",{attrs:{type:"text",precision:2,step:.1,min:0,max:999999},model:{value:e.PointValidateForm.money,callback:function(t){e.$set(e.PointValidateForm,"money",t)},expression:"PointValidateForm.money"}})],1)]],2),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handlePointClose}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",loading:e.loadingBtn},on:{click:function(t){return e.submitPointForm("PointValidateForm")}}},[e._v("确 定")])],1)],1),e._v(" "),i("detail-user",{ref:"userDetailFrom"})],1)},r=[],s=i("c24f"),l=i("21d2"),n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-drawer",{ref:"userDetailFrom",attrs:{visible:e.dialogUserDetail,size:"1200px"},on:{"update:visible":function(t){e.dialogUserDetail=t},close:e.handleClose}},[i("div",{staticClass:"title",attrs:{slot:"title"},slot:"title"}),e._v(" "),i("div",{staticClass:"demo-drawer__content"},[e.userDetailData?i("div",{staticClass:"description"},[i("div",{staticClass:"con-head"},[i("img",{attrs:{src:e.userDetailData.avatar,alt:""}}),e._v(" "),i("span",{staticClass:"nickname"},[e._v(e._s(e.userDetailData.nickname))])]),e._v(" "),i("div",{staticClass:"acea-row info-row"},[i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("用户余额")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.nowMoney))])]),e._v(" "),i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("用户经验")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.experience))])]),e._v(" "),i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("等级")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.level))])]),e._v(" "),i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("佣金金额")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.brokeragePrice))])]),e._v(" "),i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("用户积分")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.integral))])]),e._v(" "),i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("用户购买次数")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.payCount))])]),e._v(" "),i("div",{staticClass:"info-row-item"},[i("div",{staticClass:"info-row-item-title"},[e._v("连续签到天数")]),e._v(" "),i("div",[e._v(e._s(e.userDetailData.signNum))])])]),e._v(" "),i("div",{staticClass:"user-info"},[i("div",{staticClass:"section"},[i("div",{staticClass:"section-hd"},[e._v("基本信息")]),e._v(" "),i("div",{staticClass:"section-bd"},[i("div",{staticClass:"item"},[i("div",[e._v("用户电话：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.phone))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("真实姓名：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.realName||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("用户账号：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.account||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("生日：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.birthday||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("性别：")]),e._v(" "),i("div",{staticClass:"value"},[e._v("\n                  "+e._s(0==e.userDetailData.sex?"未知":1==e.userDetailData.sex?"男":2==e.userDetailData.sex?"女":"保密")+"\n                ")])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("国家：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s("CN"==e.userDetailData.country?"中国":"其他"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("用户地址：")]),e._v(" "),i("div",{staticClass:"value"},[e._v("\n                  "+e._s(e.userDetailData.province+e.userDetailData.city+e.userDetailData.district+e.userDetailData.address||"-")+"\n                ")])])])])]),e._v(" "),i("div",{staticClass:"user-info"},[i("div",{staticClass:"section"},[i("div",{staticClass:"section-hd-other"},[e._v("其他信息")]),e._v(" "),i("div",{staticClass:"section-bd"},[i("div",{staticClass:"item"},[i("div",[e._v("创建ip：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.addIp||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("添加时间：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.createTime||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否关联ios：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isBindingIos?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否注销：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isLogoff?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否为推广员：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isPromoter?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否关联微信android：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatAndroid?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否关联微信ios：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatIos?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否关联公众号：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatPublic?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("是否关联小程序：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatRoutine?"是":"否"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("最后一次登录ip：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.lastIp||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("最后一次登录时间：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.lastLoginTime||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("注销时间：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.logoffTime||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("成为分销员时间：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.promoterTime||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("注册类型：")]),e._v(" "),i("div",{staticClass:"value"},[e._v("\n                  "+e._s("wechat"==e.userDetailData.registerType?"公众号":"routine"==e.userDetailData.registerType?"小程序":"h5"==e.userDetailData.registerType?"H5":"iosWx"==e.userDetailData.registerType?"微信ios":"androidWx"==e.userDetailData.registerType?"微信安卓":"ios")+"\n                ")])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("下级人数：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadCount||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("上级推广员昵称：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadName||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("绑定上级推广员时间：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadTime||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("上级推广员id：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadUid||"-"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("状态：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.status?"正常":"禁止"))])]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("标签：")]),e._v(" "),i("div",{staticClass:"value",style:Array.isArray(e.userDetailData.tags)&&e.userDetailData.tags.length>0&&"margin-top: -8px"},[Array.isArray(e.userDetailData.tags)&&e.userDetailData.tags.length>0?e._l(e.userDetailData.tags,(function(t){return i("el-tag",{key:t.id,staticStyle:{"margin-right":"4px"}},[e._v(e._s(t.name))])})):[e._v(" - ")]],2)]),e._v(" "),i("div",{staticClass:"item"},[i("div",[e._v("备注：")]),e._v(" "),i("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.mark||"-"))])])])])])]):e._e()])])],1)},o=[],c={name:"detailUser",props:{userNo:{type:Number,default:0}},data:function(){return{dialogUserDetail:!1,userDetailData:{tags:[]}}},methods:{handleClose:function(){this.dialogUserDetail=!1},getUserDetail:function(e){var t=this;Object(s["w"])(e).then((function(e){t.userDetailData=e}))}}},u=c,d=(i("3d32"),i("2877")),v=Object(d["a"])(u,n,o,!1,null,"7b555011",null),m=v.exports,f=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"用户编号："}},[i("el-input",{staticClass:"selWidth",attrs:{disabled:""},model:{value:e.ruleForm.id,callback:function(t){e.$set(e.ruleForm,"id",t)},expression:"ruleForm.id"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户地址："}},[i("el-input",{staticClass:"selWidth",attrs:{disabled:""},model:{value:e.address,callback:function(t){e.address=t},expression:"address"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户标签："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.labelData,callback:function(t){e.labelData=t},expression:"labelData"}},e._l(e.labelLists,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.name}})})),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"生日："}},[i("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.ruleForm.birthday,callback:function(t){e.$set(e.ruleForm,"birthday",t)},expression:"ruleForm.birthday"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户备注："}},[i("el-input",{staticClass:"selWidth",attrs:{type:"textarea"},model:{value:e.ruleForm.mark,callback:function(t){e.$set(e.ruleForm,"mark",t)},expression:"ruleForm.mark"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"推广员"}},[i("el-radio-group",{model:{value:e.ruleForm.isPromoter,callback:function(t){e.$set(e.ruleForm,"isPromoter",t)},expression:"ruleForm.isPromoter"}},[i("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),i("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"状态"}},[i("el-radio-group",{model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[i("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),i("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:user:update"],expression:"['platform:user:update']"}],attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("提交")]),e._v(" "),i("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("取消")])],1)],1)},h=[],p=i("61f7");function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return e};var e={},t=Object.prototype,i=t.hasOwnProperty,a=Object.defineProperty||function(e,t,i){e[t]=i.value},r="function"==typeof Symbol?Symbol:{},s=r.iterator||"@@iterator",l=r.asyncIterator||"@@asyncIterator",n=r.toStringTag||"@@toStringTag";function o(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{o({},"")}catch(O){o=function(e,t,i){return e[t]=i}}function c(e,t,i,r){var s=t&&t.prototype instanceof v?t:v,l=Object.create(s.prototype),n=new S(r||[]);return a(l,"_invoke",{value:x(e,i,n)}),l}function u(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(O){return{type:"throw",arg:O}}}e.wrap=c;var d={};function v(){}function m(){}function f(){}var h={};o(h,s,(function(){return this}));var p=Object.getPrototypeOf,g=p&&p(p(L([])));g&&g!==t&&i.call(g,s)&&(h=g);var y=f.prototype=v.prototype=Object.create(h);function w(e){["next","throw","return"].forEach((function(t){o(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(a,s,l,n){var o=u(e[a],e,s);if("throw"!==o.type){var c=o.arg,d=c.value;return d&&"object"==_(d)&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,l,n)}),(function(e){r("throw",e,l,n)})):t.resolve(d).then((function(e){c.value=e,l(c)}),(function(e){return r("throw",e,l,n)}))}n(o.arg)}var s;a(this,"_invoke",{value:function(e,i){function a(){return new t((function(t,a){r(e,i,t,a)}))}return s=s?s.then(a,a):a()}})}function x(e,t,i){var a="suspendedStart";return function(r,s){if("executing"===a)throw new Error("Generator is already running");if("completed"===a){if("throw"===r)throw s;return P()}for(i.method=r,i.arg=s;;){var l=i.delegate;if(l){var n=D(l,i);if(n){if(n===d)continue;return n}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===a)throw a="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);a="executing";var o=u(e,t,i);if("normal"===o.type){if(a=i.done?"completed":"suspendedYield",o.arg===d)continue;return{value:o.arg,done:i.done}}"throw"===o.type&&(a="completed",i.method="throw",i.arg=o.arg)}}}function D(e,t){var i=t.method,a=e.iterator[i];if(void 0===a)return t.delegate=null,"throw"===i&&e.iterator.return&&(t.method="return",t.arg=void 0,D(e,t),"throw"===t.method)||"return"!==i&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+i+"' method")),d;var r=u(a,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var s=r.arg;return s?s.done?(t[e.resultName]=s.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):s:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function L(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,r=function t(){for(;++a<e.length;)if(i.call(e,a))return t.value=e[a],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:P}}function P(){return{value:void 0,done:!0}}return m.prototype=f,a(y,"constructor",{value:f,configurable:!0}),a(f,"constructor",{value:m,configurable:!0}),m.displayName=o(f,n,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,o(e,n,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},w(C.prototype),o(C.prototype,l,(function(){return this})),e.AsyncIterator=C,e.async=function(t,i,a,r,s){void 0===s&&(s=Promise);var l=new C(c(t,i,a,r),s);return e.isGeneratorFunction(i)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},w(y),o(y,n,"Generator"),o(y,s,(function(){return this})),o(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),i=[];for(var a in t)i.push(a);return i.reverse(),function e(){for(;i.length;){var a=i.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},e.values=L,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(F),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function a(i,a){return l.type="throw",l.arg=e,t.next=i,a&&(t.method="next",t.arg=void 0),!!a}for(var r=this.tryEntries.length-1;r>=0;--r){var s=this.tryEntries[r],l=s.completion;if("root"===s.tryLoc)return a("end");if(s.tryLoc<=this.prev){var n=i.call(s,"catchLoc"),o=i.call(s,"finallyLoc");if(n&&o){if(this.prev<s.catchLoc)return a(s.catchLoc,!0);if(this.prev<s.finallyLoc)return a(s.finallyLoc)}else if(n){if(this.prev<s.catchLoc)return a(s.catchLoc,!0)}else{if(!o)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return a(s.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var s=r;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var l=s?s.completion:{};return l.type=e,l.arg=t,s?(this.method="next",this.next=s.finallyLoc,d):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),F(i),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var a=i.completion;if("throw"===a.type){var r=a.arg;F(i)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:L(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),d}},e}function g(e,t,i,a,r,s,l){try{var n=e[s](l),o=n.value}catch(c){return void i(c)}n.done?t(o):Promise.resolve(o).then(a,r)}function y(e){return function(){var t=this,i=arguments;return new Promise((function(a,r){var s=e.apply(t,i);function l(e){g(s,a,r,l,n,"next",e)}function n(e){g(s,a,r,l,n,"throw",e)}l(void 0)}))}}var w={id:null,mark:"",addres:"",groupId:"",level:"",isPromoter:!1,status:!1},C={name:"UserEdit",props:{userInfo:{type:Object,default:null}},data:function(){return{ruleForm:Object.assign({},w),groupData:[],labelData:[],rules:{},labelLists:[]}},computed:{address:function(){return this.ruleForm.province+this.ruleForm.city}},mounted:function(){this.userInfo&&(this.ruleForm=JSON.parse(JSON.stringify(this.userInfo))),this.labelLists=JSON.parse(localStorage.getItem("tagAllList"))||[],this.labelData=this.userInfo.tagId?this.userInfo.tagId.split(",").map(Number):[]},methods:{submitForm:Object(p["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.ruleForm.tagId=t.labelData.join(","),Object(s["z"])({id:t.ruleForm.id},t.ruleForm).then(function(){var e=y(b().mark((function e(i){return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$message.success("编辑成功"),t.$parent.$parent.visible=!1,t.$parent.$parent.getList();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}))})),resetForm:function(e){this.$refs[e].resetFields(),this.$emit("resetForm")}}},x=C,D=(i("07fb"),Object(d["a"])(x,f,h,!1,null,"33c939b8",null)),k=D.exports,F=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,"label-width":"100px"}},[i("el-form-item",[i("el-alert",{attrs:{title:"请勿频繁更改，以免计算产生混乱！",type:"warning"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户等级","label-width":"100px"}},[i("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:e.currentSel},model:{value:e.ruleForm.levelId,callback:function(t){e.$set(e.ruleForm,"levelId",t)},expression:"ruleForm.levelId"}},e._l(e.levelList,(function(e){return i("el-option",{key:e.grade,attrs:{label:e.name,value:e.id}})})),1)],1),e._v(" "),""!=e.grade&&e.grade<e.levelInfo.gradeLevel?i("el-form-item",{attrs:{label:"扣除经验","label-width":"100px"}},[i("el-switch",{model:{value:e.ruleForm.isSub,callback:function(t){e.$set(e.ruleForm,"isSub",t)},expression:"ruleForm.isSub"}})],1):e._e(),e._v(" "),i("el-form-item",[i("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("确定")])],1)],1)},S=[],L={props:{levelInfo:{type:Object,default:{}},levelList:{type:Array,default:[]}},data:function(){return{grade:"",levelStatus:!1,ruleForm:{isSub:!1,levelId:"",uid:this.levelInfo.uid}}},created:function(){this.ruleForm.levelId=this.levelInfo.level?Number(this.levelInfo.level):""},watch:{levelInfo:function(e){this.ruleForm.uid=e.uid||0,this.ruleForm.levelId=this.levelInfo.level?Number(this.levelInfo.level):e.levelId}},methods:{submitForm:Object(p["a"])((function(e){var t=this;this.$refs[e].validate((function(i){if(!i)return!1;Object(s["x"])(t.ruleForm).then((function(i){t.$message.success("编辑成功"),t.$parent.$parent.getList(),t.$parent.$parent.levelVisible=!1,t.$refs[e].resetFields(),t.grade=""}))}))})),currentSel:function(){var e=this;this.levelList.forEach((function(t){t.id==e.ruleForm.levelId&&(e.grade=t.grade)}))},resetForm:function(e){var t=this;this.$nextTick((function(){t.$refs[e].resetFields(),t.grade=""})),this.$parent.$parent.levelVisible=!1}}},P=L,O=Object(d["a"])(P,F,S,!1,null,null,null),I=O.exports,$=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-form",{attrs:{inline:""}},[i("el-form-item",[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户名称"},model:{value:e.keywords,callback:function(t){e.keywords=t},expression:"keywords"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.search},slot:"append"})],1)],1)],1)],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableData.data,width:"800px",size:"small"}},[i("el-table-column",{attrs:{label:"",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-radio",{attrs:{label:t.row.id},nativeOn:{change:function(i){return e.getTemplateRow(t.$index,t.row)}},model:{value:e.templateRadio,callback:function(t){e.templateRadio=t},expression:"templateRadio"}},[e._v(" ")])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),e._v(" "),i("el-table-column",{attrs:{prop:"nickname",label:"微信用户名称","min-width":"130"}}),e._v(" "),i("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{staticClass:"demo-image__preview"},[i("el-image",{staticClass:"tabImage",attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"性别","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("saxFilter")(t.row.sex)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"地区","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.province+t.row.city||"-"))])]}}])})],1),e._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1)],1)},j=[],V={name:"UserList",props:{checked:{type:Number,default:0},isLogoff:{type:Number,default:0}},filters:{saxFilter:function(e){var t={0:"未知",1:"男",2:"女",3:"保密"};return t[e]},statusFilter:function(e){var t={wechat:"微信用户",routine:"小程序用户"};return t[e]}},data:function(){return{templateRadio:this.checked,loading:!1,tableData:{data:[],total:0},tableFrom:{page:1,limit:10,nikename:"",isLogoff:this.isLogoff},keywords:""}},mounted:function(){this.getList()},methods:{getTemplateRow:function(e,t){this.$emit("getTemplateRow",t)},getList:function(){var e=this;this.loading=!0,this.tableFrom.nikename=encodeURIComponent(this.keywords),Object(s["y"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.loading=!1})).catch((function(t){e.$message.error(t.message),e.loading=!1}))},search:function(){this.getList()},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()}}},E=V,T=Object(d["a"])(E,$,j,!1,null,"658449ea",null),N=T.exports,A=(i("2f2c"),i("a78e"),i("e350")),U={name:"UserIndex",components:{editFrom:k,userList:N,levelEdit:I,detailUser:m},filters:{sexFilter:function(e){var t={0:"未知",1:"男",2:"女",3:"保密"};return t[e]}},data:function(){return{registerTypeList:[{value:"wechat",label:"公众号"},{value:"routine",label:"小程序"},{value:"h5",label:"H5"},{value:"iosWx",label:"微信ios"},{value:"androidWx",label:"微信安卓"},{value:"ios",label:"ios"}],formExtension:{spreadUid:"",userId:""},ruleInline:{},extensionVisible:!1,userVisible:!1,levelInfo:"",pickerOptions:this.$timeOptions,loadingBtn:!1,PointValidateForm:{integral:null,operateType:"add",uid:"",money:null},loadingPoint:!1,VisiblePoint:!1,visible:!1,userIds:"",dialogVisible:!1,levelVisible:!1,labelData:[],selData:[],keywords:"",labelPosition:"right",collapse:!1,listLoading:!0,tableData:{data:[],total:0},userFrom:{tagIds:"",sex:"",registerType:"",payCount:"",accessType:0,dateLimit:"",nikename:"",phone:"",page:1,limit:15},phone:"",grid:{xl:8,lg:12,md:12,sm:24,xs:24},labelLists:[],selectedData:[],timeVal:[],dynamicValidateForm:{groupId:[]},loading:!1,groupIdFrom:[],selectionList:[],batchName:"",uid:0,Visible:!1,keyNum:0,multipleSelectionAll:[],idKey:"uid",card_select_show:!1,checkAll:!1,checkedCities:["ID","头像","姓名","用户等级","推荐人","手机号","余额","积分"],columnData:["ID","头像","姓名","用户等级","推荐人","手机号","余额","积分"],isIndeterminate:!0,type:"integral",userInfo:{},currentSpreadUid:0}},mounted:function(){this.getList(1),this.getTagList()},activated:function(){this.userFrom.nikename="",this.getList(1),this.getTagList()},methods:{checkPermi:A["a"],filterRegisterType:function(e){var t={wechat:"#FD5ACC",routine:"#A277FF",h5:"#E8B600",iosWx:"#1BBE6B",androidWx:"#EF9C20",ios:"#1890FF"};return t[e]},setPhone:function(e){var t=this;this.$prompt("修改手机号",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入修改手机号",inputType:"text",inputValue:e.phone,inputPlaceholder:"请输入手机号",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"请填写手机号"}}).then((function(i){var a=i.value;Object(s["u"])({id:e.uid,phone:a}).then((function(){t.$message.success("编辑成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},clearSpread:function(e){var t=this;this.$modalSure("解除【"+e.nickname+"】的上级推广人吗").then((function(){Object(l["d"])(e.id).then((function(e){t.$message.success("清除成功"),t.getList()}))})).catch((function(){}))},onSubExtension:function(){var e=this;0!=this.formExtension.spreadUid&&Object(s["v"])(this.formExtension).then((function(t){e.$message.success("设置成功"),e.handleCloseExtension(),e.getList()}))},getTemplateRow:function(e){this.formExtension.spreadUid=e.id},setExtension:function(e){this.formExtension={spreadUid:"",userId:e.id},this.currentSpreadUid=e.spreadUid,this.userVisible=!0},handleCloseExtension:function(){this.userVisible=!1,this.currentSpreadUid=0},resetForm:function(){this.visible=!1},reset:function(e){this.userFrom={tagIds:"",sex:"",isPromoter:"",payCount:"",accessType:0,dateLimit:"",province:"",city:"",page:1,limit:15,nikename:""},this.keywords="",this.phone="",this.labelData=[],this.timeVal=[],this.getList()},onSend:function(){if(0===this.selectionList.length)return this.$message.warning("请选择要设置的用户");var e=this;this.$modalCoupon("send",this.keyNum+=1,[],(function(t){e.formValidate.give_coupon_ids=[],e.couponData=[],t.map((function(t){e.formValidate.give_coupon_ids.push(t.coupon_id),e.couponData.push(t.title)})),e.selectionList=[]}),this.userIds,"user")},Close:function(){this.Visible=!1,this.levelVisible=!1},onDetails:function(e){this.$refs.userDetailFrom.getUserDetail(e),this.$refs.userDetailFrom.dialogUserDetail=!0},editPoint:function(e,t){this.uid=e.id,this.type=t,this.userInfo=e,this.VisiblePoint=!0},submitPointForm:Object(p["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.PointValidateForm.uid=t.uid,t.loadingBtn=!0,"integral"===t.type?Object(s["d"])(t.PointValidateForm).then((function(e){t.$message.success("设置成功"),t.loadingBtn=!1,t.handlePointClose(),t.getList()})).catch((function(){t.loadingBtn=!1})):Object(s["a"])(t.PointValidateForm).then((function(e){t.$message.success("设置成功"),t.loadingBtn=!1,t.handlePointClose(),t.getList()})).catch((function(){t.loadingBtn=!1}))}))})),handlePointClose:function(){this.VisiblePoint=!1,this.PointValidateForm={integral:null,operateType:"add",uid:"",money:null}},editUser:function(e){this.userInfo=e,this.visible=!0},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.loading=!0,Object(s["r"])({tagIds:t.dynamicValidateForm.groupId.join(","),ids:t.userIds}).then((function(e){t.$message.success("设置成功"),t.loading=!1,t.handleClose(),t.getList(),t.$refs.table.clearSelection()})).catch((function(){t.loading=!1}))}))},setBatch:function(e,t){if(this.batchName=e,t?(this.userIds=t.id,this.dynamicValidateForm.groupId=t.tagId?t.tagId.split(",").map(Number):[]):this.dynamicValidateForm.groupId="",0===this.multipleSelectionAll.length&&!t)return this.$message.warning("请选择要设置的用户");this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.$refs["dynamicValidateForm"].resetFields()},onSelectTab:function(e){var t=this;this.selectionList=e,setTimeout((function(){t.changePageCoreRecordData();var e=[];t.multipleSelectionAll.length&&(t.multipleSelectionAll.map((function(t){e.push(t.id)})),t.userIds=e.join(","))}),50)},userSearchs:function(){this.userFrom.page=1,this.getList()},onchangeTime:function(e){this.timeVal=e,this.userFrom.dateLimit=e?this.timeVal.join(","):""},getTagList:function(){var e=this;Object(s["o"])().then((function(t){e.labelLists=t,localStorage.setItem("tagAllList",JSON.stringify(t))}))},getList:function(e){var t=this;this.listLoading=!0,this.userFrom.page=e||this.userFrom.page,this.userFrom.tagIds=this.labelData.join(","),this.userFrom.nikename=encodeURIComponent(this.keywords),this.userFrom.phone=encodeURIComponent(this.phone),Object(s["y"])(this.userFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.$nextTick((function(){this.setSelectRow()})),t.listLoading=!1})).catch((function(){t.listLoading=!1})),this.checkedCities=this.$cache.local.has("user_stroge")?this.$cache.local.getJSON("user_stroge"):this.checkedCities,this.$set(this,"card_select_show",!1)},setSelectRow:function(){if(this.multipleSelectionAll&&!(this.multipleSelectionAll.length<=0)){var e=this.idKey,t=[];this.multipleSelectionAll.forEach((function(i){t.push(i[e])})),this.$refs.table.clearSelection();for(var i=0;i<this.tableData.data.length;i++)t.indexOf(this.tableData.data[i][e])>=0&&this.$refs.table.toggleRowSelection(this.tableData.data[i],!0)}},changePageCoreRecordData:function(){var e=this.idKey,t=this;if(this.multipleSelectionAll.length<=0)this.multipleSelectionAll=this.selectionList;else{var i=[];this.multipleSelectionAll.forEach((function(t){i.push(t[e])}));var a=[];this.selectionList.forEach((function(r){a.push(r[e]),i.indexOf(r[e])<0&&t.multipleSelectionAll.push(r)}));var r=[];this.tableData.data.forEach((function(t){a.indexOf(t[e])<0&&r.push(t[e])})),r.forEach((function(a){if(i.indexOf(a)>=0)for(var r=0;r<t.multipleSelectionAll.length;r++)if(t.multipleSelectionAll[r][e]==a){t.multipleSelectionAll.splice(r,1);break}}))}},pageChange:function(e){this.changePageCoreRecordData(),this.userFrom.page=e,this.getList()},handleSizeChange:function(e){this.changePageCoreRecordData(),this.userFrom.limit=e,this.getList(1)},handleDelete:function(e,t){var i=this;this.$modalSure().then((function(){productDeleteApi(e).then((function(){i.$message.success("删除成功"),i.getList(1)}))})).catch((function(){}))},onchangeIsShow:function(e){var t=this;e.isShow?putOnShellApi(e.id).then((function(){t.$message.success("上架成功"),t.getList()})).catch((function(){e.isShow=!e.isShow})):offShellApi(e.id).then((function(){t.$message.success("下架成功"),t.getList()})).catch((function(){e.isShow=!e.isShow}))},renderHeader:function(e){var t=this;return e("p",[e("span",{style:"padding-right:5px;"},["操作"]),e("i",{class:"el-icon-setting",on:{click:function(){return t.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(e){this.checkedCities=e?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.columnData.length,this.isIndeterminate=t>0&&t<this.columnData.length},checkSave:function(){this.card_select_show=!1,this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("user_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)}}},R=U,z=(i("c460"),Object(d["a"])(R,a,r,!1,null,"45e5979a",null));t["default"]=z.exports},c460:function(e,t,i){"use strict";i("99a7")}}]);