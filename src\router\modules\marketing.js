// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout';

const marketingRouter = {
  path: '/marketing',
  component: Layout,
  redirect: '/coupon/list',
  name: 'Marketing',
  meta: {
    title: '营销',
    icon: 'clipboard',
  },
  children: [
    {
      path: 'sign',
      component: () => import('@/views/marketing/sign'),
      name: 'Sign',
      meta: { title: '签到', icon: '' },
      children: [
        {
          path: 'config',
          component: () => import('@/views/marketing/sign/config/index'),
          name: 'signConfig',
          hidden: true,
          meta: { title: '签到配置', icon: '' },
        },
        {
          path: 'record',
          component: () => import('@/views/marketing/sign/record/index'),
          name: 'signRecord',
          hidden: true,
          meta: { title: '签到记录', icon: '' },
        },
      ],
    },
    {
      path: 'integral',
      component: () => import('@/views/marketing/integral/index'),
      name: 'Integral',
      meta: { title: '积分', icon: '' },
      children: [
        {
          path: 'integralconfig',
          component: () => import('@/views/marketing/integral/config/index'),
          name: 'integralConfig',
          meta: { title: '积分配置', icon: '' },
        },
        {
          path: 'integrallog',
          component: () => import('@/views/marketing/integral/integralLog/index'),
          name: 'integralLog',
          meta: { title: '积分日志', icon: '' },
        },
      ],
    },
  ],
};

export default marketingRouter;
