(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3acccbb8"],{"40d2":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div")},a=[],r=n("2877"),l={},o=Object(r["a"])(l,i,a,!1,null,null,null);t["a"]=o.exports},"4be0":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"divBox"},[n("el-card",{staticClass:"box-card"},[e.checkPermi(["platform:system:form:info"])?n("el-tabs",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],on:{"tab-click":e.handleTabClick},model:{value:e.activeNamel1,callback:function(t){e.activeNamel1=t},expression:"activeNamel1"}},e._l(e.treeList,(function(t,i){return n("el-tab-pane",{key:i,attrs:{label:t.name,name:t.id.toString()}},[[t.child&&t.child.length>0?n("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.handleItemTabClick},model:{value:e.activeNamel2,callback:function(t){e.activeNamel2=t},expression:"activeNamel2"}},e._l(t.child,(function(t,i){return n("el-tab-pane",{key:i,attrs:{label:t.name,name:t.extra}},[e.formConfChild.render?n("parser",{attrs:{"is-edit":e.formConfChild.isEdit,"form-conf":e.formConfChild.content,"form-edit-data":e.currentEditData},on:{submit:e.handlerSubmit}}):e._e()],1)})),1):n("span",[e.formConfChild.render?n("parser",{attrs:{"is-edit":e.formConfChild.isEdit,"form-conf":e.formConfChild.content,"form-edit-data":e.currentEditData},on:{submit:e.handlerSubmit}}):e._e()],1)]],2)})),1):e._e()],1)],1)},a=[],r=n("3fbe"),l=n("651a"),o=n("fca7"),d=n("92c6"),c=(n("785a"),n("2b9b")),s=n("40d2"),f=(n("9255"),n("e350")),h=n("61f7"),u={components:{Template:s["a"],parser:r["a"]},data:function(){return{loading:!1,formConf:{content:{fields:[]},id:null,render:!1,isEdit:!1},formConfChild:{content:{fields:[]},id:null,render:!1,isEdit:!1},activeNamel1:null,activeNamel2:"",treeList:[],editDataChild:{},isCreate:0,currentEditId:null,currentEditData:null,currentSelectedUploadFlag:null}},mounted:function(){this.handlerGetTreeList(),this.getCurrentUploadSelectedFlag()},methods:{checkPermi:f["a"],handleTabClick:function(e){this.formConfChild.render=!1,e.$children.length&&e.$children[0].panes?this.activeNamel2=e.$children[0].panes[0].name:this.getFromId(),this.activeNamel2&&this.handlerGetLevel2FormConfig(this.activeNamel2)},getFromId:function(){var e=this;this.activeNamel2=this.treeList.find((function(t){return t.id==e.activeNamel1})).extra},handlerGetLevel1FormConfig:function(e){var t=this,n={id:e};this.currentEditId=e,this.formConf.content={fields:[]},this.formConf.render=!1,this.loading=!0,d["b"](n).then((function(e){var n=e.id,i=(e.name,e.info,e.content);t.formConf.content=JSON.parse(i),t.formConf.id=n,t.handlerGetSettingInfo(n,1),t.loading=!1})).catch((function(){t.loading=!1}))},handleItemTabClick:function(e,t){var n=e.name?e.name:e;if(!n)return this.$message.error("表单配置不正确，请关联正确表单后使用");this.handlerGetLevel2FormConfig(n)},handlerGetLevel2FormConfig:function(e){var t=this,n={id:e};this.currentEditId=e,this.formConfChild.content={fields:[]},this.formConfChild.render=!1,this.loading=!0,d["b"](n).then((function(e){var n=e.id,i=(e.name,e.info,e.content);t.formConfChild.content=JSON.parse(i),t.formConfChild.id=n,t.handlerGetSettingInfo(n,2),t.loading=!1})).catch((function(){t.loading=!1}))},handlerGetSettingInfo:function(e,t){var n=this;c["b"]({id:e}).then((function(e){n.currentEditData=e,n.formConfChild.isEdit=null!==n.currentEditData,n.formConfChild.render=!0}))},handlerSubmit:Object(h["a"])((function(e){this.handlerSave(e)})),handlerSave:function(e){var t=this,n=this.buildFormPram(e);c["c"](n).then((function(e){t.$message.success("添加数据成功")}))},handlerGetTreeList:function(){var e=this,t={type:this.$constants.categoryType[5].value,status:1};this.loading=!0,l["d"](t).then((function(t){e.treeList=e.handleAddArrt(t),e.treeList.length>0&&(e.activeNamel1=e.treeList[0].id.toString()),e.treeList.length>0&&e.treeList[0].child?e.activeNamel2=e.treeList[0].child[0].extra:e.getFromId(),e.handlerGetLevel2FormConfig(e.activeNamel2),e.loading=!1})).catch((function(){e.loading=!1}))},handleAddArrt:function(e){var t=o["addTreeListLabel"](e);return t},buildFormPram:function(e){var t={fields:[],id:this.currentEditId,sort:0,status:!0},n=[];return Object.keys(e).forEach((function(t){n.push({name:t,title:t,value:e[t]})})),t.fields=n,t},getCurrentUploadSelectedFlag:function(){var e=this;c["a"]({key:"uploadType"}).then((function(t){e.currentSelectedUploadFlag=parseInt(t)}))}}},m=u,v=n("2877"),C=Object(v["a"])(m,i,a,!1,null,"6ae6c682",null);t["default"]=C.exports}}]);