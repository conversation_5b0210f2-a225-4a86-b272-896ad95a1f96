(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1064efd1"],{"70a7":function(t,s,i){"use strict";i.r(s);var a=function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticStyle:{"font-size":"16px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"page_title"},[t._v("小程序下载")])]),t._v(" "),i("div",[i("div",{staticClass:"flex"},[i("div",{staticClass:"ml-100 flex-1"},[i("div",{staticClass:"header_title"},[t._v("小程序设置")]),t._v(" "),i("div",{staticClass:"content-box"},[i("div",{staticClass:"left"},[t._v("小程序名称：")]),t._v(" "),i("div",{staticClass:"right"},[t._v("一码秦川")])]),t._v(" "),i("div",{staticClass:"content-box"},[i("div",{staticClass:"left"},[t._v("小程序包：")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(s){return t.downCode()}}},[t._v("下载小程序")])],1)])])])])],1)},e=[],n=i("ffd2"),c={data:function(){return{}},methods:{downCode:function(){Object(n["g"])().then((function(t){window.open(t)}))}}},l=c,o=(i("f349"),i("2877")),d=Object(o["a"])(l,a,e,!1,null,null,null);s["default"]=d.exports},"8728b":function(t,s,i){},f349:function(t,s,i){"use strict";i("8728b")}}]);