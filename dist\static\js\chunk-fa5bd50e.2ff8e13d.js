(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fa5bd50e"],{"634a":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[e.checkPermi(["platform:order:status:num"])?a("el-form-item",{attrs:{label:"订单状态："}},[a("el-radio-group",{attrs:{type:"button"},on:{change:e.seachList},model:{value:e.tableFrom.status,callback:function(t){e.$set(e.tableFrom,"status",t)},expression:"tableFrom.status"}},[a("el-radio-button",{attrs:{label:"all"}},[e._v("全部 "+e._s(e.orderChartType.all))]),e._v(" "),a("el-radio-button",{attrs:{label:"unPaid"}},[e._v("未支付 "+e._s(e.orderChartType.unPaid))]),e._v(" "),a("el-radio-button",{attrs:{label:"notShipped"}},[e._v("未发货 "+e._s(e.orderChartType.notShipped))]),e._v(" "),a("el-radio-button",{attrs:{label:"spike"}},[e._v("待收货 "+e._s(e.orderChartType.spike))]),e._v(" "),a("el-radio-button",{attrs:{label:"awaitVerification"}},[e._v("待核销\n                "+e._s(e.orderChartType.verification))]),e._v(" "),a("el-radio-button",{attrs:{label:"complete"}},[e._v("交易完成 "+e._s(e.orderChartType.complete))]),e._v(" "),a("el-radio-button",{attrs:{label:"refunded"}},[e._v("已退款 "+e._s(e.orderChartType.refunded))]),e._v(" "),a("el-radio-button",{attrs:{label:"deleted"}},[e._v("已删除 "+e._s(e.orderChartType.deleted))])],1)],1):e._e(),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(t){return e.selectChange(e.tableFrom.dateLimit)}},model:{value:e.tableFrom.dateLimit,callback:function(t){e.$set(e.tableFrom,"dateLimit",t)},expression:"tableFrom.dateLimit"}},e._l(e.fromList.fromTxt,(function(t,i){return a("el-radio-button",{key:i,attrs:{label:t.val}},[e._v(e._s(t.text))])})),1),e._v(" "),a("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"订单号："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单号",size:"small",clearable:""},model:{value:e.tableFrom.orderNo,callback:function(t){e.$set(e.tableFrom,"orderNo",t)},expression:"tableFrom.orderNo"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"商户名称："}},[a("merchant-name",{on:{getMerId:e.getMerId}})],1)],1)],1)])]),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:e.tableData.data,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"},"row-key":function(e){return e.orderNo}}},[e.checkedCities.includes("订单号")?a("el-table-column",{attrs:{label:"订单号","min-width":"210"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"acea-row"},[a("span",{directives:[{name:"show",rawName:"v-show",value:1===t.row.type,expression:"scope.row.type===1"}],staticClass:"iconfont icon-shipinhao mr5",staticStyle:{color:"#F6AE02"}}),e._v(" "),a("span",{class:parseInt(t.row.refundStatus)>0?"red":"",staticStyle:{display:"block"},domProps:{textContent:e._s(t.row.orderNo)}})]),e._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:parseInt(t.row.refundStatus)>0,expression:"parseInt(scope.row.refundStatus) > 0"}],staticStyle:{color:"#ed4014",display:"block"}},[e._v(e._s(e._f("orderRefundStatusFilter")(t.row.refundStatus)))]),e._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:t.row.isUserDel,expression:"scope.row.isUserDel"}],staticStyle:{color:"#ed4014",display:"block"}},[e._v("用户已删除")]),e._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:t.row.isMerchantDel,expression:"scope.row.isMerchantDel"}],staticStyle:{color:"#ed4014",display:"block"}},[e._v("商户已删除")])]}}],null,!1,636852489)}):e._e(),e._v(" "),e.checkedCities.includes("商户名称")?a("el-table-column",{attrs:{prop:"merName",label:"商户名称","min-width":"150"}}):e._e(),e._v(" "),e.checkedCities.includes("收货人")?a("el-table-column",{attrs:{prop:"nickName",label:"用户昵称","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:1==t.row.isLogoff?"red":""},[e._v(e._s(t.row.nickName))]),e._v(" "),1==t.row.isLogoff?a("span",{class:1==t.row.isLogoff?"red":""},[e._v("|")]):e._e(),e._v(" "),1==t.row.isLogoff?a("span",{staticClass:"red"},[e._v("(已注销)")]):e._e()]}}],null,!1,4076123740)}):e._e(),e._v(" "),e.checkedCities.includes("实际支付")?a("el-table-column",{attrs:{prop:"payPrice",label:"实际支付","min-width":"80"}}):e._e(),e._v(" "),e.checkedCities.includes("支付方式")?a("el-table-column",{attrs:{label:"支付方式","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("payTypeFilter")(t.row.payType)))])]}}],null,!1,2281245087)}):e._e(),e._v(" "),e.checkedCities.includes("订单状态")?a("el-table-column",{attrs:{label:"订单状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[3===t.row.refundStatus?a("span",{staticClass:"fontColor3"},[e._v("已退款")]):a("span",[e._v(e._s(e._f("orderStatusFilter")(t.row.status)))])]}}],null,!1,3658376581)}):e._e(),e._v(" "),e.checkedCities.includes("下单时间")?a("el-table-column",{attrs:{prop:"createTime",label:"下单时间","min-width":"140"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center","render-header":e.renderHeader},scopedSlots:e._u([{key:"default",fn:function(t){return[e.checkPermi(["platform:order:info"])?a("el-button",{staticClass:"mr10",attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderDetails(t.row.orderNo)}}},[e._v("订单详情")]):e._e()]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.card_select_show,expression:"card_select_show"}],staticClass:"card_abs"},[[a("div",{staticClass:"cell_ht"},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.checkSave()}}},[e._v("保存")])],1),e._v(" "),a("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.checkedCities,callback:function(t){e.checkedCities=t},expression:"checkedCities"}},e._l(e.columnData,(function(t){return a("el-checkbox",{key:t,staticClass:"check_cell",attrs:{label:t}},[e._v(e._s(t))])})),1)]],2),e._v(" "),a("details-from",{ref:"orderDetail",attrs:{orderNo:e.orderNo}})],1)},s=[],l=a("f8b7"),r=a("7ff6"),o=a("6ed5"),n=(a("6537"),a("ed08")),c=a("c4c8"),d=a("e350"),h={name:"orderlistDetails",components:{detailsFrom:r["a"],merchantName:o["a"]},data:function(){return{options:[{value:0,label:"全部"},{value:2,label:"商城订单"},{value:1,label:"视频号订单"}],RefuseVisible:!1,RefuseData:{},orderNo:"",refundVisible:!1,refundData:{},dialogVisibleJI:!1,tableDataLog:{data:[],total:0},tableFromLog:{page:1,limit:10,orderNo:0},LogLoading:!1,isCreate:1,editData:null,dialogVisible:!1,tableData:{data:[],total:0},listLoading:!0,tableFrom:{status:"all",dateLimit:"",orderNo:"",page:1,limit:20,merId:0},orderChartType:{},timeVal:[],fromList:this.$constants.fromList,fromType:[{value:"all",text:"全部"},{value:"info",text:"普通"},{value:"pintuan",text:"拼团"},{value:"bragin",text:"砍价"},{value:"miaosha",text:"秒杀"}],selectionList:[],ids:"",orderids:"",cardLists:[],isWriteOff:Object(n["e"])(),proType:0,active:!1,card_select_show:!1,checkAll:!1,checkedCities:["订单号","订单类型","商户名称","收货人","商品名称","实际支付","支付方式","订单状态","下单时间"],columnData:["订单号","订单类型","商户名称","收货人","商品名称","实际支付","支付方式","订单状态","下单时间"],isIndeterminate:!0}},mounted:function(){this.getList(),this.getOrderStatusNum()},methods:{checkPermi:d["a"],getMerId:function(e){this.tableFrom.merId=e,this.seachList()},resetFormRefundhandler:function(){this.refundVisible=!1},resetFormRefusehand:function(){this.RefuseVisible=!1},resetForm:function(e){this.dialogVisible=!1},seachList:function(){this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onOrderDetails:function(e){this.orderNo=e,this.$refs.orderDetail.getDetail(e),this.$refs.orderDetail.getOrderInvoiceList(e),this.$refs.orderDetail.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},selectChange:function(e){this.timeVal=[],this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onchangeTime:function(e){this.timeVal=e,this.tableFrom.dateLimit=e?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},getList:function(){var e=this;this.listLoading=!0,Object(l["d"])(this.tableFrom).then((function(t){e.tableData.data=t.list||[],e.tableData.total=t.total,e.listLoading=!1,e.checkedCities=e.$cache.local.has("order_stroge")?e.$cache.local.getJSON("order_stroge"):e.checkedCities})).catch((function(){e.listLoading=!1}))},getOrderStatusNum:function(){var e=this;Object(l["f"])({dateLimit:this.tableFrom.dateLimit,type:this.tableFrom.type}).then((function(t){e.orderChartType=t}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},exports:function(){var e={dateLimit:this.tableFrom.dateLimit,orderNo:this.tableFrom.orderNo,status:this.tableFrom.status,type:this.tableFrom.type};Object(c["m"])(e).then((function(e){window.open(e.fileName)}))},renderHeader:function(e){var t=this;return e("p",[e("span",{style:"padding-right:5px;"},["操作"]),e("i",{class:"el-icon-setting",on:{click:function(){return t.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(e){this.checkedCities=e?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.columnData.length,this.isIndeterminate=t>0&&t<this.columnData.length},checkSave:function(){this.card_select_show=!1,this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("order_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)}}},u=h,m=(a("acc7"),a("2877")),p=Object(m["a"])(u,i,s,!1,null,"d463e286",null);t["default"]=p.exports},6537:function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var i=a("b775");function s(e){return Object(i["a"])({url:"/admin/system/store/staff/list",method:"get",params:e})}},"75b6":function(e,t,a){},acc7:function(e,t,a){"use strict";a("75b6")}}]);