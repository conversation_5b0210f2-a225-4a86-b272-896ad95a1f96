<template>
  <div class="divBox">
    <el-row :gutter="20">
      <el-col
        style="min-width: 122px;max-width:11.11%"
        v-for="(item, index) in permList"
        :key="index"
        class="ivu-mb"
      >
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="nav_item" @click="navigatorTo(item.url)">
            <div class="pic_badge">
              <span class="iconfont" :class="item.icon" :style="{ color: item.bgColor }"></span>
            </div>
            <p class="text-14">{{ item.title }}</p>
          </div>
        </el-card>
      </el-col>
      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="12">
        <el-card class="box-card">
          <div class="header_title">经营数据</div>
          <div class="nav_grid">
            <div
              class="nav_grid_item"
              v-for="(item, index) in businessList"
              :key="index"
              @click="navigatorTo(item.path)"
            >
              <p class="num_data">{{ item.num || 0 }}</p>
              <p class="label">{{ item.title || 0 }}</p>
            </div>
          </div>
        </el-card>
      </el-col> -->
    </el-row>
  </div>
</template>
<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import echartsNew from '@/components/echartsNew/index';
import { businessData } from '@/api/dashboard';
import { checkPermi } from '@/utils/permission'; // 权限判断函数
export default {
  components: {
    echartsNew,
  },
  data() {
    return {
      nav_list: [
        {
          bgColor: '#EF9C20',
          icon: 'icon-yonghuguanli',
          title: '用户管理',
          url: '/user/index',
        },
        {
          bgColor: '#1890FF',
          icon: 'icon-shangpinguanli',
          title: '商品管理',
          url: '/product/list',
        },
        {
          bgColor: '#4BCAD5',
          icon: 'icon-shanghuguanli',
          title: '商户管理',
          url: '/merchant/list',
        },
        {
          bgColor: '#A277FF',
          icon: 'icon-a-dingdanguanli1',
          title: '订单管理',
          url: '/order/list',
        },
        {
          bgColor: '#1BBE6B',
          icon: 'icon-xitongshezhi',
          title: '系统设置',
          url: '/operation/setting',
        },
        {
          bgColor: '#1890FF',
          icon: 'icon-fenxiaoshezhi',
          title: '分销设置',
          url: '/distribution/distributionconfig',
        },
        {
          bgColor: '#A277FF',
          icon: 'icon-caiwuguanli',
          title: '财务管理',
          url: '/finance/capitalFlow',
        },

        {
          bgColor: '#EF9C20',
          icon: 'icon-yihaotong',
          title: '一号通',
          url: '/onePass/index',
        },
        {
          bgColor: '#4BCAD5',
          icon: 'icon-qiandaopeizhi',
          title: '签到配置',
          url: '/marketing/sign/config',
        },
      ],
      statisticData: [
        { title: '待审核商品数量', num: 0, path: '/product/list' },
        { title: '待核销订单数量', num: 0, path: '/order/list' },
        { title: '待发货订单数量', num: 0, path: '/order/list' },
        { title: '在售商品数量', num: 0, path: '/product/list' },
        { title: '待退款订单数量', num: 0, path: '/order/refund' },
      ],
      optionData: {},
      applyNum: 0,
      style: { height: '250px' },
    };
  },
  computed: {
    //鉴权处理
    permList: function () {
      let arr = [];
      this.nav_list.forEach((item) => {
        //if (this.checkPermi(item.perms)) {
        arr.push(item);
        //}
      });
      return arr;
    },
    // businessList: function () {
    //   let arr = [];
    //   this.statisticData.forEach((item) => {
    //     //if (this.checkPermi(item.perms)) {
    //     arr.push(item);
    //     //}
    //   });
    //   return arr;
    // },
  },
  mounted() {
    // this.getbusinessData();
  },
  methods: {
    checkPermi,
    navigatorTo(path) {
      console.log(path, 'path');
      this.$router.push(path);
    },
    // getbusinessData() {
    //   businessData().then((res) => {
    //     this.statisticData[0].num = res.notShippingOrderNum; //待审核商品数量
    //     this.statisticData[1].num = res.awaitAuditProductNum; //待核销订单数量
    //     this.statisticData[2].num = res.notShippingOrderNum; //待发货订单数量
    //     this.statisticData[3].num = res.onSaleProductNum; //在售商品数量
    //     this.statisticData[4].num = res.refundingOrderNum; //待退款订单数量
    //   });
    // },
  },
};
</script>
<style lang="scss" scoped>
.ivu-mb {
  // margin-bottom: 10px;
  cursor: pointer;
}
.divBox {
  padding: 0 20px !important;
}

.dashboard-console-grid {
  text-align: center;
  .ivu-card-body {
    padding: 0;
  }
  i {
    font-size: 32px;
  }
  a {
    display: block;
    color: inherit;
  }
  p {
    margin-top: 8px;
  }
}
.mb20 {
  margin-bottom: 20px;
}
.header_title {
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #000000;
  padding-left: 8px;
  position: relative;
  &::before {
    position: absolute;
    content: '';
    width: 2px;
    height: 18px;
    background: #1890ff;
    top: 0;
    left: 0;
  }
}
.nav_grid {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}
.nav_grid_item {
  width: 11.11%;
  // height: 90px;
  // margin-top: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  img {
    width: 58px;
    height: 58px;
  }
  .pic_badge {
    width: 58px;
    height: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 4px;
    // margin-bottom: 10px;
    .iconfont {
      font-size: 30px;
    }
  }
  p {
    height: 17px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #000000;
    line-height: 17px;
    margin-top: 12px;
  }
  .num_data {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 18px;
  }
  .label {
    font-size: 14px;
    color: #666666;
    text-align: center;
  }
}
.el-ccard {
  width: 100% !important;
}
::v-deep .el-row {
  padding: 0 !important;
}
.pic_badge {
  // width: 58px;
  // height: 58px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 4px;
  margin-bottom: 13px;
  .iconfont {
    font-size: 30px;
  }
}
.nav_item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
// .el-col-lg-2-7 {
//   width: 11.11%;
// }
.text-14 {
  font-size: 14px;
  color: #333;
}
</style>
