(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6ceff8f7"],{"2f2c":function(e,t,a){"use strict";a.d(t,"a",(function(){return u})),a.d(t,"d",(function(){return m})),a.d(t,"e",(function(){return f})),a.d(t,"g",(function(){return d})),a.d(t,"f",(function(){return p})),a.d(t,"b",(function(){return b})),a.d(t,"c",(function(){return h}));var r=a("b775");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function i(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){n(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function n(e,t,a){return t=l(t),t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function l(e){var t=c(e,"string");return"symbol"===s(t)?t:String(t)}function c(e,t){if("object"!==s(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!==s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function u(){return Object(r["a"])({url:"/admin/platform/city/region/city/tree",method:"get"})}function m(e){return Object(r["a"])({url:"/admin/platform/express/list",method:"get",params:i({},e)})}function f(){return Object(r["a"])({url:"/admin/platform/express/sync/express",method:"post"})}function d(e){return Object(r["a"])({url:"/admin/platform/express/update/show",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/admin/platform/express/update",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/admin/express/delete",method:"GET",params:i({},e)})}function h(e){return Object(r["a"])({url:"admin/platform/express/info",method:"get",params:i({},e)})}},b443:function(e,t,a){},bd36:function(e,t,a){"use strict";a("b443")},fbfd:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form}},[a("el-form-item",{attrs:{label:"关键字："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入关键字",size:"small",clearable:""},model:{value:e.form.keywords,callback:function(t){e.$set(e.form,"keywords",t)},expression:"form.keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:express:list"],expression:"['platform:express:list']"}],attrs:{slot:"append",size:"small",icon:"el-icon-search"},on:{click:e.handlerSearch},slot:"append"})],1)],1)],1)],1),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:express:sync"],expression:"['platform:express:sync']"}],attrs:{type:"primary",size:"small"},on:{click:e.addExpress}},[e._v("同步物流公司")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableData.list,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"物流公司名称","min-width":"150",prop:"name"}}),e._v(" "),a("el-table-column",{attrs:{"min-width":"200",label:"编码",prop:"code"}}),e._v(" "),a("el-table-column",{attrs:{"min-width":"100",label:"排序",prop:"sort",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"是否显示","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.checkPermi(["platform:express:update:show"])?a("el-switch",{staticClass:"demo",attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},on:{change:function(a){return e.bindStatus(t.row)}},model:{value:t.row.isShow,callback:function(a){e.$set(t.row,"isShow",a)},expression:"scope.row.isShow"}}):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"address",fixed:"right","min-width":"120",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:express:info"],expression:"['platform:express:info']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.bindEdit(t.row)}}},[e._v("编辑")])]}}])})],1),e._v("`\n    "),a("div",{staticClass:"block-pagination"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableData.limit,"current-page":e.tableData.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"current-change":e.pageChange,"size-change":e.handleSizeChange}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"编辑物流公司",visible:e.dialogVisible,width:"700px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"formData",staticClass:"demo-ruleForm",attrs:{model:e.formData,rules:e.rules,"label-width":"100px"}},[e.formData.partnerId?a("el-form-item",{attrs:{label:"月结账号",prop:"account"}},[a("el-input",{attrs:{placeholder:"请输入月结账号"},model:{value:e.formData.account,callback:function(t){e.$set(e.formData,"account",t)},expression:"formData.account"}})],1):e._e(),e._v(" "),e.formData.partnerKey?a("el-form-item",{attrs:{label:"月结密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入月结密码"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1):e._e(),e._v(" "),e.formData.net?a("el-form-item",{attrs:{label:"网点名称",prop:"netName"}},[a("el-input",{attrs:{placeholder:"请输入网点名称"},model:{value:e.formData.netName,callback:function(t){e.$set(e.formData,"netName",t)},expression:"formData.netName"}})],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"排序",prop:"sort"}},[a("el-input-number",{attrs:{min:0,max:9999,label:"排序"},model:{value:e.formData.sort,callback:function(t){e.$set(e.formData,"sort",t)},expression:"formData.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"是否启用",prop:"status"}},[a("el-radio-group",{model:{value:e.formData.status,callback:function(t){e.$set(e.formData,"status",t)},expression:"formData.status"}},[a("el-radio",{attrs:{label:!1}},[e._v("关闭")]),e._v(" "),a("el-radio",{attrs:{label:!0}},[e._v("开启")])],1)],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:express:update"],expression:"['platform:express:update']"}],attrs:{type:"primary"},on:{click:function(t){return e.submit("formData")}}},[e._v("确 定")])],1)],1)],1)},s=[],o=a("3fbe"),i=(a("92c6"),a("2f2c")),n=a("e350"),l=a("61f7"),c={name:"CompanyList",components:{parser:o["a"]},data:function(){return{constants:this.$constants,formConf:{fields:[]},form:{keywords:""},tableData:{},page:1,limit:20,loading:!1,dialogVisible:!1,fromType:"add",formData:{status:!1},isCreate:0,formShow:!1,editId:0,rules:{sort:[{required:!0,message:"请输入排序",trigger:"blur"}],account:[{required:!0,message:"请输入月结账号",trigger:"blur"}],password:[{required:!0,message:"请输入月结密码",trigger:"blur"}],netName:[{required:!0,message:"请输入网点名称",trigger:"blur"}]}}},created:function(){this.getExpressList()},methods:{checkPermi:n["a"],handlerSearch:function(){this.page=1,this.getExpressList()},getExpressList:function(){var e=this;this.loading=!0,i["d"]({page:this.page,limit:this.limit,keywords:this.form.keywords}).then((function(t){e.loading=!1,e.tableData=t})).catch((function(){e.loading=!1}))},bindStatus:function(e){var t=this;i["g"]({account:e.account,code:e.code,id:e.id,isShow:e.isShow,name:e.name,sort:e.sort}).then((function(e){t.$message.success("操作成功")})).catch((function(){e.isShow=!e.isShow}))},pageChange:function(e){this.page=e,this.getExpressList()},handleSizeChange:function(e){this.limit=e,this.getExpressList()},addExpress:function(){var e=this;i["e"]().then((function(t){e.page=1,e.getExpressList()}))},bindDelete:function(e){var t=this;this.$modalSure().then((function(){i["b"]({id:e.id}).then((function(e){t.$message.success("删除成功"),t.getExpressList()}))})).catch((function(){}))},submit:Object(l["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;i["f"](t.formData).then((function(e){t.$message.success("操作成功"),t.handleClose(),t.getExpressList()}))}))})),handleClose:function(e){this.formShow=!1,this.formConf.fields=[],this.dialogVisible=!1,this.isCreate=0},bindEdit:function(e){var t=this;this.dialogVisible=!0,this.editId=e.id,i["c"]({id:e.id}).then((function(e){t.formData=e}))}}},u=c,m=(a("bd36"),a("2877")),f=Object(m["a"])(u,r,s,!1,null,"4e0b52c1",null);t["default"]=f.exports}}]);