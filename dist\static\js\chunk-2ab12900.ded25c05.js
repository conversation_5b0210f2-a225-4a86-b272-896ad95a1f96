(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ab12900"],{"0c19":function(t,e,r){"use strict";r("b6fa")},3124:function(t,e,r){},5724:function(t,e,r){"use strict";r("94fd")},"937b":function(t,e,r){"use strict";r("d523")},9406:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[t.checkPermi(["admin:statistics:home:index"])?r("base-info",{ref:"baseInfo"}):t._e(),t._v(" "),r("grid-menu",{staticClass:"mb20"}),t._v(" "),r("user-overview")],1)},i=[],a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox",staticStyle:{"padding-bottom":"0"}},[r("el-row",{staticClass:"baseInfo",attrs:{gutter:20}},[r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("总数据")])])]),t._v(" "),t.viewData?r("div",{staticClass:"container"},[r("div",{staticClass:"con_left"},[r("span",{staticClass:"content-number spBlock m-b-15"},[t._v(t._s(t.viewData.userNum||0))]),t._v(" "),r("span",{staticClass:"main_tit"},[t._v("全部用户")])]),t._v(" "),r("div",{staticClass:"line"}),t._v(" "),r("div",{staticClass:"con_right"},[r("span",{staticClass:"content-number spBlock m-b-15"},[t._v(t._s(t.viewData.merchantNum||0))]),t._v(" "),r("span",{staticClass:"main_tit"},[t._v("全部商户")])])]):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("今日新增商户数")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.todayNewUserNum||0))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayNewMerchantNum||0)+" 人")])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("今日新增用户数")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.todayNewUserNum||0))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayNewUserNum||0)+" 人")])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("订单量")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.orderNum||0))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayOrderNum||0)+"单")])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("用户访问量")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.pageviews||0))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayPageviews||0))])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("销售额")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.sales))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdaySales||0)+" 元")])])],1):t._e()])],1)],1)],1)},o=[],s=r("b775");function c(){return Object(s["a"])({url:"/admin/platform/statistics/home/<USER>",method:"GET"})}function l(){return Object(s["a"])({url:"/admin/statistics/home/<USER>/user",method:"get"})}function u(){return Object(s["a"])({url:"/admin/statistics/home/<USER>/order",method:"get"})}function h(){return Object(s["a"])({url:"/admin/statistics/home/<USER>/order/month",method:"get"})}function f(){return Object(s["a"])({url:"/admin/statistics/home/<USER>/order/week",method:"get"})}function d(){return Object(s["a"])({url:"/admin/statistics/home/<USER>/order/year",method:"get"})}function p(){return Object(s["a"])({url:"/admin/platform/statistics/home/<USER>/data",method:"get"})}function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(k){c=function(t,e,r){return t[e]=r}}function l(t,e,r,i){var a=e&&e.prototype instanceof f?e:f,o=Object.create(a.prototype),s=new S(i||[]);return n(o,"_invoke",{value:C(t,r,s)}),o}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(k){return{type:"throw",arg:k}}}t.wrap=l;var h={};function f(){}function d(){}function p(){}var y={};c(y,a,(function(){return this}));var g=Object.getPrototypeOf,w=g&&g(g(O([])));w&&w!==e&&r.call(w,a)&&(y=w);var b=p.prototype=f.prototype=Object.create(y);function _(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function i(n,a,o,s){var c=u(t[n],t,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==v(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(h).then((function(t){l.value=t,o(l)}),(function(t){return i("throw",t,o,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function C(t,e,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return F()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=L(o,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=u(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function L(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,L(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var i=u(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:F}}function F(){return{value:void 0,done:!0}}return d.prototype=p,n(b,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:d,configurable:!0}),d.displayName=c(p,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},_(x.prototype),c(x.prototype,o,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,i,a){void 0===a&&(a=Promise);var o=new x(l(e,r,n,i),a);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},_(b),c(b,s,"Generator"),c(b,a,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(D),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return o.type="throw",o.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;D(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}function y(t,e,r,n,i,a,o){try{var s=t[a](o),c=s.value}catch(l){return void r(l)}s.done?e(c):Promise.resolve(c).then(n,i)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){y(a,n,i,o,s,"next",t)}function s(t){y(a,n,i,o,s,"throw",t)}o(void 0)}))}}var w={data:function(){return{grid:{xl:6,lg:8,md:12,sm:12,xs:24},viewData:{}}},methods:{statisticsOrder:function(){var t=this;c().then(function(){var e=g(m().mark((function e(r){return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.viewData=r;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}},mounted:function(){this.statisticsOrder()}},b=w,_=(r("e56e"),r("2877")),x=Object(_["a"])(b,a,o,!1,null,"08c27684",null),C=x.exports,L=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-row",{attrs:{gutter:20}},t._l(t.permList,(function(e,n){return r("el-col",{key:n,staticClass:"ivu-mb",staticStyle:{"min-width":"122px","max-width":"11.11%"}},[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"nav_item",on:{click:function(r){return t.navigatorTo(e.url)}}},[r("div",{staticClass:"pic_badge"},[r("span",{staticClass:"iconfont",class:e.icon,style:{color:e.bgColor}})]),t._v(" "),r("p",{staticClass:"text-14"},[t._v(t._s(e.title))])])])],1)})),1)],1)},E=[],D=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{style:t.styles,attrs:{id:t.echarts}})])},S=[],O=r("313e"),F=r.n(O),k={name:"Index",props:{styles:{type:Object,default:null},optionData:{type:Object,default:null}},data:function(){return{myChart:null}},computed:{echarts:function(){return"echarts"+Math.ceil(100*Math.random())}},watch:{optionData:{handler:function(t,e){this.handleSetVisitChart()},deep:!0}},mounted:function(){var t=this,e=this;e.$nextTick((function(){e.handleSetVisitChart(),window.addEventListener("resize",t.wsFunc)}))},beforeDestroy:function(){window.removeEventListener("resize",this.wsFunc),this.myChart&&(this.myChart.dispose(),this.myChart=null)},methods:{wsFunc:function(){this.myChart.resize()},handleSetVisitChart:function(){this.myChart=F.a.init(document.getElementById(this.echarts));var t=null;t=this.optionData,this.myChart.setOption(t,!0)}}},A=k,j=Object(_["a"])(A,D,S,!1,null,"2895e388",null),N=j.exports,P=r("e350"),B={components:{echartsNew:N},data:function(){return{nav_list:[{bgColor:"#EF9C20",icon:"icon-yonghuguanli",title:"用户管理",url:"/user/index"},{bgColor:"#1890FF",icon:"icon-shangpinguanli",title:"商品管理",url:"/product/list"},{bgColor:"#4BCAD5",icon:"icon-shanghuguanli",title:"商户管理",url:"/merchant/list"},{bgColor:"#A277FF",icon:"icon-a-dingdanguanli1",title:"订单管理",url:"/order/list"},{bgColor:"#1BBE6B",icon:"icon-xitongshezhi",title:"系统设置",url:"/operation/setting"},{bgColor:"#1890FF",icon:"icon-fenxiaoshezhi",title:"分销设置",url:"/distribution/distributionconfig"},{bgColor:"#A277FF",icon:"icon-caiwuguanli",title:"财务管理",url:"/finance/capitalFlow"},{bgColor:"#EF9C20",icon:"icon-yihaotong",title:"一号通",url:"/onePass/index"},{bgColor:"#4BCAD5",icon:"icon-qiandaopeizhi",title:"签到配置",url:"/marketing/sign/config"}],statisticData:[{title:"待审核商品数量",num:0,path:"/product/list"},{title:"待核销订单数量",num:0,path:"/order/list"},{title:"待发货订单数量",num:0,path:"/order/list"},{title:"在售商品数量",num:0,path:"/product/list"},{title:"待退款订单数量",num:0,path:"/order/refund"}],optionData:{},applyNum:0,style:{height:"250px"}}},computed:{permList:function(){var t=[];return this.nav_list.forEach((function(e){t.push(e)})),t}},mounted:function(){},methods:{checkPermi:P["a"],navigatorTo:function(t){console.log(t,"path"),this.$router.push(t)}}},T=B,I=(r("5724"),Object(_["a"])(T,L,E,!1,null,"4e6fb28c",null)),G=I.exports,V=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{san:"24"}},[r("el-card",{staticClass:"dashboard-console-visit",attrs:{bordered:!1}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row row-middle"},[r("div",{staticClass:"header_title"},[t._v("订单统计")])]),t._v(" "),r("div",{staticClass:"checkTime"},[r("el-radio-group",{staticClass:"ivu-mr-8",on:{change:t.radioChange},model:{value:t.visitDate,callback:function(e){t.visitDate=e},expression:"visitDate"}},[r("el-radio-button",{attrs:{label:"last30"}},[t._v("30天")]),t._v(" "),r("el-radio-button",{attrs:{label:"week"}},[t._v("周")]),t._v(" "),r("el-radio-button",{attrs:{label:"month"}},[t._v("月")]),t._v(" "),r("el-radio-button",{attrs:{label:"year"}},[t._v("年")])],1)],1)])]),t._v(" "),r("h4",[t._v("订单量趋势")]),t._v(" "),t.info?r("echarts-from",{ref:"visitChart",attrs:{yAxisData:t.yAxisData,seriesData:t.series,xAxis:t.xAxis,legendData:t.legendData}}):t._e()],1)],1)],1)],1)},$=[],z=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{style:t.styles,attrs:{id:t.echarts}})])},q=[],M={name:"index",props:{seriesData:{type:Array,default:function(){return[]}},xAxis:{type:Array,default:function(){return[]}},echartsTitle:{type:String,default:""},yAxisData:{type:Array,default:function(){return[]}},legendData:{type:Array,default:function(){return[]}}},data:function(){return{styles:"height:300px",infoLists:this.infoList,seriesArray:this.seriesData}},watch:{seriesData:{handler:function(t,e){this.seriesArray=t,this.handleSetVisitChart()},deep:!0}},computed:{echarts:function(){return"echarts"+Math.ceil(100*Math.random())}},mounted:function(){var t=this,e=this;e.$nextTick((function(){e.handleSetVisitChart(),window.addEventListener("resize",t.wsFunc)}))},methods:{wsFunc:function(){this.myChart.resize()},handleSetVisitChart:function(){this.myChart=F.a.init(document.getElementById(this.echarts));var t=null;t="circle"===this.echartsTitle?{tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right",data:this.legendData||[]},series:[{name:"访问来源",type:"pie",radius:"70%",center:["50%","60%"],data:this.seriesArray||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}:{tooltip:{trigger:"axis"},toolbox:{},legend:{data:this.legendData||[]},color:["#1495EB","#00CC66","#F9D249","#ff9900","#9860DF"],grid:{left:16,right:25,bottom:10,top:40,containLabel:!0},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#D7DDE4"}},axisTick:{alignWithLabel:!0,lineStyle:{color:"#D7DDE4"}},splitLine:{show:!1,lineStyle:{color:"#F5F7F9"}},axisLabel:{interval:0,rotate:40,textStyle:{color:"#7F8B9C"}},data:this.xAxis}],yAxis:this.yAxisData.length?this.yAxisData:{axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}},type:"value"},series:this.seriesArray},this.myChart.setOption(t,!0)},handleResize:function(){this.myChart.resize()}},beforeDestroy:function(){window.removeEventListener("resize",this.wsFunc),this.myChart&&(this.myChart.dispose(),this.myChart=null)}},U=M,Q=Object(_["a"])(U,z,q,!1,null,"018a888b",null),Y=Q.exports;function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function W(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */W=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(O){c=function(t,e,r){return t[e]=r}}function l(t,e,r,i){var a=e&&e.prototype instanceof f?e:f,o=Object.create(a.prototype),s=new E(i||[]);return n(o,"_invoke",{value:_(t,r,s)}),o}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(O){return{type:"throw",arg:O}}}t.wrap=l;var h={};function f(){}function d(){}function p(){}var v={};c(v,a,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(D([])));y&&y!==e&&r.call(y,a)&&(v=y);var g=p.prototype=f.prototype=Object.create(v);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,a,o,s){var c=u(t[n],t,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==R(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(h).then((function(t){l.value=t,o(l)}),(function(t){return i("throw",t,o,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function _(t,e,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return S()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=x(o,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=u(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function x(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var i=u(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function D(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=p,n(g,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:d,configurable:!0}),d.displayName=c(p,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,s,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},w(b.prototype),c(b.prototype,o,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,a){void 0===a&&(a=Promise);var o=new b(l(e,r,n,i),a);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},w(g),c(g,s,"Generator"),c(g,a,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=D,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return o.type="throw",o.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;L(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:D(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}function H(t,e,r,n,i,a,o){try{var s=t[a](o),c=s.value}catch(l){return void r(l)}s.done?e(c):Promise.resolve(c).then(n,i)}function J(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){H(a,n,i,o,s,"next",t)}function s(t){H(a,n,i,o,s,"throw",t)}o(void 0)}))}}var X={components:{echartsFrom:Y},data:function(){return{infoList:null,visitDate:"last30",series:[],xAxis:[],info:{},legendData:[],yAxisData:[]}},mounted:function(){this.yAxisData=[{type:"value",name:"金额",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},{type:"value",name:"数量",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}}]},methods:{checkPermi:P["a"],radioChange:function(t){switch(t){case"week":this.handleChangeWeek();break;case"month":this.handleChangeMonth();break;case"year":this.handleChangeYear();break;default:this.handleChangeVisitType();break}},handleChangeVisitType:function(){var t=this;this.xAxis=[],this.legendData=[],u().then(function(){var e=J(W().mark((function e(r){var n,i,a,o;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a in t.info=r,n=[],i=[],r.price)n.push(Number(r.price[a])),t.xAxis.push(a);for(o in r.quality)i.push(Number(r.quality[o]));t.legendData=["订单金额","订单数"],t.series=[{name:"订单金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:n},{name:"订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#4BCAD5"}},yAxisIndex:1,data:i}];case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleChangeWeek:function(){var t=this;this.xAxis=[],this.legendData=[],f().then(function(){var e=J(W().mark((function e(r){var n,i,a,o,s,c,l,u;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(s in t.info=r,t.legendData=["上周金额","本周金额","上周订单数","本周订单数"],n=[],i=[],a=[],o=[],r.prePrice)n.push(Number(r.prePrice[s])),t.xAxis.push(s);for(c in r.price)i.push(Number(r.price[c]));for(l in r.preQuality)o.push(Number(r.preQuality[l]));for(u in r.quality)a.push(Number(r.quality[u]));t.series=[{name:"上周金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:n},{name:"本周金额",type:"bar",itemStyle:{normal:{color:"#4BCAD5"}},data:i},{name:"上周订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#E6A23C"}},yAxisIndex:1,data:o},{name:"本周订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#768A9C"}},yAxisIndex:1,data:a}];case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleChangeMonth:function(){var t=this;this.xAxis=[],this.legendData=[],h().then(function(){var e=J(W().mark((function e(r){var n,i,a,o,s,c,l,u;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(s in t.info=r,t.legendData=["上月金额","本月金额","上月订单数","本月订单数"],n=[],i=[],a=[],o=[],r.prePrice)n.push(Number(r.prePrice[s])),t.xAxis.push(s);for(c in r.price)i.push(Number(r.price[c]));for(l in r.preQuality)o.push(Number(r.preQuality[l]));for(u in r.quality)a.push(Number(r.quality[u]));t.series=[{name:"上月金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:n},{name:"本月金额",type:"bar",itemStyle:{normal:{color:"#4BCAD5"}},data:i},{name:"上月订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#E6A23C"}},yAxisIndex:1,data:o},{name:"本月订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#768A9C"}},yAxisIndex:1,data:a}];case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleChangeYear:function(){var t=this;this.xAxis=[],this.legendData=[],d().then(function(){var e=J(W().mark((function e(r){var n,i,a,o,s,c,l,u;return W().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(s in t.info=r,t.legendData=["去年金额","今年金额","去年订单数","今年订单数"],n=[],i=[],a=[],o=[],r.prePrice)n.push(Number(r.prePrice[s])),t.xAxis.push(s);for(c in r.price)i.push(Number(r.price[c]));for(l in r.preQuality)o.push(Number(r.preQuality[l]));for(u in r.quality)a.push(Number(r.quality[u]));t.series=[{name:"去年金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:n},{name:"今年金额",type:"bar",itemStyle:{normal:{color:"#4BCAD5"}},data:i},{name:"去年订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#E6A23C"}},yAxisIndex:1,data:o},{name:"今年订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#768A9C"}},yAxisIndex:1,data:a}];case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleResize:function(){this.infoList&&this.$refs.visitChart.handleResize()}},created:function(){}},K=X,Z=(r("0c19"),Object(_["a"])(K,V,$,!1,null,"2c387ac6",null)),tt=Z.exports,et=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-row",{attrs:{gutter:24}},[r("el-col",{staticClass:"ivu-mb mb10 dashboard-console-visit"},[r("el-card",{attrs:{bordered:!1,"dis-hover":""}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"acea-row row-middle"},[r("div",{staticClass:"header_title"},[t._v("用户统计")])])]),t._v(" "),t.infoList?r("echarts-from",{ref:"userChart",attrs:{echartsTitle:t.line,xAxis:t.xAxis,seriesData:t.series}}):t._e()],1)],1)],1)],1)},rt=[];function nt(t){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nt(t)}function it(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */it=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(O){c=function(t,e,r){return t[e]=r}}function l(t,e,r,i){var a=e&&e.prototype instanceof f?e:f,o=Object.create(a.prototype),s=new E(i||[]);return n(o,"_invoke",{value:_(t,r,s)}),o}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(O){return{type:"throw",arg:O}}}t.wrap=l;var h={};function f(){}function d(){}function p(){}var v={};c(v,a,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(D([])));y&&y!==e&&r.call(y,a)&&(v=y);var g=p.prototype=f.prototype=Object.create(v);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function i(n,a,o,s){var c=u(t[n],t,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==nt(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(h).then((function(t){l.value=t,o(l)}),(function(t){return i("throw",t,o,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function _(t,e,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return S()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=x(o,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=u(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function x(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var i=u(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function D(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=p,n(g,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:d,configurable:!0}),d.displayName=c(p,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,s,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},w(b.prototype),c(b.prototype,o,(function(){return this})),t.AsyncIterator=b,t.async=function(e,r,n,i,a){void 0===a&&(a=Promise);var o=new b(l(e,r,n,i),a);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},w(g),c(g,s,"Generator"),c(g,a,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=D,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return o.type="throw",o.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;L(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:D(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}function at(t,e,r,n,i,a,o){try{var s=t[a](o),c=s.value}catch(l){return void r(l)}s.done?e(c):Promise.resolve(c).then(n,i)}function ot(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){at(a,n,i,o,s,"next",t)}function s(t){at(a,n,i,o,s,"throw",t)}o(void 0)}))}}var st={name:"user-chart",components:{echartsFrom:Y},data:function(){return{line:"line",circle:"circle",xAxis:[],infoList:{},series:[],xData:[],y1Data:[],y2Data:[],lists:[],bing_data:[],bing_xdata:[],legendData:[],seriesUser:[],chartBuy:{}}},methods:{getStatistics:function(){var t=this;l().then(function(){var e=ot(it().mark((function e(r){var n,i;return it().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i in t.infoList=r,n=[],r)n.push(r[i]),t.xAxis.push(i);t.series=[{data:n,name:"人数（人）",type:"line",tooltip:!0,smooth:!0,symbol:"none",areaStyle:{normal:{opacity:.2}}}];case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleResize:function(){this.infoList&&0!==this.series.length&&this.$refs.userChart.handleResize(),this.infoList&&this.$refs.visitChart.handleResize()}},mounted:function(){},beforeDestroy:function(){this.visitChart&&(this.visitChart.dispose(),this.visitChart=null)}},ct=st,lt=(r("e5af"),Object(_["a"])(ct,et,rt,!1,null,"7162e402",null)),ut=lt.exports,ht=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox",staticStyle:{"padding-top":"0"}},[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{xs:24,sm:24,md:24,lg:16}},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"header_title"},[t._v("经营数据")]),t._v(" "),r("div",{staticClass:"nav_grid m-t-20"},t._l(t.businessList,(function(e,n){return r("div",{key:n,staticClass:"nav_grid_item",on:{click:function(r){return t.navigatorTo(e.path)}}},[r("div",{staticClass:"pic_badge",style:{background:e.bgColor}},[r("span",{staticClass:"iconfont",class:e.icon,style:{color:e.color}})]),t._v(" "),r("div",[r("p",{staticClass:"label"},[t._v(t._s(e.title||0))]),t._v(" "),r("p",{staticClass:"num_data"},[t._v(t._s(e.num||0))])])])})),0)])],1),t._v(" "),r("el-col",{attrs:{xs:24,sm:24,md:24,lg:8}},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"header_title"},[t._v("用户渠道比例")]),t._v(" "),t.optionData?r("echarts-new",{attrs:{"option-data":t.optionData,styles:t.style,height:"100%",width:"100%"}}):t._e()],1)],1)],1)],1)},ft=[];function dt(){return Object(s["a"])({url:"/admin/platform/statistics/home/<USER>",method:"GET"})}function pt(t){return Object(s["a"])({url:"/admin/statistics/user/overview",method:"GET",params:t})}var vt={data:function(){return{optionData:{},style:{height:"320px"},timeVal:[],dateLimit:"",list:[],fromList:this.$constants.timeList,userView:{},pickerOptions:this.$timeOptions,statisticData:[{icon:"icon-zaishoushangpin",title:"在售商品",num:0,path:"/product/list",color:"#1890FF",bgColor:"rgba(24, 144, 255, 0.08)"},{icon:"icon-daishenheshangpin",title:"待审核商品",num:0,path:"/product/list",color:"#A277FF",bgColor:"rgba(162, 119, 255, 0.08)"},{icon:"icon-daifahuo2",title:"待发货",num:0,path:"/order/list",color:"#1890FF",bgColor:"rgba(24, 144, 255, 0.08"},{icon:"icon-daihexiao",title:"待核销",num:0,path:"/order/list",color:"#1BBE6B",bgColor:"rgba(27, 190, 107, 0.08)"},{icon:"icon-daituikuan",title:"待退款",num:0,path:"/order/refund",color:"#EF9C20",bgColor:"rgba(239, 156, 32, 0.08)"}]}},components:{echartsNew:N},computed:{permList:function(){var t=[];return this.nav_list.forEach((function(e){t.push(e)})),t},businessList:function(){var t=[];return this.statisticData.forEach((function(e){t.push(e)})),t}},created:function(){var t=new Date,e=new Date;e.setTime(e.setTime(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()-29))),this.timeVal=[e,t]},mounted:function(){this.dateLimit="lately7",this.dateLimitPram="lately7",this.getChannel(),this.getbusinessData()},methods:{checkPermi:P["a"],navigatorTo:function(t){this.$router.push(t)},getbusinessData:function(){var t=this;p().then((function(e){t.statisticData[0].num=e.onSaleProductNum,t.statisticData[1].num=e.awaitAuditProductNum,t.statisticData[2].num=e.notShippingOrderNum,t.statisticData[3].num=e.awaitVerificationOrderNum,t.statisticData[4].num=e.refundingOrderNum}))},onchangeTime:function(t){this.timeVal=t,this.dateLimit=t?this.timeVal.join(","):"",this.dateLimitPram=t?this.timeVal.join(","):""},onSeach:function(){this.getUserView()},exports:function(){},selectChange:function(t){""==t?(this.$set(this,"dateLimitPram","yesterday"),this.getUserView()):(this.dateLimitPram=t,this.getUserView())},getUserView:function(){var t=this;pt({dateLimit:this.dateLimitPram}).then((function(e){t.userView=e}))},getChannel:function(){var t=this;dt().then((function(e){var r=e.data;r=[{name:"H5",value:0,channel:"h5"},{name:"小程序",value:0,channel:"routine"},{name:"公众号",value:0,channel:"wechat"},{name:"ios",value:0,channel:"ios"},{name:"微信ios",value:0,channel:"iosWx"},{name:"微信安卓",value:0,channel:"androidWx"}];var n=new Array;r.forEach((function(t){e.forEach((function(e){t.channel==e.registerType&&n.push({name:t.name,value:e.num?e.num:0})}))})),t.optionData={tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left",top:"center"},series:[{name:"访问来源",type:"pie",radius:["30%","50%"],avoidLabelOverlap:!1,label:{show:!0,position:"inside",color:"#fff",fontSize:"12",normal:{position:"inside",formatter:function(t){return parseInt(t.percent).toFixed(0)+"%"}}},itemStyle:{emphasis:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},normal:{color:function(t){var e=["#1BBE6B","#1890FF","#EF9C20","#F56C6C","#A277FF"];return e[t.dataIndex]}}},labelLine:{show:!1},data:n}]}}))}}},mt=vt,yt=(r("937b"),Object(_["a"])(mt,ht,ft,!1,null,"658dd3a0",null)),gt=yt.exports,wt=(r("7dae"),r("2b9b")),bt=r("63f0"),_t={name:"Dashboard",components:{baseInfo:C,gridMenu:G,visitChart:tt,userChart:ut,userOverview:gt},data:function(){return{authStatus:null,authHost:"",authQueryStatus:!1,notInformation:null}},mounted:function(){-1==window.location.host.indexOf("localhost")&&(this.authStatus=bt["a"].local.has("auth-information")?bt["a"].local.getJSON("auth-information"):null,this.notInformation=bt["a"].local.has("not-information")?bt["a"].local.getJSON("not-information"):null,this.authStatus||this.getUniq())},methods:{checkPermi:P["a"],authInformationQuery:function(){},getUniq:function(){var t=this;Object(wt["a"])({key:"authHost"}).then((function(e){t.authHost=e,""!==e?t.authInformationQuery():t.$modal.confirm("您尚未提交授权申请").then((function(){t.$router.push({path:"/maintain/authCRMEB"})}))}))}}},xt=_t,Ct=Object(_["a"])(xt,n,i,!1,null,null,null);e["default"]=Ct.exports},"94fd":function(t,e,r){},b6fa:function(t,e,r){},d523:function(t,e,r){},e56e:function(t,e,r){"use strict";r("3124")},e5af:function(t,e,r){"use strict";r("ec2b")},ec2b:function(t,e,r){}}]);