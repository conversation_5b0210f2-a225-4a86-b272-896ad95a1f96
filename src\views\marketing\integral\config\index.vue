<template>
  <div class="divBox">
    <el-card class="box-card" v-loading="loading">
      <z-b-parser
        :form-id="formId"
        :is-create="isCreate"
        :edit-data="editData"
        @submit="handlerSubmit"
        v-if="isShow"
      ></z-b-parser>
    </el-card>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { integralSetConfigApi, integralGetConfigApi } from '@/api/systemConfig.js';
import { Debounce } from '@/utils/validate';

export default {
  name: 'integralconfig',
  data() {
    return {
      isShow: false,
      isCreate: 0,
      editData: {},
      formId: 47,
      loading: false,
    };
  },
  mounted() {
    this.getFormInfo();
  },
  methods: {
    handlerSubmit: Debounce(function (data) {
      integralSetConfigApi(data).then((res) => {
        this.getFormInfo();
        this.$message.success('操作成功');
      });
    }),
    // 获取表单详情
    getFormInfo() {
      this.loading = true;
      integralGetConfigApi().then((res) => {
        this.isShow = false;
        this.editData = {
          freezeIntegralDay: res.freezeIntegralDay,
          integralDeductionMoney: res.integralDeductionMoney,
          integralDeductionRatio: res.integralDeductionRatio,
          integralDeductionStartMoney: res.integralDeductionStartMoney,
          integralDeductionSwitch: res.integralDeductionSwitch,
          integralSwitch: res.integralSwitch,
          orderGiveIntegral: res.orderGiveIntegral,
          inviteRegister: res.inviteRegister,
          orderEvaluation: res.orderEvaluation,
          integralRule: res.integralRule,
        };
        this.isCreate = 1;
        setTimeout(() => {
          // 让表单重复渲染待编辑数据
          this.isShow = true;
        }, 20);
        this.loading = false;
      });
    },
  },
};
</script>

<style scoped></style>
