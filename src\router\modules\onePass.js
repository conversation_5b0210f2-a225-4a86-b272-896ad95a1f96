// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from '@/layout';

const onePassRouter = {
  path: '/onePass',
  component: Layout,
  redirect: '/onePass/index',
  name: 'OnePass',
  meta: {
    title: '一号通',
    icon: 'clipboard',
    roles: ['admin'],
  },
  children: [
    {
      path: 'index',
      name: 'onePass',
      component: () => import('@/views/sms/smsConfig'),
      meta: {
        title: '一号通',
        icon: 'clipboard',
      },
    },
    {
      path: 'pay',
      component: () => import('@/views/sms/smsPay'),
      name: 'SmsPay',
      meta: { title: '短信购买', noCache: true, activeMenu: `/onePass/index` },
    },
  ],
};

export default onePassRouter; //collate
