<template>
  <div>
    <el-form ref="editPram" :model="editPram" label-width="130px">
      <el-form-item
        label="分类名称"
        prop="name"
        :rules="[{ required: true, message: '请输入分类名称', trigger: ['blur', 'change'] }]"
      >
        <el-input v-model="editPram.name" :maxlength="biztype.value === 1 ? 8 : 20" placeholder="分类名称" />
      </el-form-item>
      <el-form-item label="父级" v-if="biztype.value !== 2">
        <el-cascader
          v-model="editPram.pid"
          :disabled="isCreate === 1"
          :options="allTreeList"
          filterable
          :props="categoryProps"
          style="width: 100%"
          ref="cascader"
          @change="handleChange"
          clearable
        />
      </el-form-item>
      <el-form-item label="分类图标(180*180)">
        <div class="upLoadPicBox" @click="modalPicTap('1')">
          <div v-if="editPram.icon" class="pictrue">
            <img :src="editPram.icon" />
          </div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="排序">
        <el-input-number v-model="editPram.sort" :min="0" />
      </el-form-item>
      <el-form-item label="扩展字段" v-if="biztype.value !== 1 && biztype.value !== 3 && biztype.value !== 5">
        <el-input v-model="editPram.extra" type="textarea" placeholder="扩展字段" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :loading="loadingBtn"
          @click="handlerSubmit('editPram')"
          v-hasPermi="['platform:category:update']"
          >确定</el-button
        >
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<!--创建和编辑公用一个组件-->
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import * as articleApi from '@/api/article.js';
import * as storeApi from '@/api/product.js';
export default {
  // name: "edit"
  props: {
    prent: {
      type: Object,
      required: true,
    },
    isCreate: {
      type: Number,
      default: 0,
    },
    editData: {
      type: Object,
    },
    biztype: {
      type: Object,
      required: true,
    },
    allTreeList: {
      type: Array,
    },
  },
  data() {
    return {
      loadingBtn: false,
      constants: this.$constants,
      editPram: {
        icon: null,
        name: null,
        pid: null,
        sort: 0,
        // status: true,
        type: this.biztype.value,
        url: null,
        id: 0,
      },
      categoryProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
      },
      parentOptions: [],
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    handleChange() {
      this.prent.level = this.$refs['cascader'].getCheckedNodes()[0].level
    },
    // 点击图标
    addIcon() {
      const _this = this;
      _this.$modalIcon(function (icon) {
        _this.editPram.icon = icon;
      });
    },
    // 点击商品图
    modalPicTap(tit, num, i) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (tit === '1' && !num) {
            _this.editPram.icon = img[0].sattDir;
          }
          if (tit === '2' && !num) {
            img.map((item) => {
              attr.push(item.attachment_src);
              _this.formValidate.slider_image.push(item);
            });
          }
        },
        tit,
        'store',
      );
    },
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      this.parentOptions = [...this.allTreeList];
      this.addTreeListLabelForCasCard(this.parentOptions, 'child');
      const { icon, name, pid, sort, type, id, url, level } = this.editData;
      if (this.isCreate === 1) {
        this.editPram.icon = icon;
        this.editPram.name = name;
        this.editPram.pid = pid;
        this.editPram.sort = sort;
        this.editPram.type = type;
        this.editPram.url = url;
        this.editPram.id = id;
        this.editPram.level = level;
      } else {
        this.editPram.pid = this.prent.id;
        this.editPram.type = this.biztype.value;
        this.editPram.level = parseInt(this.prent.level) + 1;
      }
    },
    addTreeListLabelForCasCard(arr, child) {
      arr.forEach((o, i) => {
        if (o.child && o.child.length) {
          // o.disabled = true
          o.child.forEach((j) => {
            j.disabled = true;
          });
        }
      });
    },
    handlerSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) return;
        this.handlerSaveOrUpdate(this.isCreate === 0);
      });
    },
    handlerSaveOrUpdate(isSave) {
      if (isSave) {
        // this.editPram.pid = this.prent.id
        this.loadingBtn = true;
        if (this.biztype.value !== 2) {
          if (this.editPram.pid === 0) this.editPram.level = 1;
          if (!this.editPram.level) this.editPram.level = parseInt(this.prent.level) + 1;
          storeApi
            .productCategoryAddApi(this.editPram)
            .then((data) => {
              this.$emit('hideEditDialog');
              this.$message.success('创建目录成功');
              this.$store.commit('product/SET_AdminProductClassify', []);
              this.loadingBtn = false;
            })
            .catch(() => {
              this.loadingBtn = false;
            });
        } else {
          articleApi
            .articleCategoryAddApi(this.editPram)
            .then((data) => {
              this.$emit('hideEditDialog');
              this.$message.success('创建目录成功');
              localStorage.clear('articleClass');
              this.loadingBtn = false;
            })
            .catch(() => {
              this.loadingBtn = false;
            });
        }
      } else {
        this.loadingBtn = true;
        if (this.biztype.value !== 2) {
          if (this.editPram.pid === this.editData.id) return this.$message.warning('父级不能选当前分类');
          storeApi
            .productCategoryUpdateApi(this.editPram)
            .then((data) => {
              this.$emit('hideEditDialog');
              this.$message.success('更新目录成功');
              this.$store.commit('product/SET_AdminProductClassify', []);
              this.loadingBtn = false;
            })
            .catch(() => {
              this.loadingBtn = false;
            });
        } else {
          this.editPram.pid = Array.isArray(this.editPram.pid) ? this.editPram.pid[0] : this.editPram.pid;
          articleApi
            .articleCategoryUpdateApi(this.editPram)
            .then((data) => {
              this.$emit('hideEditDialog');
              this.$message.success('更新目录成功');
              localStorage.clear('articleClass');
              this.loadingBtn = false;
            })
            .catch(() => {
              this.loadingBtn = false;
            });
        }
      }
    },
  },
};
</script>

<style scoped></style>
