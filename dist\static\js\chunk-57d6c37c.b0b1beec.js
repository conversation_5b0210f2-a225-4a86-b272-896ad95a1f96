(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-57d6c37c"],{1139:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[[i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:category:add","platform:article:category:add"],expression:"['platform:product:category:add', 'platform:article:category:add']"}],attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handleAddMenu({id:0,name:"顶层目录"})}}},[e._v("新增"+e._s(e.biztype.name)+"\n          ")]),e._v(" "),1===e.biztype.value?i("el-alert",{staticClass:"alert_title",attrs:{title:"温馨提示",description:"平台产品分类必须要设置三级分类",type:"warning",effect:"dark"}}):e._e()],1),e._v(" "),i("el-table",{ref:"treeList",staticClass:"table",attrs:{data:e.dataList,size:"mini","highlight-current-row":"","row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"}}},[i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"240"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.name)+" | "+e._s(t.row.id))]}}])}),e._v(" "),e.selectModel?e._e():[i("el-table-column",{attrs:{label:"分类图标","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"demo-image__preview"},[t.row.icon?i("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.icon,"preview-src-list":[t.row.icon]}}):i("img",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.defaultImg,alt:""}})],1)]}}],null,!1,2354360158)}),e._v(" "),5===e.biztype.value?i("el-table-column",{key:"2",attrs:{label:"Url","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.url))])]}}],null,!1,3700262509)}):e._e(),e._v(" "),i("el-table-column",{attrs:{label:"排序",prop:"sort","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:"状态","min-width":"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["platform:product:category:show:status","platform:article:category:switch"])?[2===e.biztype.value?i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"显示","inactive-text":"隐藏"},on:{change:function(i){return e.onchangeIsShow(t.row)}},model:{value:t.row.status,callback:function(i){e.$set(t.row,"status",i)},expression:"scope.row.status"}}):i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"显示","inactive-text":"隐藏"},on:{change:function(i){return e.onchangeIsShow(t.row)}},model:{value:t.row.isShow,callback:function(i){e.$set(t.row,"isShow",i)},expression:"scope.row.isShow"}})]:void 0}}],null,!0)}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"200",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===e.biztype.value&&t.row.level<3?i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:category:add","platform:article:category:add"],expression:"['platform:product:category:add', 'platform:article:category:add']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleAddMenu(t.row)}}},[e._v("添加子目录\n                ")]):e._e(),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:category:update","platform:article:category:update"],expression:"['platform:product:category:update', 'platform:article:category:update']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleEditMenu(t.row)}}},[e._v("编辑\n                ")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:category:delete","platform:article:category:delete"],expression:"['platform:product:category:delete', 'platform:article:category:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleDelMenu(t.row)}}},[e._v("删除\n                ")])]}}],null,!1,3175170922)})]],2)],1)],1)],e._v(" "),i("el-dialog",{attrs:{title:0===e.editDialogConfig.isCreate?"创建"+e.biztype.name:"编辑"+e.biztype.name,visible:e.editDialogConfig.visible,"destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){return e.$set(e.editDialogConfig,"visible",t)}}},[e.editDialogConfig.visible?i("edit",{attrs:{prent:e.editDialogConfig.prent,"is-create":e.editDialogConfig.isCreate,"edit-data":e.editDialogConfig.data,biztype:e.editDialogConfig.biztype,"all-tree-list":e.dataList},on:{hideEditDialog:e.hideEditDialog}}):e._e()],1)],2)},n=[],r=i("2423"),l=i("c4c8"),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-tree",{attrs:{data:e.ddd,props:e.defaultProps},on:{"node-click":e.handleNodeClick}})],1)},o=[],d=i("651a"),c={props:{id:{type:Number,required:!0}},data:function(){return{defaultProps:{children:"children",label:"label"},ddd:[{label:"一级 1",children:[{label:"二级 1-1",children:[{label:"三级 1-1-1"}]}]},{label:"一级 2",children:[{label:"二级 2-1",children:[{label:"三级 2-1-1"}]},{label:"二级 2-2",children:[{label:"三级 2-2-1"}]}]},{label:"一级 3",children:[{label:"二级 3-1",children:[{label:"三级 3-1-1"}]},{label:"二级 3-2",children:[{label:"三级 3-2-1"}]}]}],dataList:{page:0,limit:0,totalPage:0,total:0,list:[]}}},mounted:function(){this.handlerGetTreeList(this.id)},methods:{handlerGetTreeList:function(e){var t=this;e?d["d"]({pid:e}).then((function(e){t.dataList=e})):this.$message.error("当前数据id不正确")},handleNodeClick:function(e){console.log("data:",e)}}},u=c,h=i("2877"),p=Object(h["a"])(u,s,o,!1,null,"059d689d",null),m=p.exports,f=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{ref:"editPram",attrs:{model:e.editPram,"label-width":"130px"}},[i("el-form-item",{attrs:{label:"分类名称",prop:"name",rules:[{required:!0,message:"请输入分类名称",trigger:["blur","change"]}]}},[i("el-input",{attrs:{maxlength:1===e.biztype.value?8:20,placeholder:"分类名称"},model:{value:e.editPram.name,callback:function(t){e.$set(e.editPram,"name",t)},expression:"editPram.name"}})],1),e._v(" "),2!==e.biztype.value?i("el-form-item",{attrs:{label:"父级"}},[i("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{disabled:1===e.isCreate,options:e.allTreeList,filterable:"",props:e.categoryProps,clearable:""},on:{change:e.handleChange},model:{value:e.editPram.pid,callback:function(t){e.$set(e.editPram,"pid",t)},expression:"editPram.pid"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"分类图标(180*180)"}},[i("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("1")}}},[e.editPram.icon?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.editPram.icon}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),e._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{min:0},model:{value:e.editPram.sort,callback:function(t){e.$set(e.editPram,"sort",t)},expression:"editPram.sort"}})],1),e._v(" "),1!==e.biztype.value&&3!==e.biztype.value&&5!==e.biztype.value?i("el-form-item",{attrs:{label:"扩展字段"}},[i("el-input",{attrs:{type:"textarea",placeholder:"扩展字段"},model:{value:e.editPram.extra,callback:function(t){e.$set(e.editPram,"extra",t)},expression:"editPram.extra"}})],1):e._e(),e._v(" "),i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:category:update"],expression:"['platform:category:update']"}],attrs:{type:"primary",loading:e.loadingBtn},on:{click:function(t){return e.handlerSubmit("editPram")}}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)],1)},g=[];function v(e){return C(e)||P(e)||y(e)||b()}function b(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"===typeof e)return w(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?w(e,t):void 0}}function P(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function C(e){if(Array.isArray(e))return w(e)}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,a=new Array(t);i<t;i++)a[i]=e[i];return a}var _={props:{prent:{type:Object,required:!0},isCreate:{type:Number,default:0},editData:{type:Object},biztype:{type:Object,required:!0},allTreeList:{type:Array}},data:function(){return{loadingBtn:!1,constants:this.$constants,editPram:{icon:null,name:null,pid:null,sort:0,type:this.biztype.value,url:null,id:0},categoryProps:{value:"id",label:"name",children:"children",expandTrigger:"hover",checkStrictly:!0,emitPath:!1},parentOptions:[]}},mounted:function(){this.initEditData()},methods:{handleChange:function(){this.prent.level=this.$refs["cascader"].getCheckedNodes()[0].level},addIcon:function(){var e=this;e.$modalIcon((function(t){e.editPram.icon=t}))},modalPicTap:function(e,t,i){var a=this,n=[];this.$modalUpload((function(i){"1"!==e||t||(a.editPram.icon=i[0].sattDir),"2"!==e||t||i.map((function(e){n.push(e.attachment_src),a.formValidate.slider_image.push(e)}))}),e,"store")},close:function(){this.$emit("hideEditDialog")},initEditData:function(){this.parentOptions=v(this.allTreeList),this.addTreeListLabelForCasCard(this.parentOptions,"child");var e=this.editData,t=e.icon,i=e.name,a=e.pid,n=e.sort,r=e.type,l=e.id,s=e.url,o=e.level;1===this.isCreate?(this.editPram.icon=t,this.editPram.name=i,this.editPram.pid=a,this.editPram.sort=n,this.editPram.type=r,this.editPram.url=s,this.editPram.id=l,this.editPram.level=o):(this.editPram.pid=this.prent.id,this.editPram.type=this.biztype.value,this.editPram.level=parseInt(this.prent.level)+1)},addTreeListLabelForCasCard:function(e,t){e.forEach((function(e,t){e.child&&e.child.length&&e.child.forEach((function(e){e.disabled=!0}))}))},handlerSubmit:function(e){var t=this;this.$refs[e].validate((function(e){e&&t.handlerSaveOrUpdate(0===t.isCreate)}))},handlerSaveOrUpdate:function(e){var t=this;if(e)this.loadingBtn=!0,2!==this.biztype.value?(0===this.editPram.pid&&(this.editPram.level=1),this.editPram.level||(this.editPram.level=parseInt(this.prent.level)+1),l["o"](this.editPram).then((function(e){t.$emit("hideEditDialog"),t.$message.success("创建目录成功"),t.$store.commit("product/SET_AdminProductClassify",[]),t.loadingBtn=!1})).catch((function(){t.loadingBtn=!1}))):r["f"](this.editPram).then((function(e){t.$emit("hideEditDialog"),t.$message.success("创建目录成功"),localStorage.clear("articleClass"),t.loadingBtn=!1})).catch((function(){t.loadingBtn=!1}));else if(this.loadingBtn=!0,2!==this.biztype.value){if(this.editPram.pid===this.editData.id)return this.$message.warning("父级不能选当前分类");l["t"](this.editPram).then((function(e){t.$emit("hideEditDialog"),t.$message.success("更新目录成功"),t.$store.commit("product/SET_AdminProductClassify",[]),t.loadingBtn=!1})).catch((function(){t.loadingBtn=!1}))}else this.editPram.pid=Array.isArray(this.editPram.pid)?this.editPram.pid[0]:this.editPram.pid,r["j"](this.editPram).then((function(e){t.$emit("hideEditDialog"),t.$message.success("更新目录成功"),localStorage.clear("articleClass"),t.loadingBtn=!1})).catch((function(){t.loadingBtn=!1}))}}},k=_,S=Object(h["a"])(k,f,g,!1,null,"32e65dd9",null),$=S.exports,x=i("fca7"),D=i("e350"),z={components:{info:m,edit:$},props:{biztype:{type:Object,default:{value:-1},validator:function(e){return e.value>0}},pid:{type:Number,default:0,validator:function(e){return e>=0}},selectModel:{type:Boolean,default:!1},selectModelKeys:{type:Array},rowSelect:{}},data:function(){return{selectModelKeysNew:this.selectModelKeys,loading:!1,constants:this.$constants,treeProps:{label:"name",children:"child"},multipleSelection:[],editDialogConfig:{visible:!1,isCreate:0,prent:{},data:{},biztype:this.biztype},dataList:[],listPram:{pid:this.pid,type:this.biztype.value,status:-1,name:"",page:this.$constants.page.page,limit:this.$constants.page.limit[0]},viewInfoConfig:{data:null,visible:!1},defaultImg:i("cf6b")}},mounted:function(){2===this.biztype.value?this.handlerGetList():this.handlerGetTreeList()},methods:{checkPermi:D["a"],onchangeIsShow:function(e){var t=this;2===this.biztype.value?r["i"](e.id).then((function(){t.$message.success("修改成功"),t.handlerGetList()})).catch((function(){e.status=!e.status})):l["s"](e.id).then((function(){t.$message.success("修改成功"),t.handlerGetTreeList()})).catch((function(){e.isShow=!e.isShow}))},handleEditMenu:function(e){this.editDialogConfig.isCreate=1,this.editDialogConfig.data=e,this.editDialogConfig.prent=e,this.editDialogConfig.visible=!0},handleAddMenu:function(e){this.editDialogConfig.isCreate=0,this.editDialogConfig.prent=e,this.editDialogConfig.data={},this.editDialogConfig.biztype=this.biztype,this.editDialogConfig.visible=!0},getCurrentNode:function(e){var t=this.$refs.tree.getNode(e);this.childNodes(t),this.$emit("rulesSelect",this.$refs.tree.getCheckedKeys())},childNodes:function(e){for(var t=e.childNodes.length,i=0;i<t;i++)e.childNodes[i].checked=e.checked,this.childNodes(e.childNodes[i])},parentNodes:function(e){if(e.parent)for(var t in e)"parent"==t&&(e[t].checked=!0,this.parentNodes(e[t]))},handleDelMenu:function(e){var t=this;this.$modalSure("删除当前数据?").then((function(){2===t.biztype.value?r["g"](e).then((function(e){t.handlerGetList(),localStorage.clear("articleClass"),t.$message.success("删除成功")})):l["q"](e.id).then((function(e){t.handlerGetTreeList(),t.$message.success("删除成功")}))})).catch((function(){}))},handlerGetList:function(){var e=this;r["h"]().then((function(t){e.dataList=t;var i=t.filter((function(e){return e.status}));localStorage.setItem("articleClass",JSON.stringify(i))}))},handlerGetTreeList:function(){var e=this;l["r"]().then((function(t){var i={},a=[];t.forEach((function(e){i=e,i.parentId=e.pid,i.children=[],a.push(i)})),e.dataList=e.handleTree(a,"menuId"),e.loading=!1})).catch((function(){e.loading=!1}))},handlerGetInfo:function(e){this.viewInfoConfig.data=e,this.viewInfoConfig.visible=!0},handleNodeClick:function(e){console.log("data:",e)},handleAddArrt:function(e){var t=x["addTreeListLabel"](e);return t},hideEditDialog:function(){var e=this;setTimeout((function(){e.editDialogConfig.prent={},e.editDialogConfig.type=0,e.editDialogConfig.visible=!1,2===e.biztype.value?e.handlerGetList():e.handlerGetTreeList()}),200)},handleSelectionChange:function(e,t){t.checkedNodes;var i=t.checkedKeys;t.halfCheckedNodes,t.halfCheckedKeys;this.multipleSelection=i,this.$emit("rulesSelect",this.multipleSelection)}}},L=z,N=(i("74c8"),Object(h["a"])(L,a,n,!1,null,"0e2f384f",null));t["a"]=N.exports},"74c8":function(e,t,i){"use strict";i("fd241")},fd241:function(e,t,i){}}]);