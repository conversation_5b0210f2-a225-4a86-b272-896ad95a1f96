(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c575b"],{"3ed7":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"昵称","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.nickname))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"手机号",prop:"phone","min-width":"100","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{label:"连续签到天数",prop:"day","min-width":"100","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"experience",label:"签到经验","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"integral",label:"签到积分","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"awardExperience",label:"连续签获经验","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"awardIntegral",label:"连续签获积分","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"签到日期","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.date))])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)],1)},i=[],n=a("b7be"),o={data:function(){return{tableFrom:{page:1,limit:20},tableData:{data:[],total:0},listLoading:!1}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;this.listLoading=!0,Object(n["m"])(this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},r=o,s=a("2877"),c=Object(s["a"])(r,l,i,!1,null,null,null);e["default"]=c.exports}}]);