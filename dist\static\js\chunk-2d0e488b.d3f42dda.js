(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e488b"],{9141:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("upload-picture",{attrs:{pictureType:e.pictureType}})],1)],1)},c=[],i={name:"index",data:function(){return{pictureType:"maintain"}}},r=i,u=a("2877"),s=Object(u["a"])(r,n,c,!1,null,"410661ec",null);t["default"]=s.exports}}]);