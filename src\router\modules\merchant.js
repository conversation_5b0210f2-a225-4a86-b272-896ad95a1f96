// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from '@/layout';

const merchantRouter = {
  path: '/merchant',
  component: Layout,
  redirect: '/merchant',
  name: 'Merchant',
  meta: {
    title: '商户',
    icon: 'clipboard',
  },
  children: [
    {
      path: 'classify',
      name: 'classify',
      component: () => import('@/views/merchant/classify'),
      meta: { title: '商户分类', icon: '' },
    },
    {
      path: 'list',
      name: 'list',
      component: () => import('@/views/merchant/list'),
      meta: { title: '商户列表', icon: '' },
    },
    {
      path: 'system',
      name: 'system',
      component: () => import('@/views/merchant/system'),
      meta: { title: '商户菜单管理', icon: '' },
    },
    {
      path: 'application',
      name: 'application',
      component: () => import('@/views/merchant/application'),
      meta: { title: '商户入驻申请', icon: '' },
    },
    {
      path: 'type',
      name: 'type',
      component: () => import('@/views/merchant/type'),
      meta: {
        title: '店铺类型',
        icon: 'clipboard',
      },
      children: [
        {
          path: 'list',
          component: () => import('@/views/merchant/type/list'),
          name: 'list',
          meta: { title: '店铺类型', icon: '' },
        },
      ],
    },
  ],
};

export default merchantRouter;
