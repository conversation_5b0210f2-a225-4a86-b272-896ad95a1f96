(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c3184f32"],{8770:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px",inline:!0}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"选择时间："}},[a("el-radio-group",{attrs:{size:"small"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,"dateLimit",e)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,r){return a("el-radio-button",{key:r,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(" "),a("el-date-picker",{attrs:{type:"daterange",placeholder:"选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"审核状态："}},[a("el-radio-group",{attrs:{size:"small"},on:{change:function(e){return t.statusChange(t.tableFrom.auditStatus)}},model:{value:t.tableFrom.auditStatus,callback:function(e){t.$set(t.tableFrom,"auditStatus",e)},expression:"tableFrom.auditStatus"}},t._l(t.statusList.fromTxt,(function(e,r){return a("el-radio-button",{key:r,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商户分类："}},[a("el-select",{staticClass:"selWidth",attrs:{clearable:"",placeholder:"请选择"},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.categoryId,callback:function(e){t.$set(t.tableFrom,"categoryId",e)},expression:"tableFrom.categoryId"}},t._l(t.merchantClassify,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"店铺类型："}},[a("el-select",{staticClass:"selWidth",attrs:{clearable:"",placeholder:"请选择"},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.typeId,callback:function(e){t.$set(t.tableFrom,"typeId",e)},expression:"tableFrom.typeId"}},t._l(t.merchantType,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"关键字："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商户名称/关键字"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getList(1)}},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[a("el-button",{staticClass:"el-button-solt",attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"switchTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"small","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"商户姓名："}},[a("span",[t._v(t._s(e.row.realName))])]),t._v(" "),a("el-form-item",{attrs:{label:"商户类别："}},[a("span",[t._v(t._s(e.row.isSelf?"自营":"非自营"))])]),t._v(" "),a("el-form-item",{attrs:{label:"备注："}},[a("span",[t._v(t._s(e.row.remark))])])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"商户名称","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"email",label:"商户邮箱","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"phone",label:"联系方式","min-width":"130"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"申请时间","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[2==e.row.auditStatus?a("el-tag",{attrs:{type:"success"}},[t._v("通过")]):t._e(),t._v(" "),1==e.row.auditStatus?a("el-tag",{attrs:{type:"info"}},[t._v("未处理")]):t._e(),t._v(" "),3==e.row.auditStatus?a("el-tag",{attrs:{type:"warning"}},[t._v("未通过")]):t._e(),t._v(" "),3==e.row.auditStatus?a("div",[t._v("原因："+t._s(e.row.denialReason))]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"110",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.auditStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:merchant:apply:audit"],expression:"['platform:merchant:apply:audit']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.onchangeIsShow(e.row,"isSHOW")}}},[t._v("审核")]):t._e(),t._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.onchangeIsShow(e.row)}}},[t._v("详情")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:merchant:apply:remark"],expression:"['platform:merchant:apply:remark']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.onEdit(e.row)}}},[t._v("备注")])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("audit-from",{ref:"auditFroms",attrs:{merData:t.merData,isSHOW:t.isSHOW},on:{subSuccess:t.subSuccess}})],1)},i=[],l=a("8492"),n=a("2f62"),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"box"},[a("el-drawer",{attrs:{visible:t.dialogVisible,title:t.isSHOW?"商户入驻审核":"商户详情",direction:t.direction,size:"700px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[a("div",{staticClass:"demo-drawer__content divBox"},[t.dialogVisible?a("el-form",{ref:"dataForm",attrs:{inline:!0,model:t.dataForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"商户名称：",prop:"name"}},[a("el-input",{attrs:{readonly:t.isDisabled,placeholder:"请输入商户名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商户账号："}},[a("el-input",{attrs:{readonly:t.isDisabled,placeholder:"请输入商户账号"},model:{value:t.dataForm.phone,callback:function(e){t.$set(t.dataForm,"phone",e)},expression:"dataForm.phone"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商户分类：",prop:"categoryId"}},[a("span",{staticClass:"widths"},[t._v(t._s(t._f("merCategoryFilter")(t.dataForm.categoryId)))])]),t._v(" "),a("el-form-item",{attrs:{label:"店铺类型：",prop:"typeId"}},[a("span",{staticClass:"widths"},[t._v(t._s(t._f("merchantTypeFilter")(t.dataForm.typeId)))])]),t._v(" "),t.dataForm.password?a("el-form-item",{attrs:{label:"登录密码",prop:"password"}},[a("el-input",{attrs:{readonly:t.isDisabled,placeholder:"请输入登录密码"},model:{value:t.dataForm.password,callback:function(e){t.$set(t.dataForm,"password",e)},expression:"dataForm.password"}})],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:"商户姓名：",prop:"realName"}},[a("el-input",{attrs:{readonly:t.isDisabled,placeholder:"请输入商户姓名"},model:{value:t.dataForm.realName,callback:function(e){t.$set(t.dataForm,"realName",e)},expression:"dataForm.realName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商户手机号：",prop:"phone"}},[a("span",{staticClass:"widths"},[t._v(t._s(t._f("filterEmpty")(t.dataForm.phone)))])]),t._v(" "),a("el-form-item",{staticClass:"inline",attrs:{prop:"email",label:"邮箱："}},[a("el-input",{attrs:{readonly:t.isDisabled},model:{value:t.dataForm.email,callback:function(e){t.$set(t.dataForm,"email",e)},expression:"dataForm.email"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"手续费(%)：",prop:"handlingFee"}},[a("el-input",{attrs:{disabled:t.isDisabled,min:0,precision:2},model:{value:t.dataForm.handlingFee,callback:function(e){t.$set(t.dataForm,"handlingFee",e)},expression:"dataForm.handlingFee"}})],1),t._v(" "),a("el-form-item",{staticClass:"lang",attrs:{label:"商户地址：",prop:"address"}},[a("span",{staticClass:"langcent"},[t._v(t._s(t.dataForm.address))])]),t._v(" "),a("el-form-item",{staticClass:"inline",attrs:{label:"简介：",prop:"keywords"}},[a("div",[t._v(t._s(t.dataForm.keywords||"无"))])]),t._v(" "),a("el-form-item",{staticClass:"lang",attrs:{label:"备注：",prop:"remark"}},[a("span",{staticClass:"widths"},[t._v(t._s(t.dataForm.remark?t.dataForm.remark:"无"))])]),t._v(" "),a("el-form-item",{staticClass:"lang",attrs:{label:"资质图片："}},[a("div",{staticClass:"acea-row"},t._l(t.dataForm.qualificationPictureData,(function(e,r){return a("div",{key:r,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a("el-image",{attrs:{src:e,"preview-src-list":t.dataForm.qualificationPictureData}})],1)})),0)]),t._v(" "),a("el-form-item",{attrs:{label:"是否开启："}},[a("span",{staticClass:"widths"},[t._v(t._s(t.dataForm.isSwitch?"显示":"隐藏"))])]),t._v(" "),a("el-form-item",{attrs:{label:"是否推荐："}},[a("span",{staticClass:"widths"},[t._v(t._s(t.dataForm.isRecommend?"推荐":"不推荐"))])]),t._v(" "),a("el-form-item",{attrs:{label:"是否自营："}},[a("span",{staticClass:"widths"},[t._v(t._s(t.dataForm.isSelf?"自营":"非自营"))])]),t._v(" "),a("el-form-item",{attrs:{label:"商品审核："}},[a("span",{staticClass:"widths"},[t._v(t._s(t.dataForm.productSwitch?"开启":"关闭"))])])],1):t._e()],1),t._v(" "),t.isSHOW?a("div",{staticClass:"from-foot-btn fix btn-shadow"},[a("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[a("el-radio-group",{model:{value:t.ruleForm.auditStatus,callback:function(e){t.$set(t.ruleForm,"auditStatus",e)},expression:"ruleForm.auditStatus"}},[a("el-radio",{attrs:{label:2}},[t._v("通过")]),t._v(" "),a("el-radio",{attrs:{label:3}},[t._v("拒绝")])],1)],1),t._v(" "),3===t.ruleForm.auditStatus?a("el-form-item",{attrs:{label:"原因",prop:"denialReason"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入原因"},model:{value:t.ruleForm.denialReason,callback:function(e){t.$set(t.ruleForm,"denialReason",e)},expression:"ruleForm.denialReason"}})],1):t._e(),t._v(" "),a("el-form-item",[a("el-button",{on:{click:t.close}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("ruleForm")}}},[t._v(t._s(t.loadingBtn?"提交中 ...":"确 定"))])],1)],1)],1):t._e()])],1)},o=[];function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function m(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach((function(e){d(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function d(t,e,a){return e=p(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function p(t){var e=f(t,"string");return"symbol"===c(e)?e:String(e)}function f(t,e){if("object"!==c(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var b={name:"audit",data:function(){return{dialogVisible:!1,direction:"rtl",isDisabled:!0,rules:{auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],denialReason:[{required:!0,message:"请填写拒绝原因",trigger:"blur"}]},ruleForm:{denialReason:"",auditStatus:2,id:""},loadingBtn:!1}},props:{merData:{type:Object,default:function(){return null}},isSHOW:{type:String,default:function(){return""}}},computed:m({},Object(n["b"])(["merchantClassify","merchantType"])),watch:{merData:{handler:function(t){t.qualificationPicture&&(t.qualificationPictureData=JSON.parse(t.qualificationPicture)),this.dataForm=m({},t)},deep:!0}},methods:{close:function(){this.dialogVisible=!1,this.ruleForm={denialReason:"",auditStatus:2}},onSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loadingBtn=!0,e.ruleForm.id=e.dataForm.id,Object(l["f"])(e.ruleForm).then((function(t){e.$message.success("操作成功"),e.dialogVisible=!1,e.$emit("subSuccess"),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}))}}},h=b,v=(a("9238"),a("2877")),g=Object(v["a"])(h,s,o,!1,null,"61ccf195",null),y=g.exports;function _(t){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(t)}function F(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function w(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?F(Object(a),!0).forEach((function(e){S(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):F(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function S(t,e,a){return e=O(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function O(t){var e=k(t,"string");return"symbol"===_(e)?e:String(e)}function k(t,e){if("object"!==_(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var C={name:"MerchantApplication",components:{auditFrom:y},data:function(){return{props:{emitPath:!1},fromList:this.$constants.fromList,statusList:this.$constants.statusList,isChecked:!1,listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,dateLimit:"",auditStatus:"",keywords:"",categoryId:"",typeId:""},mer_id:this.$route.query.id?this.$route.query.id:"",autoUpdate:!0,timeVal:[],merData:{},isSHOW:""}},computed:w({},Object(n["b"])(["merchantClassify","merchantType"])),watch:{mer_id:function(t,e){this.getList("")}},mounted:function(){this.getList("")},methods:{subSuccess:function(){this.getList("")},selectChange:function(t){this.tableFrom.dateLimit=t,this.timeVal=[],this.tableFrom.page=1,this.getList("")},statusChange:function(t){this.tableFrom.auditStatus=t,this.tableFrom.page=1,this.getList("")},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=this.timeVal?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList("")},getList:function(t){var e=this;this.merchantClassify.length||this.$store.dispatch("merchant/getMerchantClassify"),this.merchantType.length||this.$store.dispatch("merchant/getMerchantType"),this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(l["g"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList("")},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList(1)},onchangeIsShow:function(t,e){this.merData=t,this.isSHOW=e,this.$refs.auditFroms.dialogVisible=!0},onEdit:function(t){var e=this;this.$prompt("备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入备注",inputType:"textarea",inputValue:t.remark,inputPlaceholder:"请输入备注",inputValidator:function(t){if(!t)return"请输入备注"}}).then((function(a){var r=a.value;Object(l["h"])({id:t.id,remark:r}).then((function(t){e.$message({type:"success",message:"提交成功"}),e.getList("")}))})).catch((function(){e.$message({type:"info",message:"取消输入"})}))},handleDelete:function(t){var e=this;this.$modalSure().then((function(){intentionDelte(t).then((function(t){var a=t.message;e.$message.success(a),e.getList(1)}))})).catch((function(){}))}}},x=C,j=(a("974d"),Object(v["a"])(x,r,i,!1,null,"1bd3a913",null));e["default"]=j.exports},9238:function(t,e,a){"use strict";a("fa84")},"974d":function(t,e,a){"use strict";a("ef98")},ef98:function(t,e,a){},fa84:function(t,e,a){}}]);