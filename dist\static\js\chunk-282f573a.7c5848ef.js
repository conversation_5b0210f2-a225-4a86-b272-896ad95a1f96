(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-282f573a"],{"21d2":function(t,e,r){"use strict";r.d(e,"a",(function(){return o})),r.d(e,"b",(function(){return l})),r.d(e,"c",(function(){return i})),r.d(e,"e",(function(){return n})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return c}));var a=r("b775");function o(){return Object(a["a"])({url:"/admin/platform/retail/store/config/get",method:"get"})}function l(t){return Object(a["a"])({url:"/admin/platform/retail/store/config/save",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/admin/platform/retail/store/people/list",method:"get",params:t})}function n(t){return Object(a["a"])({url:"/admin/platform/retail/store/sub/user/list",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/admin/platform/retail/store/promotion/order/list",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/admin/platform/retail/store/clean/spread/".concat(t),method:"get"})}},9026:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"promoterForm",staticClass:"demo-promoterForm",attrs:{model:t.promoterForm,rules:t.rules,"label-width":"200px"}},[r("el-form-item",{attrs:{prop:"retailStoreSwitch"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销启用：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"商城分销功能开启关闭",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.promoterForm.retailStoreSwitch,callback:function(e){t.$set(t.promoterForm,"retailStoreSwitch",e)},expression:"promoterForm.retailStoreSwitch"}},[r("el-radio",{attrs:{label:1}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:0}},[t._v("关闭")])],1)],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreMode"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销模式：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"全员分销指注册用户自动成员分销员，指定分销指需要满足一定条件才能成为分销员",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{on:{input:t.modeChanged},model:{value:t.promoterForm.retailStoreMode,callback:function(e){t.$set(t.promoterForm,"retailStoreMode",e)},expression:"promoterForm.retailStoreMode"}},[r("el-radio",{attrs:{label:0}},[t._v("全员分销")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("指定分销")])],1)],1),t._v(" "),t.showRetailStoreLine?r("el-form-item",{attrs:{prop:"retailStoreLine"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("满额分销最低金额：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"满额分销满足金额开通分销权限",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{placeholder:"满额分销满足金额开通分销权限",min:-1,step:1},nativeOn:{keydown:function(e){return t.channelInputLimit(e)}},model:{value:t.promoterForm.retailStoreLine,callback:function(e){t.$set(t.promoterForm,"retailStoreLine",e)},expression:"promoterForm.retailStoreLine"}})],1):t._e(),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreBindingType"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销关系绑定：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"所有用户”指所有没有上级推广人的用户，“新用户”指新注册的用户",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.promoterForm.retailStoreBindingType,callback:function(e){t.$set(t.promoterForm,"retailStoreBindingType",e)},expression:"promoterForm.retailStoreBindingType"}},[r("el-radio",{attrs:{label:0}},[t._v("所有用户")]),t._v(" "),r("el-radio",{attrs:{label:1}},[t._v("新用户")])],1)],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreBubbleSwitch"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销气泡：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"基础商品详情页分销气泡功能开启关闭",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.promoterForm.retailStoreBubbleSwitch,callback:function(e){t.$set(t.promoterForm,"retailStoreBubbleSwitch",e)},expression:"promoterForm.retailStoreBubbleSwitch"}},[r("el-radio",{attrs:{label:1}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:0}},[t._v("关闭")])],1)],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreBrokerageFirstRatio"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("一级返佣比例：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{"step-strictly":"",min:0,max:100,placeholder:"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%"},model:{value:t.promoterForm.retailStoreBrokerageFirstRatio,callback:function(e){t.$set(t.promoterForm,"retailStoreBrokerageFirstRatio",e)},expression:"promoterForm.retailStoreBrokerageFirstRatio"}}),t._v(" "),r("span",[t._v("%")])],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreBrokerageSecondRatio"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("二级返佣比例：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"订单交易成功后给上级返佣的比例,例:5 = 反订单金额的5%，返佣比例之和不能大于项目文件配置的佣金返佣比例和上限",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{"step-strictly":"",min:0,max:100,placeholder:"订单交易成功后给上级返佣的比例,例:5 = 反订单金额的5%，返佣比例之和不能大于项目文件配置的佣金返佣比例和上限"},model:{value:t.promoterForm.retailStoreBrokerageSecondRatio,callback:function(e){t.$set(t.promoterForm,"retailStoreBrokerageSecondRatio",e)},expression:"promoterForm.retailStoreBrokerageSecondRatio"}}),t._v(" "),r("span",[t._v("%")])],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreExtractMinPrice"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("提现最低金额：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"用户提现最低金额",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{min:0,step:1,placeholder:"用户提现最低金额"},model:{value:t.promoterForm.retailStoreExtractMinPrice,callback:function(e){t.$set(t.promoterForm,"retailStoreExtractMinPrice",e)},expression:"promoterForm.retailStoreExtractMinPrice"}})],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreExtractBank"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("提现银行卡：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"提现银行卡，每个银行换行",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("keyword",{staticStyle:{width:"600px"},attrs:{labelarr:t.labelarr,type:t.keywordType},on:{getLabelarr:t.getLabelarr}})],1),t._v(" "),r("el-form-item",{attrs:{prop:"retailStoreBrokerageFreezingTime"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("冻结时间：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"佣金冻结时间(天)",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{min:0,placeholder:"佣金冻结时间(天)"},model:{value:t.promoterForm.retailStoreBrokerageFreezingTime,callback:function(e){t.$set(t.promoterForm,"retailStoreBrokerageFreezingTime",e)},expression:"promoterForm.retailStoreBrokerageFreezingTime"}})],1),t._v(" "),r("el-form-item",[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:retail:store:config:save"],expression:"['platform:retail:store:config:save']"}],attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.submitForm("promoterForm")}}},[t._v("提交")])],1)],1)],1)],1)},o=[],l=r("21d2"),i=r("fca7"),n=r("e350"),s=r("61f7"),c={name:"Index",data:function(){return{keywordType:"textarea",labelarr:[],promoterForm:{},loading:!0,rules:{retailStoreSwitch:[{required:!0,message:"请选择是否启用分销",trigger:"change"}],retailStoreBrokerageFirstRatio:[{required:!0,message:"请输入一级返佣比例",trigger:"blur"}],retailStoreBrokerageSecondRatio:[{required:!0,message:"请输入二级返佣比例",trigger:"blur"}]},showRetailStoreLine:!1}},mounted:function(){this.getDetal()},methods:{checkPermi:n["a"],getLabelarr:function(t){this.labelarr=t},channelInputLimit:function(t){var e=t.key;return"e"!==e&&"."!==e||(t.returnValue=!1,!1)},getDetal:function(){var t=this;this.loading=!0,Object(l["a"])().then((function(e){t.loading=!1,t.modeChanged(e.retailStoreMode),t.promoterForm=e,t.labelarr=e.retailStoreExtractBank?e.retailStoreExtractBank.split(","):[]})).catch((function(e){t.$message.error(e.message)}))},submitForm:Object(s["a"])((function(t){var e=this;this.$refs[t].validate((function(t){return!!t&&(i["Add"](e.promoterForm.retailStoreBrokerageFirstRatio,e.promoterForm.retailStoreBrokerageSecondRatio)>100?e.$message.warning("返佣比例相加不能超过100%"):(e.loading=!0,e.promoterForm.retailStoreExtractBank=e.labelarr.join(","),void Object(l["b"])(e.promoterForm).then((function(t){e.loading=!1,e.$message.success("提交成功")})).catch((function(t){e.loading=!1}))))}))})),modeChanged:function(t){this.showRetailStoreLine=1==t}}},m=c,p=(r("aa59"),r("2877")),u=Object(p["a"])(m,a,o,!1,null,"5446b9eb",null);e["default"]=u.exports},aa59:function(t,e,r){"use strict";r("e06e")},e06e:function(t,e,r){}}]);