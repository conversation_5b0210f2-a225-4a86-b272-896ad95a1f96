(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b5a55b0e"],{7412:function(t,e,a){},"89bd":function(t,e,a){"use strict";a("7412")},"93eb":function(t,e,a){"use strict";a("ebbb")},bba1:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:brand:add"],expression:"['platform:product:brand:add']"}],attrs:{size:"small",type:"primary"},on:{click:t.onAdd}},[t._v("添加品牌")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"small","row-key":"brand_id","default-expand-all":!1,"tree-props":{children:"children",hasChildren:"hasChildren"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),t._v(" "),a("el-table-column",{attrs:{label:"品牌名称",prop:"name","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"分类图标","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"demo-image__preview"},[e.row.icon?a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.icon,"preview-src-list":[e.row.icon]}}):a("img",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.defaultImg,alt:""}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"是否显示","min-width":"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["platform:product:brand:show:status"])?[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"显示","inactive-text":"隐藏"},on:{change:function(a){return t.onchangeIsShow(e.row)}},model:{value:e.row.isShow,callback:function(a){t.$set(e.row,"isShow",a)},expression:"scope.row.isShow"}})]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:brand:update"],expression:"['platform:product:brand:update']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.onEdit(e.row)}}},[t._v("编辑")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:product:brand:delete"],expression:"['platform:product:brand:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelete(e.row.id,e.$index)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("creat-band",{ref:"creatBands",attrs:{editData:t.editData},on:{getList:t.getList}})],1)},i=[],o=a("c4c8"),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.dialogVisible?a("el-dialog",{attrs:{title:"品牌",visible:t.dialogVisible,"before-close":t.handleClose,closeOnClickModal:!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingFrom,expression:"loadingFrom"}],ref:"dataForm",attrs:{model:t.dataForm,"label-width":"100px",rules:t.rules}},[a("el-form-item",{attrs:{label:"品牌名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入品牌名称"},model:{value:t.dataForm.name,callback:function(e){t.$set(t.dataForm,"name",e)},expression:"dataForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品分类",prop:"categoryIdData"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.adminProductClassify,props:t.categoryProps},model:{value:t.dataForm.categoryIdData,callback:function(e){t.$set(t.dataForm,"categoryIdData",e)},expression:"dataForm.categoryIdData"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"品牌图标(180*180)"}},[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.dataForm.icon?a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.dataForm.icon}})]):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),a("el-form-item",{attrs:{label:"排序",prop:"sort"}},[a("el-input-number",{attrs:{min:t.$constants.NUM_Range.min,max:t.$constants.NUM_Range.max},model:{value:t.dataForm.sort,callback:function(e){t.$set(t.dataForm,"sort",e)},expression:"dataForm.sort"}})],1)],1):t._e(),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){return t.handleClose("dataForm")}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.onsubmit("dataForm")}}},[t._v("确 定")])],1)],1):t._e()},s=[],l=a("2f62");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function d(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function u(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?d(Object(a),!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function m(t,e,a){return e=p(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function p(t){var e=f(t,"string");return"symbol"===c(e)?e:String(e)}function f(t,e){if("object"!==c(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var b={name:"creatClassify",props:{editData:{type:Object,default:function(){return{}}}},computed:u({},Object(l["b"])(["adminProductClassify"])),data:function(){return{categoryProps:{value:"id",label:"name",children:"childList",expandTrigger:"hover",checkStrictly:!1,emitPath:!1,multiple:!0},dialogVisible:!1,treeList:[],loading:!1,loadingFrom:!1,rules:{name:[{required:!0,message:"请输入品牌名称",trigger:"blur"}],categoryIdData:[{required:!0,message:"请选择商品分类",trigger:"change"}]},dataForm:u({},this.editData)}},watch:{editData:{handler:function(t){t.categoryIds&&(t.categoryIdData=t.categoryIds.split(",")),t.sort=t.sort?t.sort:0,t.icon=t.icon?t.icon:"",this.dataForm=u({},t)},deep:!0}},methods:{modalPicTap:function(t){var e=this;this.$modalUpload((function(t){e.dataForm.icon=t[0].sattDir}),t,"product")},handleClose:function(){var t=this;this.$refs["dataForm"].resetFields(),this.$nextTick((function(){t.dialogVisible=!1}))},onClose:function(){var t=this;this.$refs["dataForm"].resetFields(),this.$nextTick((function(){t.dialogVisible=!1})),this.loading=!1,this.$emit("getList"),localStorage.removeItem("productBrand")},onsubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loading=!0,e.dataForm.categoryIds=e.dataForm.categoryIdData.toString(),e.dataForm.id?o["f"](e.dataForm).then((function(t){e.$message.success("操作成功"),e.onClose()})).catch((function(){e.loading=!1})):o["a"](e.dataForm).then((function(t){e.$message.success("操作成功"),e.onClose()})).catch((function(){e.loading=!1}))}))}}},h=b,g=(a("89bd"),a("2877")),v=Object(g["a"])(h,n,s,!1,null,"4b4ea27a",null),y=v.exports,w=a("e350");function P(t){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function O(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function S(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?O(Object(a),!0).forEach((function(e){_(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):O(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function _(t,e,a){return e=F(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function F(t){var e=j(t,"string");return"symbol"===P(e)?e:String(e)}function j(t,e){if("object"!==P(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==P(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var x={name:"BrandList",components:{creatBand:y},computed:S({},Object(l["b"])(["adminProductClassify"])),data:function(){return{props:{value:"store_brand_category_id",label:"cate_name",children:"children",emitPath:!1},defaultImg:a("cf6b"),isChecked:!1,listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20},editData:{}}},mounted:function(){this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify"),this.getList()},methods:{checkPermi:w["a"],getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,o["d"](this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error(t.message)}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},onAdd:function(){this.editData={},this.$refs.creatBands.dialogVisible=!0},onEdit:function(t){this.editData=t,this.$refs.creatBands.dialogVisible=!0},handleDelete:function(t,e){var a=this;this.$modalSure().then((function(){o["b"](t).then((function(t){a.$message.success("删除成功"),a.$store.commit("product/SET_ProductBrand",[]),a.getList()}))})).catch((function(){}))},onchangeIsShow:function(t){var e=this;o["e"](t.id).then((function(t){e.$message.success("操作成功"),e.$store.commit("product/SET_ProductBrand",[]),e.getList()}))}}},D=x,C=(a("93eb"),Object(g["a"])(D,r,i,!1,null,"bf6e9ca6",null));e["default"]=C.exports},ebbb:function(t,e,a){}}]);