<template>
  <div class="divBox" style="padding-bottom: 0">
    <el-row :gutter="20" class="baseInfo">
      <el-col v-bind="grid" class="ivu-mb">
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="acea-row row-between-wrapper">
            <div class="acea-row align-center">
              <span class="main_tit">总数据</span>
            </div>
          </div>
          <div class="container" v-if="viewData">
            <div class="con_left">
              <span class="content-number spBlock m-b-15">{{ viewData.userNum  || 0 }}</span>
              <span class="main_tit">全部用户</span>
            </div>
            <div class="line"></div>
            <div class="con_right">
              <span class="content-number spBlock m-b-15">{{ viewData.merchantNum  || 0 }}</span>
              <span class="main_tit">全部商户</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="grid" class="ivu-mb">
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="acea-row row-between-wrapper">
            <div class="acea-row align-center">
              <!-- <div class="main_badge">
                <span class="iconfont iconxinzengyonghu"></span>
              </div> -->
              <span class="main_tit">今日新增商户数</span>
            </div>
            <el-tag type="primary">今日</el-tag>
          </div>
          <div class="content" v-if="viewData">
            <span class="content-number spBlock my15">{{ viewData.todayNewUserNum || 0 }}</span>
            <el-divider></el-divider>
            <div class="acea-row row-between-wrapper">
              <span class="content-time">昨日数据</span>
              <span class="content-time">{{ viewData.yesterdayNewMerchantNum || 0 }} 人</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="grid" class="ivu-mb">
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="acea-row row-between-wrapper">
            <div class="acea-row align-center">
              <!-- <div class="main_badge">
                <span class="iconfont iconxinzengyonghu"></span>
              </div> -->
              <span class="main_tit">今日新增用户数</span>
            </div>
            <el-tag type="primary">今日</el-tag>
          </div>
          <div class="content" v-if="viewData">
            <span class="content-number spBlock my15">{{ viewData.todayNewUserNum || 0 }}</span>
            <el-divider></el-divider>
            <div class="acea-row row-between-wrapper">
              <span class="content-time">昨日数据</span>
              <span class="content-time">{{ viewData.yesterdayNewUserNum || 0 }} 人</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="grid" class="ivu-mb">
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="acea-row row-between-wrapper">
            <div class="acea-row align-center">
              <!-- <div class="main_badge">
                <span class="iconfont icondingdan"></span>
              </div> -->
              <span class="main_tit">订单量</span>
            </div>
            <el-tag type="primary">今日</el-tag>
          </div>
          <div class="content" v-if="viewData">
            <span class="content-number spBlock my15">{{ viewData.orderNum || 0 }}</span>
            <el-divider></el-divider>
            <div class="acea-row row-between-wrapper">
              <span class="content-time">昨日数据</span>
              <span class="content-time">{{ viewData.yesterdayOrderNum || 0 }}单</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="grid" class="ivu-mb">
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="acea-row row-between-wrapper">
            <div class="acea-row align-center">
              <!-- <div class="main_badge">
                <span class="iconfont iconyonghu"></span>
              </div> -->
              <span class="main_tit">用户访问量</span>
            </div>
            <el-tag type="primary">今日</el-tag>
          </div>
          <div class="content" v-if="viewData">
            <span class="content-number spBlock my15">{{ viewData.pageviews || 0 }}</span>
            <el-divider></el-divider>
            <div class="acea-row row-between-wrapper">
              <span class="content-time">昨日数据</span>
              <span class="content-time">{{ viewData.yesterdayPageviews || 0 }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="grid" class="ivu-mb">
        <el-card :bordered="false" dis-hover :padding="12">
          <div class="acea-row row-between-wrapper">
            <div class="acea-row align-center">
              <!-- <div class="main_badge">
                <span class="iconfont iconxiaoshoue"></span>
              </div> -->
              <span class="main_tit">销售额</span>
            </div>
            <el-tag type="primary">今日</el-tag>
          </div>
          <div class="content" v-if="viewData">
            <span class="content-number spBlock my15">{{ viewData.sales }}</span>
            <el-divider></el-divider>
            <div class="acea-row row-between-wrapper">
              <span class="content-time">昨日数据</span>
              <span class="content-time">{{ viewData.yesterdaySales || 0 }} 元</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { viewModelApi } from '@/api/dashboard';
export default {
  data() {
    return {
      grid: { xl: 6, lg: 8, md: 12, sm: 12, xs: 24 },
      viewData: {},
    };
  },
  methods: {
    statisticsOrder() {
      viewModelApi().then(async (res) => {
        this.viewData = res;
      });
    },
  },
  mounted() {
    this.statisticsOrder();
  },
};
</script>
<style scoped lang="scss">
.ivu-mb {
  height: 173px;
  margin-bottom: 20px;
}
.up,
.el-icon-caret-top {
  color: #f5222d;
  font-size: 12px;
  opacity: 1 !important;
}

.down,
.el-icon-caret-bottom {
  color: #39c15b;
  font-size: 12px;
  /*opacity: 100% !important;*/
}
.main_tit {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}
.content-time {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
.main_badge {
  width: 30px;
  height: 30px;
  border-radius: 5px;
  margin-right: 10px;
  background: #2c90ff;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.my15 {
  margin: 15px 0 15px;
}
.align-center {
  align-items: center;
}
.baseInfo {
  ::v-deep .el-card__header {
    padding: 15px 20px !important;
  }
}

.content {
  &-number {
    font-size: 30px;
    font-weight: 600;
    font-family: PingFangSC-Semibold, PingFang SC;
    color: #333;
  }
  &-time {
    font-size: 14px;
    color: #333333;
    font-weight: 400;
  }
}
.p-10 {
  padding: 10px 0;
}
.p-b-32 {
  padding-bottom: 32px;
}
.container {
  display: flex;
  justify-content: space-evenly;
  padding: 17px 0;
  .con_left,.con_right {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .line {
    width: 1px;
    height: 80px;
    background: #eee;
  }
}
.m-b-15 {
  margin-bottom: 15px;
}
</style>
