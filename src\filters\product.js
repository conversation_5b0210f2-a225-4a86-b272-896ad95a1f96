// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 审核状态
 */
export function auditStatusFilter(status) {
  const statusMap = {
    0: '无需审核',
    1: '待审核',
    2: '审核成功',
    3: '审核拒绝',
  };
  return statusMap[status];
}

/**
 * @description 视频号草稿商品微信审核状态
 */
export function editStatusFilter(status) {
  const statusMap = {
    '-1': '违规/风控系统下',
    '-2': '平台下架',
    '-3': '商家下架',
    1: '未审核',
    2: '审核中',
    3: '审核失败',
    4: '审核成功',
  };
  return statusMap[status];
}

/**
 * @description 视频号草稿商品平台审核状态
 */
export function platformStatusFilter(status) {
  const statusMap = {
    1: '未审核',
    2: '审核中',
    3: '审核失败',
    4: '审核成功',
  };
  return statusMap[status];
}

/**
 * @description 视频号正式商品状态
 */
export function videoStatusFilter(status) {
  const statusMap = {
    0: '初始值',
    5: '上架',
    11: '自主下架',
    13: '违规下架/风控系统下架',
  };
  return statusMap[status];
}

/**
 * @description 视频号 微信商品类型资质类型
 */
export function productQualificationTypeFilter(status) {
  const statusMap = {
    0: '不需要',
    1: '必填',
    2: '选填',
    null: '无要求',
  };
  return statusMap[status];
}
