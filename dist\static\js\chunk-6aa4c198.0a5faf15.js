(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6aa4c198"],{"2eb3":function(t,e,a){"use strict";a.d(e,"c",(function(){return o})),a.d(e,"d",(function(){return l})),a.d(e,"b",(function(){return r})),a.d(e,"e",(function(){return i})),a.d(e,"m",(function(){return u})),a.d(e,"l",(function(){return d})),a.d(e,"i",(function(){return s})),a.d(e,"f",(function(){return m})),a.d(e,"g",(function(){return c})),a.d(e,"h",(function(){return p})),a.d(e,"j",(function(){return h})),a.d(e,"k",(function(){return f})),a.d(e,"a",(function(){return b}));var n=a("b775");function o(t){var e={id:t.id};return Object(n["a"])({url:"/admin/platform/admin/delete",method:"GET",params:e})}function l(t){return Object(n["a"])({url:"/admin/platform/admin/list",method:"GET",params:t})}function r(t){var e={account:t.account,level:t.level,pwd:t.pwd,realName:t.realName,roles:t.roles.join(","),status:t.status,phone:t.phone};return Object(n["a"])({url:"/admin/platform/admin/save",method:"POST",data:e})}function i(t){var e={account:t.account,phone:t.phone,pwd:t.pwd,roles:t.roles,realName:t.realName,status:t.status,id:t.id,isDel:t.isDel};return Object(n["a"])({url:"/admin/platform/admin/update",method:"POST",data:e})}function u(t){return Object(n["a"])({url:"/admin/platform/admin/updateStatus",method:"get",params:t})}function d(t){return Object(n["a"])({url:"/admin/system/admin/update/isSms",method:"get",params:t})}function s(t){var e={menuType:t.menuType,name:t.name};return Object(n["a"])({url:"/admin/platform/menu/list",method:"get",params:e})}function m(t){var e=t;return Object(n["a"])({url:"/admin/platform/menu/add",method:"post",data:e})}function c(t){return Object(n["a"])({url:"/admin/platform/menu/delete/".concat(t),method:"post"})}function p(t){return Object(n["a"])({url:"/admin/platform/menu/info/".concat(t),method:"get"})}function h(t){var e=t;return Object(n["a"])({url:"/admin/platform/menu/update",method:"post",data:e})}function f(t){return Object(n["a"])({url:"/admin/platform/log/sensitive/list",method:"get",params:t})}function b(t){var e={password:t.pwd,realName:t.realName};return Object(n["a"])({url:"/admin/platform/login/admin/update",method:"POST",data:e})}},ec61:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.list,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"adminId",label:"管理员id",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"adminAccount",label:"管理员账号",width:"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"操作状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:e.row.status?"danger":""}},[t._v(t._s(e.row.status?"异常":"正常"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"description",label:"接口描述",width:"140","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"操作时间",width:"120","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"ip",label:"主机地址",width:"120","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"method",label:"方法名称",width:"180","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"url",label:"请求URL",width:"180","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"methodType",label:"请求类型",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"requestMethod",label:"请求方式",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"requestParam",label:"请求参数",width:"150","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"result",label:"返回参数",width:"180","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"errorMsg",label:"错误消息",width:"180","show-overflow-tooltip":!0}})],1),t._v(" "),a("el-pagination",{attrs:{"page-sizes":[20,30,40,60],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)},o=[],l=a("2eb3"),r={data:function(){return{tableData:{},tableFrom:{page:1,limit:20},listLoading:!1}},mounted:function(){this.getLogList()},methods:{getLogList:function(){var t=this;this.listLoading=!0,Object(l["k"])(this.tableFrom).then((function(e){t.tableData=e,t.listLoading=!1}))},handleSizeChange:function(t){this.tableFrom.limit=t,this.getLogList()},pageChange:function(t){this.tableFrom.page=t,this.getLogList()}}},i=r,u=a("2877"),d=Object(u["a"])(i,n,o,!1,null,null,null);e["default"]=d.exports}}]);