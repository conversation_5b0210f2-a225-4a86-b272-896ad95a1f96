(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e8d27040","chunk-3ae1e5a9"],{"40d2":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div")},n=[],l=i("2877"),s={},r=Object(l["a"])(s,a,n,!1,null,null,null);e["a"]=r.exports},d1da:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"container"},[i("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[i("el-form-item",{attrs:{label:"关键字"}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入id，名称，描述",clearable:"",size:"small"},model:{value:t.listPram.keywords,callback:function(e){t.$set(t.listPram,"keywords",e)},expression:"listPram.keywords"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handlerSearch},slot:"append"})],1)],1),t._v(" "),t.selectModel?i("el-form-item",[i("el-button",{attrs:{type:"primary",disabled:!t.selectedConfigData.id},on:{click:t.handlerConfimSelect}},[t._v("确定选择")])],1):t._e()],1)],1),t._v(" "),t.selectModel?t._e():i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:form:save"],expression:"['platform:system:form:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerEditData({},0)}}},[t._v("创建表单")])],1),t._v(" "),i("el-table",{staticClass:"table",attrs:{data:t.dataList.list,"highlight-current-row":t.selectModel,size:"mini","header-cell-style":{fontWeight:"bold"}},on:{"current-change":t.handleCurrentRowChange}},[i("el-table-column",{attrs:{label:"ID",prop:"id",width:"80"}}),t._v(" "),i("el-table-column",{attrs:{label:"名称",prop:"name","min-width":"180"}}),t._v(" "),i("el-table-column",{attrs:{label:"描述",prop:"info","min-width":"220"}}),t._v(" "),i("el-table-column",{attrs:{label:"更新时间",prop:"updateTime","min-width":"200"}}),t._v(" "),t.selectModel?t._e():i("el-table-column",{attrs:{label:"操作","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:form:update"],expression:"['platform:system:form:update']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return t.handlerEditData(e.row,1)}}},[t._v("编辑")])]}}],null,!1,3256910730)})],1),t._v(" "),i("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":[10,20,30,40],layout:t.constants.page.layout,total:t.dataList.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),i("el-dialog",{attrs:{visible:t.editDialogConfig.visible,fullscreen:"",title:0===t.editDialogConfig.isCreate?"创建表单":"编辑表单","destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?i("edit",{attrs:{"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.editData},on:{hideDialog:t.handlerHide}}):t._e()],1)],1)},n=[],l=i("92c6"),s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("config-list",{attrs:{"edit-data":t.editData,"is-create":t.isCreate},on:{getFormConfigDataResult:t.handlerGetFormConfigData}})],1)},r=[],o=i("5abd"),d={components:{configList:o["a"]},props:{editData:{type:Object,default:{}},isCreate:{type:Number,default:0}},data:function(){return{}},methods:{handlerGetFormConfigData:function(t){t.id?this.handlerEdit(t):this.handlerSave(t)},handlerSave:function(t){var e=this;l["d"](t).then((function(t){e.$message.success("创建表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))},handlerEdit:function(t){var e=this;l["a"](t).then((function(t){e.$message.success("编辑表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))}}},c=d,u=i("2877"),h=Object(u["a"])(c,s,r,!1,null,"6841b549",null),m=h.exports,f={components:{edit:m},props:{selectModel:{type:Boolean,default:!1}},data:function(){return{constants:this.$constants,listPram:{keywords:null,page:1,limit:10},editDialogConfig:{visible:!1,editData:{},isCreate:0},dataList:{list:[],total:0},selectedConfigData:{}}},mounted:function(){this.handlerGetList(this.listPram)},methods:{handlerSearch:function(){this.listPram.page=1,this.handlerGetList(this.listPram)},handlerGetList:function(t){var e=this;l["c"](t).then((function(t){e.dataList=t}))},handlerEditData:function(t,e){this.editDialogConfig.editData=0===e?{}:t,this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},handlerHide:function(){this.editDialogConfig.editData={},this.editDialogConfig.isCreate=0,this.editDialogConfig.visible=!1,this.handlerGetList(this.listPram)},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetList(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetList(this.listPram)},handleCurrentRowChange:function(t){this.selectedConfigData=t},handlerConfimSelect:function(){this.$emit("selectedRowData",this.selectedConfigData)}}},p=f,g=Object(u["a"])(p,a,n,!1,null,"12386e80",null);e["default"]=g.exports},e395:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{attrs:{slot:"header"},slot:"header"},[i("el-form",{attrs:{inline:""}},[i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerOpenAdd({id:0,name:"顶层目录"})}}},[t._v("添加分类")])],1)],1),t._v(" "),i("el-alert",{attrs:{title:"温馨提示",type:"success",description:"添加一级分类以后，务必添加二级分类并配置表单，否则会出现渲染错误"}})],1),t._v(" "),i("el-table",{ref:"treeList",staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.treeList,"row-key":"id",size:"mini","highlight-current-row":"","tree-props":{children:"child",hasChildren:"hasChildren"}}},[i("el-table-column",{attrs:{prop:"name",label:"分类昵称","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.name)+"\n        ")]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"英文名称","show-overflow-tooltip":"","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.url))])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"已关联的表单","show-overflow-tooltip":"","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.extra))])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"启用状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("filterYesOrNo")(e.row.status)))])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"250",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"text",size:"small",disabled:e.row.pid>0},on:{click:function(i){return t.handlerOpenAdd(e.row)}}},[t._v("添加子目录")]),t._v(" "),i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.handleEditMenu(e.row)}}},[t._v("编辑")]),t._v(" "),i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.handlerOpenFormConfig(e.row)}}},[t._v("配置列表")]),t._v(" "),i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.handleDelMenu(e.row)}}},[t._v("删除")])]}}])})],1)],1),t._v(" "),i("el-dialog",{attrs:{title:0===t.editDialogConfig.isCreate?"添加分类":"编辑分类",visible:t.editDialogConfig.visible,"destroy-on-close":"","close-on-click-modal":!1,width:"700"},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?i("edit",{attrs:{prent:t.editDialogConfig.prent,"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.data,biztype:t.editDialogConfig.biztype,"all-tree-list":t.treeList},on:{hideEditDialog:t.hideEditDialog}}):t._e()],1),t._v(" "),i("el-dialog",{attrs:{title:"选择已配置的表单",visible:t.configFormSelectedDialog.visible,width:"800px"},on:{"update:visible":function(e){return t.$set(t.configFormSelectedDialog,"visible",e)}}},[i("span",{staticClass:"color-red"},[t._v("注意：表单不能重复关联")]),t._v(" "),t.configFormSelectedDialog.visible?i("form-config-list",{attrs:{"select-model":""},on:{selectedRowData:t.handlerSelectedRowData}}):t._e(),t._v(" "),i("el-form",[i("el-form-item",[i("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary"},on:{click:t.handlerAddFormExtra}},[t._v("关联")])],1)],1)],1)],1)},n=[],l=i("651a"),s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"components-container"},[i("el-form",{ref:"editPram",attrs:{model:t.editPram,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"父级"}},[i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.allTreeList,props:t.categoryProps,disabled:""},model:{value:t.editPram.pid,callback:function(e){t.$set(t.editPram,"pid",e)},expression:"editPram.pid"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"分类名称",prop:"name",rules:[{required:!0,message:"请输入分类名称",trigger:["blur","change"]}]}},[i("el-input",{attrs:{placeholder:"分类名称"},model:{value:t.editPram.name,callback:function(e){t.$set(t.editPram,"name",e)},expression:"editPram.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"英文名称",prop:"url",rules:[{required:!0,message:"英文名称不能为空",trigger:["blur","change"]}]}},[i("el-input",{attrs:{placeholder:"URL"},model:{value:t.editPram.url,callback:function(e){t.$set(t.editPram,"url",e)},expression:"editPram.url"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{min:1,max:10},model:{value:t.editPram.sort,callback:function(e){t.$set(t.editPram,"sort",e)},expression:"editPram.sort"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"状态"}},[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:t.editPram.status,callback:function(e){t.$set(t.editPram,"status",e)},expression:"editPram.status"}})],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerSubmit("editPram")}}},[t._v("确定")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:t.close}},[t._v("取消")])],1)],1)],1)},r=[],o=i("fca7"),d=i("61f7"),c={props:{prent:{type:Object,default:0},isCreate:{type:Number,default:0},editData:{type:Object},allTreeList:{type:Array}},data:function(){return{constants:this.$constants,editPram:{extra:null,name:null,pid:null,sort:0,status:!0,type:this.$constants.categoryType[5].value,url:null,id:0},categoryProps:{value:"id",label:"name",children:"child",expandTrigger:"hover",checkStrictly:!0,emitPath:!1},parentOptions:[]}},mounted:function(){this.initEditData()},methods:{close:function(){this.$emit("hideEditDialog")},initEditData:function(){if(this.parentOptions=o["addTreeListLabelForCasCard"](this.allTreeList),1!==this.isCreate){var t=this.prent.id;this.editPram.pid=t}else{var e=this.editData,i=e.extra,a=e.name,n=e.pid,l=e.sort,s=e.status,r=e.type,d=e.url,c=e.id;this.editPram.name=a,this.editPram.pid=n,this.editPram.sort=l,this.editPram.status=s,this.editPram.type=r,this.editPram.url=d,this.editPram.id=c,this.editPram.extra=i}},handlerSubmit:Object(d["a"])((function(t){var e=this;this.$refs[t].validate((function(t){t&&e.handlerSaveOrUpdate(0===e.isCreate)}))})),handlerSaveOrUpdate:function(t){var e=this;t?(this.editPram.pid=this.prent.id,l["a"](this.editPram).then((function(t){e.$emit("hideEditDialog"),e.$message.success("创建分类成功")}))):(this.editPram.pid=Array.isArray(this.editPram.pid)?this.editPram.pid[0]:this.editPram.pid,l["e"](this.editPram).then((function(t){e.$emit("hideEditDialog"),e.$message.success("更新分类成功")})))}}},u=c,h=i("2877"),m=Object(h["a"])(u,s,r,!1,null,"129dc061",null),f=m.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"components-container"},[i("config-list",{attrs:{"prent-data":t.prentData}})],1)},g=[],v=i("5abd"),b={components:{configList:v["a"]},props:{prentData:{type:Object,default:{}}}},C=b,D=Object(h["a"])(C,p,g,!1,null,"34a91c85",null),_=D.exports,y=i("d1da"),P={components:{edit:f,configList:_,formConfigList:y["default"]},props:{},data:function(){return{constants:this.$constants,searchPram:{status:null,type:null},editDialogConfig:{visible:!1,isCreate:0,prent:{},data:{}},treeList:[],listPram:{pid:0,type:this.$constants.categoryType[5].value,status:null,name:null,page:this.$constants.page.page,limit:this.$constants.page.limit[1]},configFormSelectedDialog:{visible:!1,currentData:{}}}},mounted:function(){this.handlerGetTreeList()},methods:{handlerOpenFormConfig:function(t){this.configFormSelectedDialog.currentData=t,this.configFormSelectedDialog.visible=!0},handlerOpenAdd:function(t){this.editDialogConfig.isCreate=0,this.editDialogConfig.prent=t,this.editDialogConfig.data={},this.editDialogConfig.biztype=this.biztype,this.editDialogConfig.visible=!0},handleEditMenu:function(t){this.editDialogConfig.isCreate=1,this.editDialogConfig.data=t,this.editDialogConfig.prent=t,this.editDialogConfig.visible=!0},handleDelMenu:function(t){var e=this;this.$confirm("确定删除当前数据?").then((function(){l["b"](t.id).then((function(t){e.handlerGetTreeList(),e.$message.success("删除成功")}))}))},hideEditDialog:function(){var t=this;setTimeout((function(){t.editDialogConfig.prent={},t.editDialogConfig.type=0,t.editDialogConfig.visible=!1,t.handlerGetTreeList()}),200)},handlerGetTreeList:function(){var t=this,e={type:this.constants.categoryType[5].value,status:-1};l["d"](e).then((function(e){t.treeList=t.handleAddArrt(e)}))},handleAddArrt:function(t){var e=o["addTreeListLabel"](t);return e},handlerSelectedRowData:function(t){this.configFormSelectedDialog.currentData.extra=t.id},handlerAddFormExtra:function(){var t=this;l["e"](this.configFormSelectedDialog.currentData).then((function(e){t.$message.success("关联表单成功"),setTimeout((function(){t.configFormSelectedDialog.visible=!1,t.handlerGetTreeList()}),800)}))}}},w=P,x=Object(h["a"])(w,a,n,!1,null,"1f16608f",null);e["default"]=x.exports}}]);