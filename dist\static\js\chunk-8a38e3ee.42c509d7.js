(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8a38e3ee"],{"54da":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[e.checkPermi(["platform:refund:order:status:num"])?a("el-form-item",{attrs:{label:"订单状态："}},[a("el-radio-group",{attrs:{type:"button"},on:{change:e.seachList},model:{value:e.tableFrom.refundStatus,callback:function(t){e.$set(e.tableFrom,"refundStatus",t)},expression:"tableFrom.refundStatus"}},[a("el-radio-button",{attrs:{label:"9"}},[e._v("全部 "+e._s(e.orderChartType.all)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"0"}},[e._v("待审核 "+e._s(e.orderChartType.await)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"1"}},[e._v("审核未通过 "+e._s(e.orderChartType.reject)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("退款中 "+e._s(e.orderChartType.refunding)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("已退款 "+e._s(e.orderChartType.refunded)+"\n              ")])],1)],1):e._e(),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(t){return e.selectChange(e.tableFrom.dateLimit)}},model:{value:e.tableFrom.dateLimit,callback:function(t){e.$set(e.tableFrom,"dateLimit",t)},expression:"tableFrom.dateLimit"}},e._l(e.fromList.fromTxt,(function(t,i){return a("el-radio-button",{key:i,attrs:{label:t.val}},[e._v(e._s(t.text)+"\n              ")])})),1),e._v(" "),a("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"订单号："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单号",size:"small",clearable:""},model:{value:e.tableFrom.orderNo,callback:function(t){e.$set(e.tableFrom,"orderNo",t)},expression:"tableFrom.orderNo"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"退款单号："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入退款单号",size:"small",clearable:""},model:{value:e.tableFrom.refundOrderNo,callback:function(t){e.$set(e.tableFrom,"refundOrderNo",t)},expression:"tableFrom.refundOrderNo"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"商户名称："}},[a("merchant-name",{on:{getMerId:e.getMerId}})],1)],1)],1)])]),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:e.tableData.data,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"},"row-key":function(e){return e.refundOrderNo}}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand demo-table-expands",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"退款商品总数："}},[a("span",[e._v(e._s(t.row.totalNum))])]),e._v(" "),a("el-form-item",{attrs:{label:"平台备注："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.platformRemark)))])]),e._v(" "),a("el-form-item",{attrs:{label:"商家备注："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.merRemark)))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款信息："}},[a("div",{staticClass:"pup_card flex-column"},[a("span",[e._v("退款原因："+e._s(t.row.refundReasonWap))]),e._v(" "),a("span",[e._v("备注说明："+e._s(t.row.refundReasonWapExplain))]),e._v(" "),a("span",{staticClass:"acea-row"},[e._v("\n                  退款凭证：\n                  "),t.row.refundReasonWapImg?e._l(t.row.refundReasonWapImg.split(","),(function(e,t){return a("div",{key:t,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[a("el-image",{attrs:{src:e,"preview-src-list":[e]}})],1)})):a("span",{staticStyle:{display:"inline-block"}},[e._v("无")])],2)]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.row.refundReason,expression:"props.row.refundReason"}],staticClass:"pup_card flex-column"},[a("span",[e._v("拒绝原因："+e._s(t.row.refundReason))])])])],1)]}}])}),e._v(" "),e.checkedCities.includes("退款单号")?a("el-table-column",{attrs:{label:"退款单号","min-width":"210"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"acea-row"},[a("span",{directives:[{name:"show",rawName:"v-show",value:1===t.row.type,expression:"scope.row.type===1"}],staticClass:"iconfont icon-shipinhao mr5",staticStyle:{color:"#F6AE02"}}),e._v(" "),a("span",{staticStyle:{display:"block"},domProps:{textContent:e._s(t.row.refundOrderNo)}})])]}}],null,!1,2158015220)}):e._e(),e._v(" "),e.checkedCities.includes("订单号")?a("el-table-column",{attrs:{prop:"orderNo",label:"订单号","min-width":"180"}}):e._e(),e._v(" "),e.checkedCities.includes("用户信息")?a("el-table-column",{attrs:{prop:"userNickName",label:"用户信息","min-width":"180"}}):e._e(),e._v(" "),e.checkedCities.includes("商户名称")?a("el-table-column",{attrs:{prop:"merName",label:"商户名称","min-width":"180"}}):e._e(),e._v(" "),e.checkedCities.includes("退款金额")?a("el-table-column",{attrs:{prop:"refundPrice",label:"退款金额","min-width":"100"}}):e._e(),e._v(" "),e.checkedCities.includes("退款状态")?a("el-table-column",{attrs:{label:"退款状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[2===t.row.refundStatus||1===t.row.refundStatus?a("div",{staticClass:"refunding"},[[a("el-popover",{attrs:{trigger:"hover",placement:"left","open-delay":500}},[a("b",{staticClass:"fontColor3",staticStyle:{cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(e._f("refundStatusFilter")(t.row.refundStatus)))]),e._v(" "),a("div",{staticClass:"pup_card flex-column"},[a("span",[e._v("退款原因："+e._s(t.row.refundReasonWap))]),e._v(" "),a("span",[e._v("备注说明："+e._s(t.row.refundReasonWapExplain))]),e._v(" "),a("span",{staticClass:"acea-row"},[e._v("\n                    退款凭证：\n                    "),t.row.refundReasonWapImg?e._l(t.row.refundReasonWapImg.split(","),(function(e,t){return a("div",{key:t,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[a("el-image",{attrs:{src:e,"preview-src-list":[e]}})],1)})):a("span",{staticStyle:{display:"inline-block"}},[e._v("无")])],2)]),e._v(" "),a("div",{staticClass:"pup_card flex-column"},[a("span",[e._v("拒绝原因："+e._s(t.row.refundReason))])])])]],2):a("span",[e._v(e._s(e._f("refundStatusFilter")(t.row.refundStatus)))])]}}],null,!1,3000614675)}):e._e(),e._v(" "),e.checkedCities.includes("创建时间")?a("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"150"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center","render-header":e.renderHeader},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:refund:order:detail"],expression:"['platform:refund:order:detail']"}],attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderDetails(t.row)}}},[e._v("订单详情")]),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:refund:order:mark"],expression:"['platform:refund:order:mark']"}],attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderMark(t.row)}}},[e._v("订单备注")])]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.card_select_show,expression:"card_select_show"}],staticClass:"card_abs"},[[a("div",{staticClass:"cell_ht"},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选\n        ")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.checkSave()}}},[e._v("保存")])],1),e._v(" "),a("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.checkedCities,callback:function(t){e.checkedCities=t},expression:"checkedCities"}},e._l(e.columnData,(function(t){return a("el-checkbox",{key:t,staticClass:"check_cell",attrs:{label:t}},[e._v(e._s(t))])})),1)]],2),e._v(" "),a("details-from",{ref:"orderDetail",attrs:{orderNo:e.orderNo}})],1)},s=[],r=a("f8b7"),l=a("7ff6"),n=a("c4c8"),o=a("e350"),c=a("6ed5"),d={name:"orderRefund",components:{detailsFrom:l["a"],merchantName:c["a"]},data:function(){return{RefuseVisible:!1,RefuseData:{},orderNo:"",refundVisible:!1,refundData:{},dialogVisibleJI:!1,tableDataLog:{data:[],total:0},tableFromLog:{page:1,limit:10,orderNo:0},LogLoading:!1,isCreate:1,editData:null,dialogVisible:!1,tableData:{data:[],total:0},listLoading:!0,tableFrom:{refundStatus:"9",dateLimit:"",orderNo:"",refundOrderNo:"",page:1,limit:20,merId:0},orderChartType:{},timeVal:[],fromList:this.$constants.fromList,selectionList:[],ids:"",orderids:"",cardLists:[],proType:0,active:!1,card_select_show:!1,checkAll:!1,checkedCities:["退款单号","订单号","商户名称","用户信息","退款金额","退款状态","创建时间"],columnData:["退款单号","订单号","商户名称","用户信息","退款金额","退款状态","创建时间"],isIndeterminate:!0}},mounted:function(){this.getList(),this.getOrderStatusNum()},methods:{checkPermi:o["a"],getMerId:function(e){this.tableFrom.merId=e,this.seachList()},resetFormRefundhandler:function(){this.refundVisible=!1},resetFormRefusehand:function(){this.RefuseVisible=!1},resetForm:function(e){this.dialogVisible=!1},seachList:function(){this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onOrderDetails:function(e){this.orderNo=e.orderNo,this.$refs.orderDetail.getDetail(e.orderNo),this.$refs.orderDetail.getRefundOrderDetail(e.refundOrderNo),this.$refs.orderDetail.getOrderInvoiceList(e.orderNo),this.$refs.orderDetail.dialogVisible=!0},getDetail:function(e){var t=this;this.loading=!0,Object(r["b"])(e).then((function(e){t.orderDatalist=e,t.loading=!1})).catch((function(){t.orderDatalist=null,t.loading=!1}))},onOrderLog:function(e){var t=this;this.dialogVisibleJI=!0,this.LogLoading=!0,this.tableFromLog.orderNo=e,Object(r["e"])(this.tableFromLog).then((function(e){t.tableDataLog.data=e.list,t.tableDataLog.total=e.total,t.LogLoading=!1})).catch((function(){t.LogLoading=!1}))},pageChangeLog:function(e){this.tableFromLog.page=e},handleSizeChangeLog:function(e){this.tableFromLog.limit=e},handleClose:function(){this.dialogVisible=!1},onOrderMark:function(e){var t=this;this.$prompt("退款单备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入退款单备注",inputType:"textarea",inputValue:e.platformRemark,inputPlaceholder:"请输入退款单备注",inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(a){var i=a.value;Object(r["h"])({remark:i,refundOrderNo:e.refundOrderNo}).then((function(){t.$message.success("操作成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},handleSelectionChange:function(e){this.selectionList=e;var t=[];this.selectionList.map((function(e){t.push(e.orderNo)})),this.ids=t.join(",")},selectChange:function(e){this.timeVal=[],this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onchangeTime:function(e){this.timeVal=e,this.tableFrom.dateLimit=e?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},getList:function(){var e=this;this.listLoading=!0,Object(r["g"])(this.tableFrom).then((function(t){e.tableData.data=t.list||[],e.tableData.total=t.total,e.listLoading=!1,e.checkedCities=e.$cache.local.has("order_stroge")?e.$cache.local.getJSON("order_stroge"):e.checkedCities})).catch((function(){e.listLoading=!1}))},getOrderStatusNum:function(){var e=this;Object(r["j"])({dateLimit:this.tableFrom.dateLimit}).then((function(t){e.orderChartType=t}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},exports:function(){var e={dateLimit:this.tableFrom.dateLimit,orderNo:this.tableFrom.orderNo,refundStatus:this.tableFrom.status,type:this.tableFrom.type};Object(n["m"])(e).then((function(e){window.open(e.fileName)}))},renderHeader:function(e){var t=this;return e("p",[e("span",{style:"padding-right:5px;"},["操作"]),e("i",{class:"el-icon-setting",on:{click:function(){return t.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(e){this.checkedCities=e?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.columnData.length,this.isIndeterminate=t>0&&t<this.columnData.length},checkSave:function(){this.$set(this,"card_select_show",!1),this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("order_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)}}},u=d,h=(a("7ffd"),a("2877")),m=Object(h["a"])(u,i,s,!1,null,"7d9b9cba",null);t["default"]=m.exports},"7ffd":function(e,t,a){"use strict";a("90e7")},"90e7":function(e,t,a){}}]);