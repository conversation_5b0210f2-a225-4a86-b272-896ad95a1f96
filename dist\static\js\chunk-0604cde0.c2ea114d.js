(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0604cde0"],{1861:function(t,e,i){"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}var s,n,a,o,l;function c(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=A(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var r=0,s=function(){};return{s:s,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,a=!0,o=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return a=t.done,t},e:function(t){o=!0,n=t},f:function(){try{a||null==i.return||i.return()}finally{if(o)throw n}}}}function h(t){return d(t)||p(t)||A(t)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function d(t){if(Array.isArray(t))return S(t)}function f(){return f="undefined"!==typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,i){var r=m(t,e);if(r){var s=Object.getOwnPropertyDescriptor(r,e);return s.get?s.get.call(arguments.length<3?t:i):s.value}},f.apply(this,arguments)}function m(t,e){while(!Object.prototype.hasOwnProperty.call(t,e))if(t=k(t),null===t)break;return t}function y(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}function v(t,e){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},v(t,e)}function g(t){var e=P();return function(){var i,r=k(t);if(e){var s=k(this).constructor;i=Reflect.construct(r,arguments,s)}else i=r.apply(this,arguments);return x(this,i)}}function x(t,e){if(e&&("object"===r(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return b(t)}function b(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function P(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function T(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function w(t,e){return I(t)||C(t,e)||A(t,e)||E()}function E(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function A(t,e){if(t){if("string"===typeof t)return S(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?S(t,e):void 0}}function S(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=new Array(e);i<e;i++)r[i]=t[i];return r}function C(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var r,s,n,a,o=[],l=!0,c=!1;try{if(n=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;l=!1}else for(;!(l=(r=n.call(i)).done)&&(o.push(r.value),o.length!==e);l=!0);}catch(h){c=!0,s=h}finally{try{if(!l&&null!=i.return&&(a=i.return(),Object(a)!==a))return}finally{if(c)throw s}}return o}}function I(t){if(Array.isArray(t))return t}function N(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,O(r.key),r)}}function D(t,e,i){return e&&N(t.prototype,e),i&&N(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function O(t){var e=M(t,"string");return"symbol"===r(e)?e:String(e)}function M(t,e){if("object"!==r(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!==r(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function L(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function F(t,e){if(null==t)return{};var i,r,s={},n=Object.keys(t);for(r=0;r<n.length;r++)i=n[r],e.indexOf(i)>=0||(s[i]=t[i]);return s}Object.defineProperty(e,"__esModule",{value:!0});var _=D((function t(e,i,r){L(this,t),this.line=void 0,this.column=void 0,this.index=void 0,this.line=e,this.column=i,this.index=r})),B=D((function t(e,i){L(this,t),this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=e,this.end=i}));function j(t,e){var i=t.line,r=t.column,s=t.index;return new _(i,r+e,s+e)}var R={SyntaxError:"BABEL_PARSER_SYNTAX_ERROR",SourceTypeModuleError:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"},U=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.length-1;return{get:function(){return t.reduce((function(t,e){return t[e]}),this)},set:function(i){t.reduce((function(t,r,s){return s===e?t[r]=i:t[r]}),this)}}},z=function(t,e,i){return Object.keys(i).map((function(t){return[t,i[t]]})).filter((function(t){var e=w(t,2),i=e[1];return!!i})).map((function(t){var e=w(t,2),i=e[0],r=e[1];return[i,"function"===typeof r?{value:r,enumerable:!1}:"string"===typeof r.reflect?Object.assign({},r,U(r.reflect.split("."))):r]})).reduce((function(t,e){var i=w(e,2),r=i[0],s=i[1];return Object.defineProperty(t,r,Object.assign({configurable:!0},s))}),Object.assign(new t,e))},H={ImportMetaOutsideModule:{message:"import.meta may appear only with 'sourceType: \"module\"'",code:R.SourceTypeModuleError},ImportOutsideModule:{message:"'import' and 'export' may appear only with 'sourceType: \"module\"'",code:R.SourceTypeModuleError}},V={ArrayPattern:"array destructuring pattern",AssignmentExpression:"assignment expression",AssignmentPattern:"assignment expression",ArrowFunctionExpression:"arrow function expression",ConditionalExpression:"conditional expression",CatchClause:"catch clause",ForOfStatement:"for-of statement",ForInStatement:"for-in statement",ForStatement:"for-loop",FormalParameters:"function parameter list",Identifier:"identifier",ImportSpecifier:"import specifier",ImportDefaultSpecifier:"import default specifier",ImportNamespaceSpecifier:"import namespace specifier",ObjectPattern:"object destructuring pattern",ParenthesizedExpression:"parenthesized expression",RestElement:"rest element",UpdateExpression:{true:"prefix operation",false:"postfix operation"},VariableDeclarator:"variable declaration",YieldExpression:"yield expression"},q=function(t){var e=t.type,i=t.prefix;return"UpdateExpression"===e?V.UpdateExpression[String(i)]:V[e]},K={AccessorIsGenerator:function(t){var e=t.kind;return"A ".concat(e,"ter cannot be a generator.")},ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncFunction:"'await' is only allowed within async functions.",BadGetterArity:"A 'get' accesor must not have any formal parameters.",BadSetterArity:"A 'set' accesor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accesor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:function(t){var e=t.kind;return"Missing initializer in ".concat(e," declaration.")},DecoratorArgumentsOutsideParentheses:"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. You can set the 'decoratorsBeforeExport' option to false to use the 'export @decorator class {}' syntax.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Using the export keyword between a decorator and a class is not allowed. Please use `export @dec class` instead.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:function(t){var e=t.exportName;return"`".concat(e,"` has already been exported. Exported identifiers must be unique.")},DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:function(t){var e=t.localName,i=t.exportName;return"A string literal cannot be used as an exported binding without `from`.\n- Did you mean `export { '".concat(e,"' as '").concat(i,"' } from 'some-module'`?")},ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:function(t){var e=t.type;return"'".concat("ForInStatement"===e?"for-in":"for-of","' loop variable declaration may not have an initializer.")},ForInUsing:"For-in loop may not start with 'using' declaration.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:function(t){var e=t.type;return"Unsyntactic ".concat("BreakStatement"===e?"break":"continue",".")},IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportBindingIsString:function(t){var e=t.importName;return'A string literal cannot be used as an imported binding.\n- Did you mean `import { "'.concat(e,'" as foo }`?')},ImportCallArgumentTrailingComma:"Trailing comma is disallowed inside import(...) arguments.",ImportCallArity:function(t){var e=t.maxArgumentCount;return"`import()` requires exactly ".concat(1===e?"one argument":"one or two arguments",".")},ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",ImportJSONBindingNotDefault:"A JSON module can only be imported with `default`.",ImportReflectionHasAssertion:"`import module x` cannot have assertions.",ImportReflectionNotBinding:'Only `import module x from "./module"` is valid.',IncompatibleRegExpUVFlags:"The 'u' and 'v' regular expression flags cannot be enabled at the same time.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidCoverInitializedName:"Invalid shorthand property initializer.",InvalidDecimal:"Invalid decimal.",InvalidDigit:function(t){var e=t.radix;return"Expected number in radix ".concat(e,".")},InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:function(t){var e=t.reservedWord;return"Escape sequence in keyword ".concat(e,".")},InvalidIdentifier:function(t){var e=t.identifierName;return"Invalid identifier ".concat(e,".")},InvalidLhs:function(t){var e=t.ancestor;return"Invalid left-hand side in ".concat(q(e),".")},InvalidLhsBinding:function(t){var e=t.ancestor;return"Binding invalid left-hand side in ".concat(q(e),".")},InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:function(t){var e=t.unexpected;return"Unexpected character '".concat(e,"'.")},InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:function(t){var e=t.identifierName;return"Private name #".concat(e," is not defined.")},InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:function(t){var e=t.labelName;return"Label '".concat(e,"' is already declared.")},LetInLexicalBinding:"'let' is not allowed to be used as a name in 'let' or 'const' declarations.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingPlugin:function(t){var e=t.missingPlugin;return"This experimental syntax requires enabling the parser plugin: ".concat(e.map((function(t){return JSON.stringify(t)})).join(", "),".")},MissingOneOfPlugins:function(t){var e=t.missingPlugin;return"This experimental syntax requires enabling one of the following parser plugin(s): ".concat(e.map((function(t){return JSON.stringify(t)})).join(", "),".")},MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:function(t){var e=t.key;return'Duplicate key "'.concat(e,'" is not allowed in module attributes.')},ModuleExportNameHasLoneSurrogate:function(t){var e=t.surrogateCharCode;return"An export name cannot include a lone surrogate, found '\\u".concat(e.toString(16),"'.")},ModuleExportUndefined:function(t){var e=t.localName;return"Export '".concat(e,"' is not defined.")},MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PrivateInExpectedIn:function(t){var e=t.identifierName;return"Private names are only allowed in property accesses (`obj.#".concat(e,"`) or in `in` expressions (`#").concat(e," in obj`).")},PrivateNameRedeclaration:function(t){var e=t.identifierName;return"Duplicate private name #".concat(e,".")},RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",StaticPrototype:"Classes may not have static property named prototype.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:function(t){var e=t.keyword;return"Unexpected keyword '".concat(e,"'.")},UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Unexpected private name.",UnexpectedReservedWord:function(t){var e=t.reservedWord;return"Unexpected reserved word '".concat(e,"'.")},UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:function(t){var e=t.expected,i=t.unexpected;return"Unexpected token".concat(i?" '".concat(i,"'."):"").concat(e?', expected "'.concat(e,'"'):"")},UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnexpectedUsingDeclaration:"Using declaration cannot appear in the top level when source type is `script`.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:function(t){var e=t.target,i=t.onlyValidPropertyName;return"The only valid meta property for ".concat(e," is ").concat(e,".").concat(i,".")},UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",UsingDeclarationHasBindingPattern:"Using declaration cannot have destructuring patterns.",VarRedeclaration:function(t){var e=t.identifierName;return"Identifier '".concat(e,"' has already been declared.")},YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."},X={StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:function(t){var e=t.referenceName;return"Assigning to '".concat(e,"' in strict mode.")},StrictEvalArgumentsBinding:function(t){var e=t.bindingName;return"Binding '".concat(e,"' in strict mode.")},StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode."},W=new Set(["ArrowFunctionExpression","AssignmentExpression","ConditionalExpression","YieldExpression"]),J={PipeBodyIsTighter:"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.',PipeTopicUnbound:"Topic reference is unbound; it must be inside a pipe body.",PipeTopicUnconfiguredToken:function(t){var e=t.token;return"Invalid topic token ".concat(e,". In order to use ").concat(e,' as a topic reference, the pipelineOperator plugin must be configured with { "proposal": "hack", "topicToken": "').concat(e,'" }.')},PipeTopicUnused:"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.",PipeUnparenthesizedBody:function(t){var e=t.type;return"Hack-style pipe body cannot be an unparenthesized ".concat(q({type:e}),"; please wrap it in parentheses.")},PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.'},Y=["toMessage"],G=["message"];function $(t){var e=t.toMessage,i=F(t,Y);return function t(r){var s=r.loc,n=r.details;return z(SyntaxError,Object.assign({},i,{loc:s}),{clone:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.loc||{};return t({loc:new _("line"in i?i.line:this.loc.line,"column"in i?i.column:this.loc.column,"index"in i?i.index:this.loc.index),details:Object.assign({},this.details,e.details)})},details:{value:n,enumerable:!1},message:{get:function(){return"".concat(e(this.details)," (").concat(this.loc.line,":").concat(this.loc.column,")")},set:function(t){Object.defineProperty(this,"message",{value:t})}},pos:{reflect:"loc.index",enumerable:!0},missingPlugin:"missingPlugin"in n&&{reflect:"details.missingPlugin",enumerable:!0}})}}function Q(t,e){if(Array.isArray(t))return function(e){return Q(e,t[0])};for(var i={},r=function(){var r=n[s],a=t[r],o="string"===typeof a?{message:function(){return a}}:"function"===typeof a?{message:a}:a,l=o.message,c=F(o,G),h="string"===typeof l?function(){return l}:l;i[r]=$(Object.assign({code:R.SyntaxError,reasonCode:r,toMessage:h},e?{syntaxPlugin:e}:{},c))},s=0,n=Object.keys(t);s<n.length;s++)r();return i}var Z=Object.assign({},Q(H),Q(K),Q(X),Q(s||(s=T(["pipelineOperator"])))(J)),tt=Object.defineProperty,et=function(t,e){return tt(t,e,{enumerable:!1,value:t[e]})};function it(t){return t.loc.start&&et(t.loc.start,"index"),t.loc.end&&et(t.loc.end,"index"),t}var rt=function(t){return function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"parse",value:function(){var t=it(f(k(i.prototype),"parse",this).call(this));return this.options.tokens&&(t.tokens=t.tokens.map(it)),t}},{key:"parseRegExpLiteral",value:function(t){var e=t.pattern,i=t.flags,r=null;try{r=new RegExp(e,i)}catch(n){}var s=this.estreeParseLiteral(r);return s.regex={pattern:e,flags:i},s}},{key:"parseBigIntLiteral",value:function(t){var e;try{e=BigInt(t)}catch(r){e=null}var i=this.estreeParseLiteral(e);return i.bigint=String(i.value||t),i}},{key:"parseDecimalLiteral",value:function(t){var e=null,i=this.estreeParseLiteral(e);return i.decimal=String(i.value||t),i}},{key:"estreeParseLiteral",value:function(t){return this.parseLiteral(t,"Literal")}},{key:"parseStringLiteral",value:function(t){return this.estreeParseLiteral(t)}},{key:"parseNumericLiteral",value:function(t){return this.estreeParseLiteral(t)}},{key:"parseNullLiteral",value:function(){return this.estreeParseLiteral(null)}},{key:"parseBooleanLiteral",value:function(t){return this.estreeParseLiteral(t)}},{key:"directiveToStmt",value:function(t){var e=t.value;delete t.value,e.type="Literal",e.raw=e.extra.raw,e.value=e.extra.expressionValue;var i=t;return i.type="ExpressionStatement",i.expression=e,i.directive=e.extra.rawValue,delete e.extra,i}},{key:"initFunction",value:function(t,e){f(k(i.prototype),"initFunction",this).call(this,t,e),t.expression=!1}},{key:"checkDeclaration",value:function(t){null!=t&&this.isObjectProperty(t)?this.checkDeclaration(t.value):f(k(i.prototype),"checkDeclaration",this).call(this,t)}},{key:"getObjectOrClassMethodParams",value:function(t){return t.value.params}},{key:"isValidDirective",value:function(t){var e;return"ExpressionStatement"===t.type&&"Literal"===t.expression.type&&"string"===typeof t.expression.value&&!(null!=(e=t.expression.extra)&&e.parenthesized)}},{key:"parseBlockBody",value:function(t,e,r,s,n){var a=this;f(k(i.prototype),"parseBlockBody",this).call(this,t,e,r,s,n);var o=t.directives.map((function(t){return a.directiveToStmt(t)}));t.body=o.concat(t.body),delete t.directives}},{key:"pushClassMethod",value:function(t,e,i,r,s,n){this.parseMethod(e,i,r,s,n,"ClassMethod",!0),e.typeParameters&&(e.value.typeParameters=e.typeParameters,delete e.typeParameters),t.body.push(e)}},{key:"parsePrivateName",value:function(){var t=f(k(i.prototype),"parsePrivateName",this).call(this);return this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(t):t}},{key:"convertPrivateNameToPrivateIdentifier",value:function(t){var e=f(k(i.prototype),"getPrivateNameSV",this).call(this,t);return t=t,delete t.id,t.name=e,t.type="PrivateIdentifier",t}},{key:"isPrivateName",value:function(t){return this.getPluginOption("estree","classFeatures")?"PrivateIdentifier"===t.type:f(k(i.prototype),"isPrivateName",this).call(this,t)}},{key:"getPrivateNameSV",value:function(t){return this.getPluginOption("estree","classFeatures")?t.name:f(k(i.prototype),"getPrivateNameSV",this).call(this,t)}},{key:"parseLiteral",value:function(t,e){var r=f(k(i.prototype),"parseLiteral",this).call(this,t,e);return r.raw=r.extra.raw,delete r.extra,r}},{key:"parseFunctionBody",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];f(k(i.prototype),"parseFunctionBody",this).call(this,t,e,r),t.expression="BlockStatement"!==t.body.type}},{key:"parseMethod",value:function(t,e,r,s,n,a){var o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],l=this.startNode();return l.kind=t.kind,l=f(k(i.prototype),"parseMethod",this).call(this,l,e,r,s,n,a,o),l.type="FunctionExpression",delete l.kind,t.value=l,"ClassPrivateMethod"===a&&(t.computed=!1),this.finishNode(t,"MethodDefinition")}},{key:"parseClassProperty",value:function(){for(var t,e=arguments.length,r=new Array(e),s=0;s<e;s++)r[s]=arguments[s];var n=(t=f(k(i.prototype),"parseClassProperty",this)).call.apply(t,[this].concat(r));return this.getPluginOption("estree","classFeatures")?(n.type="PropertyDefinition",n):n}},{key:"parseClassPrivateProperty",value:function(){for(var t,e=arguments.length,r=new Array(e),s=0;s<e;s++)r[s]=arguments[s];var n=(t=f(k(i.prototype),"parseClassPrivateProperty",this)).call.apply(t,[this].concat(r));return this.getPluginOption("estree","classFeatures")?(n.type="PropertyDefinition",n.computed=!1,n):n}},{key:"parseObjectMethod",value:function(t,e,r,s,n){var a=f(k(i.prototype),"parseObjectMethod",this).call(this,t,e,r,s,n);return a&&(a.type="Property","method"===a.kind&&(a.kind="init"),a.shorthand=!1),a}},{key:"parseObjectProperty",value:function(t,e,r,s){var n=f(k(i.prototype),"parseObjectProperty",this).call(this,t,e,r,s);return n&&(n.kind="init",n.type="Property"),n}},{key:"isValidLVal",value:function(t,e,r){return"Property"===t?"value":f(k(i.prototype),"isValidLVal",this).call(this,t,e,r)}},{key:"isAssignable",value:function(t,e){return null!=t&&this.isObjectProperty(t)?this.isAssignable(t.value,e):f(k(i.prototype),"isAssignable",this).call(this,t,e)}},{key:"toAssignable",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null!=t&&this.isObjectProperty(t)){var r=t.key,s=t.value;this.isPrivateName(r)&&this.classScope.usePrivateName(this.getPrivateNameSV(r),r.loc.start),this.toAssignable(s,e)}else f(k(i.prototype),"toAssignable",this).call(this,t,e)}},{key:"toAssignableObjectExpressionProp",value:function(t,e,r){"get"===t.kind||"set"===t.kind?this.raise(Z.PatternHasAccessor,{at:t.key}):t.method?this.raise(Z.PatternHasMethod,{at:t.key}):f(k(i.prototype),"toAssignableObjectExpressionProp",this).call(this,t,e,r)}},{key:"finishCallExpression",value:function(t,e){var r=f(k(i.prototype),"finishCallExpression",this).call(this,t,e);if("Import"===r.callee.type){var s;if(r.type="ImportExpression",r.source=r.arguments[0],this.hasPlugin("importAssertions"))r.attributes=null!=(s=r.arguments[1])?s:null;delete r.arguments,delete r.callee}return r}},{key:"toReferencedArguments",value:function(t){"ImportExpression"!==t.type&&f(k(i.prototype),"toReferencedArguments",this).call(this,t)}},{key:"parseExport",value:function(t,e){var r=this.state.lastTokStartLoc,s=f(k(i.prototype),"parseExport",this).call(this,t,e);switch(s.type){case"ExportAllDeclaration":s.exported=null;break;case"ExportNamedDeclaration":1===s.specifiers.length&&"ExportNamespaceSpecifier"===s.specifiers[0].type&&(s.type="ExportAllDeclaration",s.exported=s.specifiers[0].exported,delete s.specifiers);case"ExportDefaultDeclaration":var n,a=s.declaration;"ClassDeclaration"===(null==a?void 0:a.type)&&(null==(n=a.decorators)?void 0:n.length)>0&&a.start===s.start&&this.resetStartLocation(s,r);break}return s}},{key:"parseSubscript",value:function(t,e,r,s){var n=f(k(i.prototype),"parseSubscript",this).call(this,t,e,r,s);if(s.optionalChainMember){if("OptionalMemberExpression"!==n.type&&"OptionalCallExpression"!==n.type||(n.type=n.type.substring(8)),s.stop){var a=this.startNodeAtNode(n);return a.expression=n,this.finishNode(a,"ChainExpression")}}else"MemberExpression"!==n.type&&"CallExpression"!==n.type||(n.optional=!1);return n}},{key:"hasPropertyAsPrivateName",value:function(t){return"ChainExpression"===t.type&&(t=t.expression),f(k(i.prototype),"hasPropertyAsPrivateName",this).call(this,t)}},{key:"isOptionalChain",value:function(t){return"ChainExpression"===t.type}},{key:"isObjectProperty",value:function(t){return"Property"===t.type&&"init"===t.kind&&!t.method}},{key:"isObjectMethod",value:function(t){return t.method||"get"===t.kind||"set"===t.kind}},{key:"finishNodeAt",value:function(t,e,r){return it(f(k(i.prototype),"finishNodeAt",this).call(this,t,e,r))}},{key:"resetStartLocation",value:function(t,e){f(k(i.prototype),"resetStartLocation",this).call(this,t,e),it(t)}},{key:"resetEndLocation",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.lastTokEndLoc;f(k(i.prototype),"resetEndLocation",this).call(this,t,e),it(t)}}]),i}(t)},st=D((function t(e,i){L(this,t),this.token=void 0,this.preserveSpace=void 0,this.token=e,this.preserveSpace=!!i})),nt={brace:new st("{"),j_oTag:new st("<tag"),j_cTag:new st("</tag"),j_expr:new st("<tag>...</tag>",!0)};nt.template=new st("`",!0);var at=!0,ot=!0,lt=!0,ct=!0,ht=!0,ut=!0,pt=D((function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};L(this,t),this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.label=e,this.keyword=i.keyword,this.beforeExpr=!!i.beforeExpr,this.startsExpr=!!i.startsExpr,this.rightAssociative=!!i.rightAssociative,this.isLoop=!!i.isLoop,this.isAssign=!!i.isAssign,this.prefix=!!i.prefix,this.postfix=!!i.postfix,this.binop=null!=i.binop?i.binop:null,this.updateContext=null})),dt=new Map;function ft(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.keyword=t;var i=Tt(t,e);return dt.set(t,i),i}function mt(t,e){return Tt(t,{beforeExpr:at,binop:e})}var yt=-1,vt=[],gt=[],xt=[],bt=[],Pt=[],kt=[];function Tt(t){var e,i,r,s,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return++yt,gt.push(t),xt.push(null!=(e=n.binop)?e:-1),bt.push(null!=(i=n.beforeExpr)&&i),Pt.push(null!=(r=n.startsExpr)&&r),kt.push(null!=(s=n.prefix)&&s),vt.push(new pt(t,n)),yt}function wt(t){var e,i,r,s,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return++yt,dt.set(t,yt),gt.push(t),xt.push(null!=(e=n.binop)?e:-1),bt.push(null!=(i=n.beforeExpr)&&i),Pt.push(null!=(r=n.startsExpr)&&r),kt.push(null!=(s=n.prefix)&&s),vt.push(new pt("name",n)),yt}var Et={bracketL:Tt("[",{beforeExpr:at,startsExpr:ot}),bracketHashL:Tt("#[",{beforeExpr:at,startsExpr:ot}),bracketBarL:Tt("[|",{beforeExpr:at,startsExpr:ot}),bracketR:Tt("]"),bracketBarR:Tt("|]"),braceL:Tt("{",{beforeExpr:at,startsExpr:ot}),braceBarL:Tt("{|",{beforeExpr:at,startsExpr:ot}),braceHashL:Tt("#{",{beforeExpr:at,startsExpr:ot}),braceR:Tt("}"),braceBarR:Tt("|}"),parenL:Tt("(",{beforeExpr:at,startsExpr:ot}),parenR:Tt(")"),comma:Tt(",",{beforeExpr:at}),semi:Tt(";",{beforeExpr:at}),colon:Tt(":",{beforeExpr:at}),doubleColon:Tt("::",{beforeExpr:at}),dot:Tt("."),question:Tt("?",{beforeExpr:at}),questionDot:Tt("?."),arrow:Tt("=>",{beforeExpr:at}),template:Tt("template"),ellipsis:Tt("...",{beforeExpr:at}),backQuote:Tt("`",{startsExpr:ot}),dollarBraceL:Tt("${",{beforeExpr:at,startsExpr:ot}),templateTail:Tt("...`",{startsExpr:ot}),templateNonTail:Tt("...${",{beforeExpr:at,startsExpr:ot}),at:Tt("@"),hash:Tt("#",{startsExpr:ot}),interpreterDirective:Tt("#!..."),eq:Tt("=",{beforeExpr:at,isAssign:ct}),assign:Tt("_=",{beforeExpr:at,isAssign:ct}),slashAssign:Tt("_=",{beforeExpr:at,isAssign:ct}),xorAssign:Tt("_=",{beforeExpr:at,isAssign:ct}),moduloAssign:Tt("_=",{beforeExpr:at,isAssign:ct}),incDec:Tt("++/--",{prefix:ht,postfix:ut,startsExpr:ot}),bang:Tt("!",{beforeExpr:at,prefix:ht,startsExpr:ot}),tilde:Tt("~",{beforeExpr:at,prefix:ht,startsExpr:ot}),doubleCaret:Tt("^^",{startsExpr:ot}),doubleAt:Tt("@@",{startsExpr:ot}),pipeline:mt("|>",0),nullishCoalescing:mt("??",1),logicalOR:mt("||",1),logicalAND:mt("&&",2),bitwiseOR:mt("|",3),bitwiseXOR:mt("^",4),bitwiseAND:mt("&",5),equality:mt("==/!=/===/!==",6),lt:mt("</>/<=/>=",7),gt:mt("</>/<=/>=",7),relational:mt("</>/<=/>=",7),bitShift:mt("<</>>/>>>",8),bitShiftL:mt("<</>>/>>>",8),bitShiftR:mt("<</>>/>>>",8),plusMin:Tt("+/-",{beforeExpr:at,binop:9,prefix:ht,startsExpr:ot}),modulo:Tt("%",{binop:10,startsExpr:ot}),star:Tt("*",{binop:10}),slash:mt("/",10),exponent:Tt("**",{beforeExpr:at,binop:11,rightAssociative:!0}),_in:ft("in",{beforeExpr:at,binop:7}),_instanceof:ft("instanceof",{beforeExpr:at,binop:7}),_break:ft("break"),_case:ft("case",{beforeExpr:at}),_catch:ft("catch"),_continue:ft("continue"),_debugger:ft("debugger"),_default:ft("default",{beforeExpr:at}),_else:ft("else",{beforeExpr:at}),_finally:ft("finally"),_function:ft("function",{startsExpr:ot}),_if:ft("if"),_return:ft("return",{beforeExpr:at}),_switch:ft("switch"),_throw:ft("throw",{beforeExpr:at,prefix:ht,startsExpr:ot}),_try:ft("try"),_var:ft("var"),_const:ft("const"),_with:ft("with"),_new:ft("new",{beforeExpr:at,startsExpr:ot}),_this:ft("this",{startsExpr:ot}),_super:ft("super",{startsExpr:ot}),_class:ft("class",{startsExpr:ot}),_extends:ft("extends",{beforeExpr:at}),_export:ft("export"),_import:ft("import",{startsExpr:ot}),_null:ft("null",{startsExpr:ot}),_true:ft("true",{startsExpr:ot}),_false:ft("false",{startsExpr:ot}),_typeof:ft("typeof",{beforeExpr:at,prefix:ht,startsExpr:ot}),_void:ft("void",{beforeExpr:at,prefix:ht,startsExpr:ot}),_delete:ft("delete",{beforeExpr:at,prefix:ht,startsExpr:ot}),_do:ft("do",{isLoop:lt,beforeExpr:at}),_for:ft("for",{isLoop:lt}),_while:ft("while",{isLoop:lt}),_as:wt("as",{startsExpr:ot}),_assert:wt("assert",{startsExpr:ot}),_async:wt("async",{startsExpr:ot}),_await:wt("await",{startsExpr:ot}),_from:wt("from",{startsExpr:ot}),_get:wt("get",{startsExpr:ot}),_let:wt("let",{startsExpr:ot}),_meta:wt("meta",{startsExpr:ot}),_of:wt("of",{startsExpr:ot}),_sent:wt("sent",{startsExpr:ot}),_set:wt("set",{startsExpr:ot}),_static:wt("static",{startsExpr:ot}),_using:wt("using",{startsExpr:ot}),_yield:wt("yield",{startsExpr:ot}),_asserts:wt("asserts",{startsExpr:ot}),_checks:wt("checks",{startsExpr:ot}),_exports:wt("exports",{startsExpr:ot}),_global:wt("global",{startsExpr:ot}),_implements:wt("implements",{startsExpr:ot}),_intrinsic:wt("intrinsic",{startsExpr:ot}),_infer:wt("infer",{startsExpr:ot}),_is:wt("is",{startsExpr:ot}),_mixins:wt("mixins",{startsExpr:ot}),_proto:wt("proto",{startsExpr:ot}),_require:wt("require",{startsExpr:ot}),_satisfies:wt("satisfies",{startsExpr:ot}),_keyof:wt("keyof",{startsExpr:ot}),_readonly:wt("readonly",{startsExpr:ot}),_unique:wt("unique",{startsExpr:ot}),_abstract:wt("abstract",{startsExpr:ot}),_declare:wt("declare",{startsExpr:ot}),_enum:wt("enum",{startsExpr:ot}),_module:wt("module",{startsExpr:ot}),_namespace:wt("namespace",{startsExpr:ot}),_interface:wt("interface",{startsExpr:ot}),_type:wt("type",{startsExpr:ot}),_opaque:wt("opaque",{startsExpr:ot}),name:Tt("name",{startsExpr:ot}),string:Tt("string",{startsExpr:ot}),num:Tt("num",{startsExpr:ot}),bigint:Tt("bigint",{startsExpr:ot}),decimal:Tt("decimal",{startsExpr:ot}),regexp:Tt("regexp",{startsExpr:ot}),privateName:Tt("#name",{startsExpr:ot}),eof:Tt("eof"),jsxName:Tt("jsxName"),jsxText:Tt("jsxText",{beforeExpr:!0}),jsxTagStart:Tt("jsxTagStart",{startsExpr:!0}),jsxTagEnd:Tt("jsxTagEnd"),placeholder:Tt("%%",{startsExpr:!0})};function At(t){return t>=93&&t<=130}function St(t){return t<=92}function Ct(t){return t>=58&&t<=130}function It(t){return t>=58&&t<=134}function Nt(t){return bt[t]}function Dt(t){return Pt[t]}function Ot(t){return t>=29&&t<=33}function Mt(t){return t>=127&&t<=129}function Lt(t){return t>=90&&t<=92}function Ft(t){return t>=58&&t<=92}function _t(t){return t>=39&&t<=59}function Bt(t){return 34===t}function jt(t){return kt[t]}function Rt(t){return t>=119&&t<=121}function Ut(t){return t>=122&&t<=128}function zt(t){return gt[t]}function Ht(t){return xt[t]}function Vt(t){return 57===t}function qt(t){return t>=24&&t<=25}function Kt(t){return vt[t]}vt[8].updateContext=function(t){t.pop()},vt[5].updateContext=vt[7].updateContext=vt[23].updateContext=function(t){t.push(nt.brace)},vt[22].updateContext=function(t){t[t.length-1]===nt.template?t.pop():t.push(nt.template)},vt[140].updateContext=function(t){t.push(nt.j_expr,nt.j_oTag)};var Xt="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟊꟐꟑꟓꟕ-ꟙꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",Wt="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",Jt=new RegExp("["+Xt+"]"),Yt=new RegExp("["+Xt+Wt+"]");Xt=Wt=null;var Gt=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,4026,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,757,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,3104,541,1507,4938,6,4191],$t=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,81,2,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,9,5351,0,7,14,13835,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,983,6,110,6,6,9,4759,9,787719,239];function Qt(t,e){for(var i=65536,r=0,s=e.length;r<s;r+=2){if(i+=e[r],i>t)return!1;if(i+=e[r+1],i>=t)return!0}return!1}function Zt(t){return t<65?36===t:t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&Jt.test(String.fromCharCode(t)):Qt(t,Gt)))}function te(t){return t<48?36===t:t<58||!(t<65)&&(t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&Yt.test(String.fromCharCode(t)):Qt(t,Gt)||Qt(t,$t))))}var ee={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},ie=new Set(ee.keyword),re=new Set(ee.strict),se=new Set(ee.strictBind);function ne(t,e){return e&&"await"===t||"enum"===t}function ae(t,e){return ne(t,e)||re.has(t)}function oe(t){return se.has(t)}function le(t,e){return ae(t,e)||oe(t)}function ce(t){return ie.has(t)}function he(t,e,i){return 64===t&&64===e&&Zt(i)}var ue=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete","implements","interface","let","package","private","protected","public","static","yield","eval","arguments","enum","await"]);function pe(t){return ue.has(t)}var de=0,fe=1,me=2,ye=4,ve=8,ge=16,xe=32,be=64,Pe=128,ke=256,Te=fe|me|Pe|ke,we=1,Ee=2,Ae=4,Se=8,Ce=16,Ie=64,Ne=128,De=256,Oe=512,Me=1024,Le=2048,Fe=4096,_e=we|Ee|Se|Ne,Be=0|we|Se|0,je=0|we|Ae|0,Re=0|we|Ce|0,Ue=0|Ee|Ne,ze=0|Ee,He=we|Ee|Se|De,Ve=0|Me,qe=0|Ie,Ke=0|we|Ie,Xe=He|Oe,We=0|Me,Je=0|Ee|Fe,Ye=Le,Ge=4,$e=2,Qe=1,Ze=$e|Qe,ti=$e|Ge,ei=Qe|Ge,ii=$e,ri=Qe,si=0,ni=D((function t(e){L(this,t),this.var=new Set,this.lexical=new Set,this.functions=new Set,this.flags=e})),ai=function(){function t(e,i){L(this,t),this.parser=void 0,this.scopeStack=[],this.inModule=void 0,this.undefinedExports=new Map,this.parser=e,this.inModule=i}return D(t,[{key:"inTopLevel",get:function(){return(this.currentScope().flags&fe)>0}},{key:"inFunction",get:function(){return(this.currentVarScopeFlags()&me)>0}},{key:"allowSuper",get:function(){return(this.currentThisScopeFlags()&ge)>0}},{key:"allowDirectSuper",get:function(){return(this.currentThisScopeFlags()&xe)>0}},{key:"inClass",get:function(){return(this.currentThisScopeFlags()&be)>0}},{key:"inClassAndNotInNonArrowFunction",get:function(){var t=this.currentThisScopeFlags();return(t&be)>0&&0===(t&me)}},{key:"inStaticBlock",get:function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t].flags;if(e&Pe)return!0;if(e&(Te|be))return!1}}},{key:"inNonArrowFunction",get:function(){return(this.currentThisScopeFlags()&me)>0}},{key:"treatFunctionsAsVar",get:function(){return this.treatFunctionsAsVarInScope(this.currentScope())}},{key:"createScope",value:function(t){return new ni(t)}},{key:"enter",value:function(t){this.scopeStack.push(this.createScope(t))}},{key:"exit",value:function(){var t=this.scopeStack.pop();return t.flags}},{key:"treatFunctionsAsVarInScope",value:function(t){return!!(t.flags&(me|Pe)||!this.parser.inModule&&t.flags&fe)}},{key:"declareName",value:function(t,e,i){var r=this.currentScope();if(e&Se||e&Ce)this.checkRedeclarationInScope(r,t,e,i),e&Ce?r.functions.add(t):r.lexical.add(t),e&Se&&this.maybeExportDefined(r,t);else if(e&Ae)for(var s=this.scopeStack.length-1;s>=0;--s)if(r=this.scopeStack[s],this.checkRedeclarationInScope(r,t,e,i),r.var.add(t),this.maybeExportDefined(r,t),r.flags&Te)break;this.parser.inModule&&r.flags&fe&&this.undefinedExports.delete(t)}},{key:"maybeExportDefined",value:function(t,e){this.parser.inModule&&t.flags&fe&&this.undefinedExports.delete(e)}},{key:"checkRedeclarationInScope",value:function(t,e,i,r){this.isRedeclaredInScope(t,e,i)&&this.parser.raise(Z.VarRedeclaration,{at:r,identifierName:e})}},{key:"isRedeclaredInScope",value:function(t,e,i){return!!(i&we)&&(i&Se?t.lexical.has(e)||t.functions.has(e)||t.var.has(e):i&Ce?t.lexical.has(e)||!this.treatFunctionsAsVarInScope(t)&&t.var.has(e):t.lexical.has(e)&&!(t.flags&ve&&t.lexical.values().next().value===e)||!this.treatFunctionsAsVarInScope(t)&&t.functions.has(e))}},{key:"checkLocalExport",value:function(t){var e=t.name,i=this.scopeStack[0];i.lexical.has(e)||i.var.has(e)||i.functions.has(e)||this.undefinedExports.set(e,t.loc.start)}},{key:"currentScope",value:function(){return this.scopeStack[this.scopeStack.length-1]}},{key:"currentVarScopeFlags",value:function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t].flags;if(e&Te)return e}}},{key:"currentThisScopeFlags",value:function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t].flags;if(e&(Te|be)&&!(e&ye))return e}}}]),t}(),oi=function(t){y(i,t);var e=g(i);function i(){var t;L(this,i);for(var r=arguments.length,s=new Array(r),n=0;n<r;n++)s[n]=arguments[n];return t=e.call.apply(e,[this].concat(s)),t.declareFunctions=new Set,t}return D(i)}(ni),li=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"createScope",value:function(t){return new oi(t)}},{key:"declareName",value:function(t,e,r){var s=this.currentScope();if(e&Le)return this.checkRedeclarationInScope(s,t,e,r),this.maybeExportDefined(s,t),void s.declareFunctions.add(t);f(k(i.prototype),"declareName",this).call(this,t,e,r)}},{key:"isRedeclaredInScope",value:function(t,e,r){return!!f(k(i.prototype),"isRedeclaredInScope",this).call(this,t,e,r)||!!(r&Le)&&(!t.declareFunctions.has(e)&&(t.lexical.has(e)||t.functions.has(e)))}},{key:"checkLocalExport",value:function(t){this.scopeStack[0].declareFunctions.has(t.name)||f(k(i.prototype),"checkLocalExport",this).call(this,t)}}]),i}(ai),ci=function(){function t(){L(this,t),this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}return D(t,[{key:"hasPlugin",value:function(t){if("string"===typeof t)return this.plugins.has(t);var e=w(t,2),i=e[0],r=e[1];if(!this.hasPlugin(i))return!1;for(var s=this.plugins.get(i),n=0,a=Object.keys(r);n<a.length;n++){var o=a[n];if((null==s?void 0:s[o])!==r[o])return!1}return!0}},{key:"getPluginOption",value:function(t,e){var i;return null==(i=this.plugins.get(t))?void 0:i[e]}}]),t}();function hi(t,e){var i;void 0===t.trailingComments?t.trailingComments=e:(i=t.trailingComments).unshift.apply(i,h(e))}function ui(t,e){var i;void 0===t.leadingComments?t.leadingComments=e:(i=t.leadingComments).unshift.apply(i,h(e))}function pi(t,e){var i;void 0===t.innerComments?t.innerComments=e:(i=t.innerComments).unshift.apply(i,h(e))}function di(t,e,i){var r=null,s=e.length;while(null===r&&s>0)r=e[--s];null===r||r.start>i.start?pi(t,i.comments):hi(r,i.comments)}var fi=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"addComment",value:function(t){this.filename&&(t.loc.filename=this.filename),this.state.comments.push(t)}},{key:"processComment",value:function(t){var e=this.state.commentStack,i=e.length;if(0!==i){var r=i-1,s=e[r];s.start===t.end&&(s.leadingNode=t,r--);for(var n=t.start;r>=0;r--){var a=e[r],o=a.end;if(!(o>n)){o===n&&(a.trailingNode=t);break}a.containingNode=t,this.finalizeComment(a),e.splice(r,1)}}}},{key:"finalizeComment",value:function(t){var e=t.comments;if(null!==t.leadingNode||null!==t.trailingNode)null!==t.leadingNode&&hi(t.leadingNode,e),null!==t.trailingNode&&ui(t.trailingNode,e);else{var i=t.containingNode,r=t.start;if(44===this.input.charCodeAt(r-1))switch(i.type){case"ObjectExpression":case"ObjectPattern":case"RecordExpression":di(i,i.properties,t);break;case"CallExpression":case"OptionalCallExpression":di(i,i.arguments,t);break;case"FunctionDeclaration":case"FunctionExpression":case"ArrowFunctionExpression":case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":di(i,i.params,t);break;case"ArrayExpression":case"ArrayPattern":case"TupleExpression":di(i,i.elements,t);break;case"ExportNamedDeclaration":case"ImportDeclaration":di(i,i.specifiers,t);break;default:pi(i,e)}else pi(i,e)}}},{key:"finalizeRemainingComments",value:function(){for(var t=this.state.commentStack,e=t.length-1;e>=0;e--)this.finalizeComment(t[e]);this.state.commentStack=[]}},{key:"resetPreviousNodeTrailingComments",value:function(t){var e=this.state.commentStack,i=e.length;if(0!==i){var r=e[i-1];r.leadingNode===t&&(r.leadingNode=null)}}},{key:"takeSurroundingComments",value:function(t,e,i){var r=this.state.commentStack,s=r.length;if(0!==s)for(var n=s-1;n>=0;n--){var a=r[n],o=a.end,l=a.start;if(l===i)a.leadingNode=t;else if(o===e)a.trailingNode=t;else if(o<e)break}}}]),i}(ci),mi=/\r\n?|[\n\u2028\u2029]/,yi=new RegExp(mi.source,"g");function vi(t){switch(t){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}var gi=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,xi=new RegExp("(?:[^\\S\\n\\r\\u2028\\u2029]|\\/\\/.*|\\/\\*.*?\\*\\/)*","y"),bi=new RegExp("(?=("+xi.source+"))\\1"+/(?=[\n\r\u2028\u2029]|\/\*(?!.*?\*\/)|$)/.source,"y");function Pi(t){switch(t){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}var ki=function(){function t(){L(this,t),this.strict=void 0,this.curLine=void 0,this.lineStart=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.maybeInArrowParameters=!1,this.inType=!1,this.noAnonFunctionType=!1,this.hasFlowComment=!1,this.isAmbientContext=!1,this.inAbstractClass=!1,this.inDisallowConditionalTypesContext=!1,this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.soloAwait=!1,this.inFSharpPipelineDirectBody=!1,this.labels=[],this.comments=[],this.commentStack=[],this.pos=0,this.type=137,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.lastTokStart=0,this.context=[nt.brace],this.canStartJSXElement=!0,this.containsEsc=!1,this.firstInvalidTemplateEscapePos=null,this.strictErrors=new Map,this.tokensLength=0}return D(t,[{key:"init",value:function(t){var e=t.strictMode,i=t.sourceType,r=t.startLine,s=t.startColumn;this.strict=!1!==e&&(!0===e||"module"===i),this.curLine=r,this.lineStart=-s,this.startLoc=this.endLoc=new _(r,s,0)}},{key:"curPosition",value:function(){return new _(this.curLine,this.pos-this.lineStart,this.pos)}},{key:"clone",value:function(e){for(var i=new t,r=Object.keys(this),s=0,n=r.length;s<n;s++){var a=r[s],o=this[a];!e&&Array.isArray(o)&&(o=o.slice()),i[a]=o}return i}}]),t}(),Ti=function(t){return t>=48&&t<=57},wi={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},Ei={bin:function(t){return 48===t||49===t},oct:function(t){return t>=48&&t<=55},dec:function(t){return t>=48&&t<=57},hex:function(t){return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}};function Ai(t,e,i,r,s,n){for(var a=i,o=r,l=s,c="",h=null,u=i,p=e.length;;){if(i>=p){n.unterminated(a,o,l),c+=e.slice(u,i);break}var d=e.charCodeAt(i);if(Si(t,d,e,i)){c+=e.slice(u,i);break}if(92===d){c+=e.slice(u,i);var f=Ci(e,i,r,s,"template"===t,n);null!==f.ch||h?c+=f.ch:h={pos:i,lineStart:r,curLine:s},i=f.pos,r=f.lineStart,s=f.curLine,u=i}else 8232===d||8233===d?(++i,++s,r=i):10===d||13===d?"template"===t?(c+=e.slice(u,i)+"\n",++i,13===d&&10===e.charCodeAt(i)&&++i,++s,u=r=i):n.unterminated(a,o,l):++i}return{pos:i,str:c,firstInvalidLoc:h,lineStart:r,curLine:s,containsInvalid:!!h}}function Si(t,e,i,r){return"template"===t?96===e||36===e&&123===i.charCodeAt(r+1):e===("double"===t?34:39)}function Ci(t,e,i,r,s,n){var a=!s;e++;var o=function(t){return{pos:e,ch:t,lineStart:i,curLine:r}},l=t.charCodeAt(e++);switch(l){case 110:return o("\n");case 114:return o("\r");case 120:var c,h=Ii(t,e,i,r,2,!1,a,n);return c=h.code,e=h.pos,o(null===c?null:String.fromCharCode(c));case 117:var u,p=Di(t,e,i,r,a,n);return u=p.code,e=p.pos,o(null===u?null:String.fromCodePoint(u));case 116:return o("\t");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:10===t.charCodeAt(e)&&++e;case 10:i=e,++r;case 8232:case 8233:return o("");case 56:case 57:if(s)return o(null);n.strictNumericEscape(e-1,i,r);default:if(l>=48&&l<=55){var d=e-1,f=t.slice(d,e+2).match(/^[0-7]+/),m=f[0],y=parseInt(m,8);y>255&&(m=m.slice(0,-1),y=parseInt(m,8)),e+=m.length-1;var v=t.charCodeAt(e);if("0"!==m||56===v||57===v){if(s)return o(null);n.strictNumericEscape(d,i,r)}return o(String.fromCharCode(y))}return o(String.fromCharCode(l))}}function Ii(t,e,i,r,s,n,a,o){var l,c=e,h=Ni(t,e,i,r,16,s,n,!1,o,!a);return l=h.n,e=h.pos,null===l&&(a?o.invalidEscapeSequence(c,i,r):e=c-1),{code:l,pos:e}}function Ni(t,e,i,r,s,n,a,o,l,c){for(var h=e,u=16===s?wi.hex:wi.decBinOct,p=16===s?Ei.hex:10===s?Ei.dec:8===s?Ei.oct:Ei.bin,d=!1,f=0,m=0,y=null==n?1/0:n;m<y;++m){var v=t.charCodeAt(e),g=void 0;if(95!==v||"bail"===o){if(g=v>=97?v-97+10:v>=65?v-65+10:Ti(v)?v-48:1/0,g>=s){if(g<=9&&c)return{n:null,pos:e};if(g<=9&&l.invalidDigit(e,i,r,s))g=0;else{if(!a)break;g=0,d=!0}}++e,f=f*s+g}else{var x=t.charCodeAt(e-1),b=t.charCodeAt(e+1);if(o){if(Number.isNaN(b)||!p(b)||u.has(x)||u.has(b)){if(c)return{n:null,pos:e};l.unexpectedNumericSeparator(e,i,r)}}else{if(c)return{n:null,pos:e};l.numericSeparatorInEscapeSequence(e,i,r)}++e}}return e===h||null!=n&&e-h!==n||d?{n:null,pos:e}:{n:f,pos:e}}function Di(t,e,i,r,s,n){var a,o=t.charCodeAt(e);if(123===o){++e;var l=Ii(t,e,i,r,t.indexOf("}",e)-e,!0,s,n);if(a=l.code,e=l.pos,++e,null!==a&&a>1114111){if(!s)return{code:null,pos:e};n.invalidCodePoint(e,i,r)}}else{var c=Ii(t,e,i,r,4,!1,s,n);a=c.code,e=c.pos}return{code:a,pos:e}}var Oi=["at"],Mi=["at"];function Li(t,e,i){return new _(i,t-e,t)}var Fi=new Set([103,109,115,105,121,117,100,118]),_i=D((function t(e){L(this,t),this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,this.loc=new B(e.startLoc,e.endLoc)})),Bi=function(t){y(i,t);var e=g(i);function i(t,r){var s;return L(this,i),s=e.call(this),s.isLookahead=void 0,s.tokens=[],s.errorHandlers_readInt={invalidDigit:function(t,e,i,r){return!!s.options.errorRecovery&&(s.raise(Z.InvalidDigit,{at:Li(t,e,i),radix:r}),!0)},numericSeparatorInEscapeSequence:s.errorBuilder(Z.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:s.errorBuilder(Z.UnexpectedNumericSeparator)},s.errorHandlers_readCodePoint=Object.assign({},s.errorHandlers_readInt,{invalidEscapeSequence:s.errorBuilder(Z.InvalidEscapeSequence),invalidCodePoint:s.errorBuilder(Z.InvalidCodePoint)}),s.errorHandlers_readStringContents_string=Object.assign({},s.errorHandlers_readCodePoint,{strictNumericEscape:function(t,e,i){s.recordStrictModeErrors(Z.StrictNumericEscape,{at:Li(t,e,i)})},unterminated:function(t,e,i){throw s.raise(Z.UnterminatedString,{at:Li(t-1,e,i)})}}),s.errorHandlers_readStringContents_template=Object.assign({},s.errorHandlers_readCodePoint,{strictNumericEscape:s.errorBuilder(Z.StrictNumericEscape),unterminated:function(t,e,i){throw s.raise(Z.UnterminatedTemplate,{at:Li(t,e,i)})}}),s.state=new ki,s.state.init(t),s.input=r,s.length=r.length,s.isLookahead=!1,s}return D(i,[{key:"pushToken",value:function(t){this.tokens.length=this.state.tokensLength,this.tokens.push(t),++this.state.tokensLength}},{key:"next",value:function(){this.checkKeywordEscapes(),this.options.tokens&&this.pushToken(new _i(this.state)),this.state.lastTokStart=this.state.start,this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}},{key:"eat",value:function(t){return!!this.match(t)&&(this.next(),!0)}},{key:"match",value:function(t){return this.state.type===t}},{key:"createLookaheadState",value:function(t){return{pos:t.pos,value:null,type:t.type,start:t.start,end:t.end,context:[this.curContext()],inType:t.inType,startLoc:t.startLoc,lastTokEndLoc:t.lastTokEndLoc,curLine:t.curLine,lineStart:t.lineStart,curPosition:t.curPosition}}},{key:"lookahead",value:function(){var t=this.state;this.state=this.createLookaheadState(t),this.isLookahead=!0,this.nextToken(),this.isLookahead=!1;var e=this.state;return this.state=t,e}},{key:"nextTokenStart",value:function(){return this.nextTokenStartSince(this.state.pos)}},{key:"nextTokenStartSince",value:function(t){return gi.lastIndex=t,gi.test(this.input)?gi.lastIndex:t}},{key:"lookaheadCharCode",value:function(){return this.input.charCodeAt(this.nextTokenStart())}},{key:"codePointAtPos",value:function(t){var e=this.input.charCodeAt(t);if(55296===(64512&e)&&++t<this.input.length){var i=this.input.charCodeAt(t);56320===(64512&i)&&(e=65536+((1023&e)<<10)+(1023&i))}return e}},{key:"setStrict",value:function(t){var e=this;this.state.strict=t,t&&(this.state.strictErrors.forEach((function(t){var i=w(t,2),r=i[0],s=i[1];return e.raise(r,{at:s})})),this.state.strictErrors.clear())}},{key:"curContext",value:function(){return this.state.context[this.state.context.length-1]}},{key:"nextToken",value:function(){this.skipSpace(),this.state.start=this.state.pos,this.isLookahead||(this.state.startLoc=this.state.curPosition()),this.state.pos>=this.length?this.finishToken(137):this.getTokenFromCode(this.codePointAtPos(this.state.pos))}},{key:"skipBlockComment",value:function(t){var e;this.isLookahead||(e=this.state.curPosition());var i=this.state.pos,r=this.input.indexOf(t,i+2);if(-1===r)throw this.raise(Z.UnterminatedComment,{at:this.state.curPosition()});this.state.pos=r+t.length,yi.lastIndex=i+2;while(yi.test(this.input)&&yi.lastIndex<=r)++this.state.curLine,this.state.lineStart=yi.lastIndex;if(!this.isLookahead){var s={type:"CommentBlock",value:this.input.slice(i+2,r),start:i,end:r+t.length,loc:new B(e,this.state.curPosition())};return this.options.tokens&&this.pushToken(s),s}}},{key:"skipLineComment",value:function(t){var e,i=this.state.pos;this.isLookahead||(e=this.state.curPosition());var r=this.input.charCodeAt(this.state.pos+=t);if(this.state.pos<this.length)while(!vi(r)&&++this.state.pos<this.length)r=this.input.charCodeAt(this.state.pos);if(!this.isLookahead){var s=this.state.pos,n=this.input.slice(i+t,s),a={type:"CommentLine",value:n,start:i,end:s,loc:new B(e,this.state.curPosition())};return this.options.tokens&&this.pushToken(a),a}}},{key:"skipSpace",value:function(){var t=this.state.pos,e=[];t:while(this.state.pos<this.length){var i=this.input.charCodeAt(this.state.pos);switch(i){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:var r=this.skipBlockComment("*/");void 0!==r&&(this.addComment(r),this.options.attachComment&&e.push(r));break;case 47:var s=this.skipLineComment(2);void 0!==s&&(this.addComment(s),this.options.attachComment&&e.push(s));break;default:break t}break;default:if(Pi(i))++this.state.pos;else if(45!==i||this.inModule){if(60!==i||this.inModule)break t;var n=this.state.pos;if(33!==this.input.charCodeAt(n+1)||45!==this.input.charCodeAt(n+2)||45!==this.input.charCodeAt(n+3))break t;var a=this.skipLineComment(4);void 0!==a&&(this.addComment(a),this.options.attachComment&&e.push(a))}else{var o=this.state.pos;if(45!==this.input.charCodeAt(o+1)||62!==this.input.charCodeAt(o+2)||!(0===t||this.state.lineStart>t))break t;var l=this.skipLineComment(3);void 0!==l&&(this.addComment(l),this.options.attachComment&&e.push(l))}}}if(e.length>0){var c=this.state.pos,h={start:t,end:c,comments:e,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(h)}}},{key:"finishToken",value:function(t,e){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();var i=this.state.type;this.state.type=t,this.state.value=e,this.isLookahead||this.updateContext(i)}},{key:"replaceToken",value:function(t){this.state.type=t,this.updateContext()}},{key:"readToken_numberSign",value:function(){if(0!==this.state.pos||!this.readToken_interpreter()){var t=this.state.pos+1,e=this.codePointAtPos(t);if(e>=48&&e<=57)throw this.raise(Z.UnexpectedDigitAfterHash,{at:this.state.curPosition()});if(123===e||91===e&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),"bar"===this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(123===e?Z.RecordExpressionHashIncorrectStartSyntaxType:Z.TupleExpressionHashIncorrectStartSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,123===e?this.finishToken(7):this.finishToken(1)}else Zt(e)?(++this.state.pos,this.finishToken(136,this.readWord1(e))):92===e?(++this.state.pos,this.finishToken(136,this.readWord1())):this.finishOp(27,1)}}},{key:"readToken_dot",value:function(){var t=this.input.charCodeAt(this.state.pos+1);t>=48&&t<=57?this.readNumber(!0):46===t&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(21)):(++this.state.pos,this.finishToken(16))}},{key:"readToken_slash",value:function(){var t=this.input.charCodeAt(this.state.pos+1);61===t?this.finishOp(31,2):this.finishOp(56,1)}},{key:"readToken_interpreter",value:function(){if(0!==this.state.pos||this.length<2)return!1;var t=this.input.charCodeAt(this.state.pos+1);if(33!==t)return!1;var e=this.state.pos;this.state.pos+=1;while(!vi(t)&&++this.state.pos<this.length)t=this.input.charCodeAt(this.state.pos);var i=this.input.slice(e+2,this.state.pos);return this.finishToken(28,i),!0}},{key:"readToken_mult_modulo",value:function(t){var e=42===t?55:54,i=1,r=this.input.charCodeAt(this.state.pos+1);42===t&&42===r&&(i++,r=this.input.charCodeAt(this.state.pos+2),e=57),61!==r||this.state.inType||(i++,e=37===t?33:30),this.finishOp(e,i)}},{key:"readToken_pipe_amp",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);if(e!==t){if(124===t){if(62===e)return void this.finishOp(39,2);if(this.hasPlugin("recordAndTuple")&&125===e){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.RecordExpressionBarIncorrectEndSyntaxType,{at:this.state.curPosition()});return this.state.pos+=2,void this.finishToken(9)}if(this.hasPlugin("recordAndTuple")&&93===e){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.TupleExpressionBarIncorrectEndSyntaxType,{at:this.state.curPosition()});return this.state.pos+=2,void this.finishToken(4)}}61!==e?this.finishOp(124===t?43:45,1):this.finishOp(30,2)}else 61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(30,3):this.finishOp(124===t?41:42,2)}},{key:"readToken_caret",value:function(){var t=this.input.charCodeAt(this.state.pos+1);if(61!==t||this.state.inType)if(94===t&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"^^"}])){this.finishOp(37,2);var e=this.input.codePointAt(this.state.pos);if(94===e)throw this.unexpected()}else this.finishOp(44,1);else this.finishOp(32,2)}},{key:"readToken_atSign",value:function(){var t=this.input.charCodeAt(this.state.pos+1);64===t&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"@@"}])?this.finishOp(38,2):this.finishOp(26,1)}},{key:"readToken_plus_min",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);e!==t?61===e?this.finishOp(30,2):this.finishOp(53,1):this.finishOp(34,2)}},{key:"readToken_lt",value:function(){var t=this.state.pos,e=this.input.charCodeAt(t+1);if(60===e)return 61===this.input.charCodeAt(t+2)?void this.finishOp(30,3):void this.finishOp(51,2);61!==e?this.finishOp(47,1):this.finishOp(49,2)}},{key:"readToken_gt",value:function(){var t=this.state.pos,e=this.input.charCodeAt(t+1);if(62===e){var i=62===this.input.charCodeAt(t+2)?3:2;return 61===this.input.charCodeAt(t+i)?void this.finishOp(30,i+1):void this.finishOp(52,i)}61!==e?this.finishOp(48,1):this.finishOp(49,2)}},{key:"readToken_eq_excl",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);if(61!==e)return 61===t&&62===e?(this.state.pos+=2,void this.finishToken(19)):void this.finishOp(61===t?29:35,1);this.finishOp(46,61===this.input.charCodeAt(this.state.pos+2)?3:2)}},{key:"readToken_question",value:function(){var t=this.input.charCodeAt(this.state.pos+1),e=this.input.charCodeAt(this.state.pos+2);63===t?61===e?this.finishOp(30,3):this.finishOp(40,2):46!==t||e>=48&&e<=57?(++this.state.pos,this.finishToken(17)):(this.state.pos+=2,this.finishToken(18))}},{key:"getTokenFromCode",value:function(t){switch(t){case 46:return void this.readToken_dot();case 40:return++this.state.pos,void this.finishToken(10);case 41:return++this.state.pos,void this.finishToken(11);case 59:return++this.state.pos,void this.finishToken(13);case 44:return++this.state.pos,void this.finishToken(12);case 91:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.TupleExpressionBarIncorrectStartSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,this.finishToken(2)}else++this.state.pos,this.finishToken(0);return;case 93:return++this.state.pos,void this.finishToken(3);case 123:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.RecordExpressionBarIncorrectStartSyntaxType,{at:this.state.curPosition()});this.state.pos+=2,this.finishToken(6)}else++this.state.pos,this.finishToken(5);return;case 125:return++this.state.pos,void this.finishToken(8);case 58:return void(this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(15,2):(++this.state.pos,this.finishToken(14)));case 63:return void this.readToken_question();case 96:return void this.readTemplateToken();case 48:var e=this.input.charCodeAt(this.state.pos+1);if(120===e||88===e)return void this.readRadixNumber(16);if(111===e||79===e)return void this.readRadixNumber(8);if(98===e||66===e)return void this.readRadixNumber(2);case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return void this.readNumber(!1);case 34:case 39:return void this.readString(t);case 47:return void this.readToken_slash();case 37:case 42:return void this.readToken_mult_modulo(t);case 124:case 38:return void this.readToken_pipe_amp(t);case 94:return void this.readToken_caret();case 43:case 45:return void this.readToken_plus_min(t);case 60:return void this.readToken_lt();case 62:return void this.readToken_gt();case 61:case 33:return void this.readToken_eq_excl(t);case 126:return void this.finishOp(36,1);case 64:return void this.readToken_atSign();case 35:return void this.readToken_numberSign();case 92:return void this.readWord();default:if(Zt(t))return void this.readWord(t)}throw this.raise(Z.InvalidOrUnexpectedToken,{at:this.state.curPosition(),unexpected:String.fromCodePoint(t)})}},{key:"finishOp",value:function(t,e){var i=this.input.slice(this.state.pos,this.state.pos+e);this.state.pos+=e,this.finishToken(t,i)}},{key:"readRegexp",value:function(){for(var t,e,i=this.state.startLoc,r=this.state.start+1,s=this.state.pos;;++s){if(s>=this.length)throw this.raise(Z.UnterminatedRegExp,{at:j(i,1)});var n=this.input.charCodeAt(s);if(vi(n))throw this.raise(Z.UnterminatedRegExp,{at:j(i,1)});if(t)t=!1;else{if(91===n)e=!0;else if(93===n&&e)e=!1;else if(47===n&&!e)break;t=92===n}}var a=this.input.slice(r,s);++s;var o="",l=function(){return j(i,s+2-r)};while(s<this.length){var c=this.codePointAtPos(s),h=String.fromCharCode(c);if(Fi.has(c))118===c?(this.expectPlugin("regexpUnicodeSets",l()),o.includes("u")&&this.raise(Z.IncompatibleRegExpUVFlags,{at:l()})):117===c&&o.includes("v")&&this.raise(Z.IncompatibleRegExpUVFlags,{at:l()}),o.includes(h)&&this.raise(Z.DuplicateRegExpFlags,{at:l()});else{if(!te(c)&&92!==c)break;this.raise(Z.MalformedRegExpFlags,{at:l()})}++s,o+=h}this.state.pos=s,this.finishToken(135,{pattern:a,flags:o})}},{key:"readInt",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],s=Ni(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,e,i,r,this.errorHandlers_readInt,!1),n=s.n,a=s.pos;return this.state.pos=a,n}},{key:"readRadixNumber",value:function(t){var e=this.state.curPosition(),i=!1;this.state.pos+=2;var r=this.readInt(t);null==r&&this.raise(Z.InvalidDigit,{at:j(e,2),radix:t});var s=this.input.charCodeAt(this.state.pos);if(110===s)++this.state.pos,i=!0;else if(109===s)throw this.raise(Z.InvalidDecimal,{at:e});if(Zt(this.codePointAtPos(this.state.pos)))throw this.raise(Z.NumberIdentifier,{at:this.state.curPosition()});if(i){var n=this.input.slice(e.index,this.state.pos).replace(/[_n]/g,"");this.finishToken(133,n)}else this.finishToken(132,r)}},{key:"readNumber",value:function(t){var e=this.state.pos,i=this.state.curPosition(),r=!1,s=!1,n=!1,a=!1,o=!1;t||null!==this.readInt(10)||this.raise(Z.InvalidNumber,{at:this.state.curPosition()});var l=this.state.pos-e>=2&&48===this.input.charCodeAt(e);if(l){var c=this.input.slice(e,this.state.pos);if(this.recordStrictModeErrors(Z.StrictOctalLiteral,{at:i}),!this.state.strict){var h=c.indexOf("_");h>0&&this.raise(Z.ZeroDigitNumericSeparator,{at:j(i,h)})}o=l&&!/[89]/.test(c)}var u=this.input.charCodeAt(this.state.pos);if(46!==u||o||(++this.state.pos,this.readInt(10),r=!0,u=this.input.charCodeAt(this.state.pos)),69!==u&&101!==u||o||(u=this.input.charCodeAt(++this.state.pos),43!==u&&45!==u||++this.state.pos,null===this.readInt(10)&&this.raise(Z.InvalidOrMissingExponent,{at:i}),r=!0,a=!0,u=this.input.charCodeAt(this.state.pos)),110===u&&((r||l)&&this.raise(Z.InvalidBigIntLiteral,{at:i}),++this.state.pos,s=!0),109===u&&(this.expectPlugin("decimal",this.state.curPosition()),(a||l)&&this.raise(Z.InvalidDecimal,{at:i}),++this.state.pos,n=!0),Zt(this.codePointAtPos(this.state.pos)))throw this.raise(Z.NumberIdentifier,{at:this.state.curPosition()});var p=this.input.slice(e,this.state.pos).replace(/[_mn]/g,"");if(s)this.finishToken(133,p);else if(n)this.finishToken(134,p);else{var d=o?parseInt(p,8):parseFloat(p);this.finishToken(132,d)}}},{key:"readCodePoint",value:function(t){var e=Di(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,this.errorHandlers_readCodePoint),i=e.code,r=e.pos;return this.state.pos=r,i}},{key:"readString",value:function(t){var e=Ai(34===t?"double":"single",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string),i=e.str,r=e.pos,s=e.curLine,n=e.lineStart;this.state.pos=r+1,this.state.lineStart=n,this.state.curLine=s,this.finishToken(131,i)}},{key:"readTemplateContinuation",value:function(){this.match(8)||this.unexpected(null,8),this.state.pos--,this.readTemplateToken()}},{key:"readTemplateToken",value:function(){var t=this.input[this.state.pos],e=Ai("template",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template),i=e.str,r=e.firstInvalidLoc,s=e.pos,n=e.curLine,a=e.lineStart;this.state.pos=s+1,this.state.lineStart=a,this.state.curLine=n,r&&(this.state.firstInvalidTemplateEscapePos=new _(r.curLine,r.pos-r.lineStart,r.pos)),96===this.input.codePointAt(s)?this.finishToken(24,r?null:t+i+"`"):(this.state.pos++,this.finishToken(25,r?null:t+i+"${"))}},{key:"recordStrictModeErrors",value:function(t,e){var i=e.at,r=i.index;this.state.strict&&!this.state.strictErrors.has(r)?this.raise(t,{at:i}):this.state.strictErrors.set(r,[t,i])}},{key:"readWord1",value:function(t){this.state.containsEsc=!1;var e="",i=this.state.pos,r=this.state.pos;void 0!==t&&(this.state.pos+=t<=65535?1:2);while(this.state.pos<this.length){var s=this.codePointAtPos(this.state.pos);if(te(s))this.state.pos+=s<=65535?1:2;else{if(92!==s)break;this.state.containsEsc=!0,e+=this.input.slice(r,this.state.pos);var n=this.state.curPosition(),a=this.state.pos===i?Zt:te;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(Z.MissingUnicodeEscape,{at:this.state.curPosition()}),r=this.state.pos-1;continue}++this.state.pos;var o=this.readCodePoint(!0);null!==o&&(a(o)||this.raise(Z.EscapedCharNotAnIdentifier,{at:n}),e+=String.fromCodePoint(o)),r=this.state.pos}}return e+this.input.slice(r,this.state.pos)}},{key:"readWord",value:function(t){var e=this.readWord1(t),i=dt.get(e);void 0!==i?this.finishToken(i,zt(i)):this.finishToken(130,e)}},{key:"checkKeywordEscapes",value:function(){var t=this.state.type;Ft(t)&&this.state.containsEsc&&this.raise(Z.InvalidEscapedReservedWord,{at:this.state.startLoc,reservedWord:zt(t)})}},{key:"raise",value:function(t,e){var i=e.at,r=F(e,Oi),s=i instanceof _?i:i.loc.start,n=t({loc:s,details:r});if(!this.options.errorRecovery)throw n;return this.isLookahead||this.state.errors.push(n),n}},{key:"raiseOverwrite",value:function(t,e){for(var i=e.at,r=F(e,Mi),s=i instanceof _?i:i.loc.start,n=s.index,a=this.state.errors,o=a.length-1;o>=0;o--){var l=a[o];if(l.loc.index===n)return a[o]=t({loc:s,details:r});if(l.loc.index<n)break}return this.raise(t,e)}},{key:"updateContext",value:function(t){}},{key:"unexpected",value:function(t,e){throw this.raise(Z.UnexpectedToken,{expected:e?zt(e):null,at:null!=t?t:this.state.startLoc})}},{key:"expectPlugin",value:function(t,e){if(this.hasPlugin(t))return!0;throw this.raise(Z.MissingPlugin,{at:null!=e?e:this.state.startLoc,missingPlugin:[t]})}},{key:"expectOnePlugin",value:function(t){var e=this;if(!t.some((function(t){return e.hasPlugin(t)})))throw this.raise(Z.MissingOneOfPlugins,{at:this.state.startLoc,missingPlugin:t})}},{key:"errorBuilder",value:function(t){var e=this;return function(i,r,s){e.raise(t,{at:Li(i,r,s)})}}}]),i}(fi),ji=D((function t(){L(this,t),this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map})),Ri=function(){function t(e){L(this,t),this.parser=void 0,this.stack=[],this.undefinedPrivateNames=new Map,this.parser=e}return D(t,[{key:"current",value:function(){return this.stack[this.stack.length-1]}},{key:"enter",value:function(){this.stack.push(new ji)}},{key:"exit",value:function(){for(var t=this.stack.pop(),e=this.current(),i=0,r=Array.from(t.undefinedPrivateNames);i<r.length;i++){var s=w(r[i],2),n=s[0],a=s[1];e?e.undefinedPrivateNames.has(n)||e.undefinedPrivateNames.set(n,a):this.parser.raise(Z.InvalidPrivateFieldResolution,{at:a,identifierName:n})}}},{key:"declarePrivateName",value:function(t,e,i){var r=this.current(),s=r.privateNames,n=r.loneAccessors,a=r.undefinedPrivateNames,o=s.has(t);if(e&Ze){var l=o&&n.get(t);if(l){var c=l&Ge,h=e&Ge,u=l&Ze,p=e&Ze;o=u===p||c!==h,o||n.delete(t)}else o||n.set(t,e)}o&&this.parser.raise(Z.PrivateNameRedeclaration,{at:i,identifierName:t}),s.add(t),a.delete(t)}},{key:"usePrivateName",value:function(t,e){var i,r,s=c(this.stack);try{for(s.s();!(r=s.n()).done;)if(i=r.value,i.privateNames.has(t))return}catch(n){s.e(n)}finally{s.f()}i?i.undefinedPrivateNames.set(t,e):this.parser.raise(Z.InvalidPrivateFieldResolution,{at:e,identifierName:t})}}]),t}(),Ui=0,zi=1,Hi=2,Vi=3,qi=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ui;L(this,t),this.type=void 0,this.type=e}return D(t,[{key:"canBeArrowParameterDeclaration",value:function(){return this.type===Hi||this.type===zi}},{key:"isCertainlyParameterDeclaration",value:function(){return this.type===Vi}}]),t}(),Ki=function(t){y(i,t);var e=g(i);function i(t){var r;return L(this,i),r=e.call(this,t),r.declarationErrors=new Map,r}return D(i,[{key:"recordDeclarationError",value:function(t,e){var i=e.at,r=i.index;this.declarationErrors.set(r,[t,i])}},{key:"clearDeclarationError",value:function(t){this.declarationErrors.delete(t)}},{key:"iterateErrors",value:function(t){this.declarationErrors.forEach(t)}}]),i}(qi),Xi=function(){function t(e){L(this,t),this.parser=void 0,this.stack=[new qi],this.parser=e}return D(t,[{key:"enter",value:function(t){this.stack.push(t)}},{key:"exit",value:function(){this.stack.pop()}},{key:"recordParameterInitializerError",value:function(t,e){var i=e.at,r={at:i.loc.start},s=this.stack,n=s.length-1,a=s[n];while(!a.isCertainlyParameterDeclaration()){if(!a.canBeArrowParameterDeclaration())return;a.recordDeclarationError(t,r),a=s[--n]}this.parser.raise(t,r)}},{key:"recordArrowParemeterBindingError",value:function(t,e){var i=e.at,r=this.stack,s=r[r.length-1],n={at:i.loc.start};if(s.isCertainlyParameterDeclaration())this.parser.raise(t,n);else{if(!s.canBeArrowParameterDeclaration())return;s.recordDeclarationError(t,n)}}},{key:"recordAsyncArrowParametersError",value:function(t){var e=t.at,i=this.stack,r=i.length-1,s=i[r];while(s.canBeArrowParameterDeclaration())s.type===Hi&&s.recordDeclarationError(Z.AwaitBindingIdentifier,{at:e}),s=i[--r]}},{key:"validateAsPattern",value:function(){var t=this,e=this.stack,i=e[e.length-1];i.canBeArrowParameterDeclaration()&&i.iterateErrors((function(i){var r=w(i,2),s=r[0],n=r[1];t.parser.raise(s,{at:n});var a=e.length-2,o=e[a];while(o.canBeArrowParameterDeclaration())o.clearDeclarationError(n.index),o=e[--a]}))}}]),t}();function Wi(){return new qi(Vi)}function Ji(){return new Ki(zi)}function Yi(){return new Ki(Hi)}function Gi(){return new qi}var $i=0,Qi=1,Zi=2,tr=4,er=8,ir=function(){function t(){L(this,t),this.stacks=[]}return D(t,[{key:"enter",value:function(t){this.stacks.push(t)}},{key:"exit",value:function(){this.stacks.pop()}},{key:"currentFlags",value:function(){return this.stacks[this.stacks.length-1]}},{key:"hasAwait",get:function(){return(this.currentFlags()&Zi)>0}},{key:"hasYield",get:function(){return(this.currentFlags()&Qi)>0}},{key:"hasReturn",get:function(){return(this.currentFlags()&tr)>0}},{key:"hasIn",get:function(){return(this.currentFlags()&er)>0}}]),t}();function rr(t,e){return(t?Zi:0)|(e?Qi:0)}var sr=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"addExtra",value:function(t,e,i){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(t){var s=t.extra=t.extra||{};r?s[e]=i:Object.defineProperty(s,e,{enumerable:r,value:i})}}},{key:"isContextual",value:function(t){return this.state.type===t&&!this.state.containsEsc}},{key:"isUnparsedContextual",value:function(t,e){var i=t+e.length;if(this.input.slice(t,i)===e){var r=this.input.charCodeAt(i);return!(te(r)||55296===(64512&r))}return!1}},{key:"isLookaheadContextual",value:function(t){var e=this.nextTokenStart();return this.isUnparsedContextual(e,t)}},{key:"eatContextual",value:function(t){return!!this.isContextual(t)&&(this.next(),!0)}},{key:"expectContextual",value:function(t,e){if(!this.eatContextual(t)){if(null!=e)throw this.raise(e,{at:this.state.startLoc});throw this.unexpected(null,t)}}},{key:"canInsertSemicolon",value:function(){return this.match(137)||this.match(8)||this.hasPrecedingLineBreak()}},{key:"hasPrecedingLineBreak",value:function(){return mi.test(this.input.slice(this.state.lastTokEndLoc.index,this.state.start))}},{key:"hasFollowingLineBreak",value:function(){return bi.lastIndex=this.state.end,bi.test(this.input)}},{key:"isLineTerminator",value:function(){return this.eat(13)||this.canInsertSemicolon()}},{key:"semicolon",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];(t?this.isLineTerminator():this.eat(13))||this.raise(Z.MissingSemicolon,{at:this.state.lastTokEndLoc})}},{key:"expect",value:function(t,e){this.eat(t)||this.unexpected(e,t)}},{key:"tryParse",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.clone(),i={node:null};try{var r=t((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;throw i.node=t,i}));if(this.state.errors.length>e.errors.length){var s=this.state;return this.state=e,this.state.tokensLength=s.tokensLength,{node:r,error:s.errors[e.errors.length],thrown:!1,aborted:!1,failState:s}}return{node:r,error:null,thrown:!1,aborted:!1,failState:null}}catch(a){var n=this.state;if(this.state=e,a instanceof SyntaxError)return{node:null,error:a,thrown:!0,aborted:!1,failState:n};if(a===i)return{node:i.node,error:null,thrown:!1,aborted:!0,failState:n};throw a}}},{key:"checkExpressionErrors",value:function(t,e){if(!t)return!1;var i=t.shorthandAssignLoc,r=t.doubleProtoLoc,s=t.privateKeyLoc,n=t.optionalParametersLoc,a=!!i||!!r||!!n||!!s;if(!e)return a;null!=i&&this.raise(Z.InvalidCoverInitializedName,{at:i}),null!=r&&this.raise(Z.DuplicateProto,{at:r}),null!=s&&this.raise(Z.UnexpectedPrivateField,{at:s}),null!=n&&this.unexpected(n)}},{key:"isLiteralPropertyName",value:function(){return It(this.state.type)}},{key:"isPrivateName",value:function(t){return"PrivateName"===t.type}},{key:"getPrivateNameSV",value:function(t){return t.id.name}},{key:"hasPropertyAsPrivateName",value:function(t){return("MemberExpression"===t.type||"OptionalMemberExpression"===t.type)&&this.isPrivateName(t.property)}},{key:"isOptionalChain",value:function(t){return"OptionalMemberExpression"===t.type||"OptionalCallExpression"===t.type}},{key:"isObjectProperty",value:function(t){return"ObjectProperty"===t.type}},{key:"isObjectMethod",value:function(t){return"ObjectMethod"===t.type}},{key:"initializeScopes",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"module"===this.options.sourceType,i=this.state.labels;this.state.labels=[];var r=this.exportedIdentifiers;this.exportedIdentifiers=new Set;var s=this.inModule;this.inModule=e;var n=this.scope,a=this.getScopeHandler();this.scope=new a(this,e);var o=this.prodParam;this.prodParam=new ir;var l=this.classScope;this.classScope=new Ri(this);var c=this.expressionScope;return this.expressionScope=new Xi(this),function(){t.state.labels=i,t.exportedIdentifiers=r,t.inModule=s,t.scope=n,t.prodParam=o,t.classScope=l,t.expressionScope=c}}},{key:"enterInitialScopes",value:function(){var t=$i;this.inModule&&(t|=Zi),this.scope.enter(fe),this.prodParam.enter(t)}},{key:"checkDestructuringPrivate",value:function(t){var e=t.privateKeyLoc;null!==e&&this.expectPlugin("destructuringPrivate",e)}}]),i}(Bi),nr=D((function t(){L(this,t),this.shorthandAssignLoc=null,this.doubleProtoLoc=null,this.privateKeyLoc=null,this.optionalParametersLoc=null})),ar=D((function t(e,i,r){L(this,t),this.type="",this.start=i,this.end=0,this.loc=new B(r),null!=e&&e.options.ranges&&(this.range=[i,0]),null!=e&&e.filename&&(this.loc.filename=e.filename)})),or=ar.prototype;function lr(t){return cr(t)}function cr(t){var e=t.type,i=t.start,r=t.end,s=t.loc,n=t.range,a=t.extra,o=t.name,l=Object.create(or);return l.type=e,l.start=i,l.end=r,l.loc=s,l.range=n,l.extra=a,l.name=o,"Placeholder"===e&&(l.expectedNode=t.expectedNode),l}function hr(t){var e=t.type,i=t.start,r=t.end,s=t.loc,n=t.range,a=t.extra;if("Placeholder"===e)return lr(t);var o=Object.create(or);return o.type=e,o.start=i,o.end=r,o.loc=s,o.range=n,void 0!==t.raw?o.raw=t.raw:o.extra=a,o.value=t.value,o}or.__clone=function(){for(var t=new ar(void 0,this.start,this.loc.start),e=Object.keys(this),i=0,r=e.length;i<r;i++){var s=e[i];"leadingComments"!==s&&"trailingComments"!==s&&"innerComments"!==s&&(t[s]=this[s])}return t};var ur=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"startNode",value:function(){return new ar(this,this.state.start,this.state.startLoc)}},{key:"startNodeAt",value:function(t){return new ar(this,t.index,t)}},{key:"startNodeAtNode",value:function(t){return this.startNodeAt(t.loc.start)}},{key:"finishNode",value:function(t,e){return this.finishNodeAt(t,e,this.state.lastTokEndLoc)}},{key:"finishNodeAt",value:function(t,e,i){return t.type=e,t.end=i.index,t.loc.end=i,this.options.ranges&&(t.range[1]=i.index),this.options.attachComment&&this.processComment(t),t}},{key:"resetStartLocation",value:function(t,e){t.start=e.index,t.loc.start=e,this.options.ranges&&(t.range[0]=e.index)}},{key:"resetEndLocation",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.lastTokEndLoc;t.end=e.index,t.loc.end=e,this.options.ranges&&(t.range[1]=e.index)}},{key:"resetStartLocationFromNode",value:function(t,e){this.resetStartLocation(t,e.loc.start)}}]),i}(sr),pr=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),dr=Q(n||(n=T(["flow"])))({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:function(t){var e=t.reservedType;return"Cannot overwrite reserved type ".concat(e,".")},DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:function(t){var e=t.memberName,i=t.enumName;return"Boolean enum members need to be initialized. Use either `".concat(e," = true,` or `").concat(e," = false,` in enum `").concat(i,"`.")},EnumDuplicateMemberName:function(t){var e=t.memberName,i=t.enumName;return"Enum member names need to be unique, but the name `".concat(e,"` has already been used before in enum `").concat(i,"`.")},EnumInconsistentMemberValues:function(t){var e=t.enumName;return"Enum `".concat(e,"` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.")},EnumInvalidExplicitType:function(t){var e=t.invalidEnumType,i=t.enumName;return"Enum type `".concat(e,"` is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `").concat(i,"`.")},EnumInvalidExplicitTypeUnknownSupplied:function(t){var e=t.enumName;return"Supplied enum type is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `".concat(e,"`.")},EnumInvalidMemberInitializerPrimaryType:function(t){var e=t.enumName,i=t.memberName,r=t.explicitType;return"Enum `".concat(e,"` has type `").concat(r,"`, so the initializer of `").concat(i,"` needs to be a ").concat(r," literal.")},EnumInvalidMemberInitializerSymbolType:function(t){var e=t.enumName,i=t.memberName;return"Symbol enum members cannot be initialized. Use `".concat(i,",` in enum `").concat(e,"`.")},EnumInvalidMemberInitializerUnknownType:function(t){var e=t.enumName,i=t.memberName;return"The enum member initializer for `".concat(i,"` needs to be a literal (either a boolean, number, or string) in enum `").concat(e,"`.")},EnumInvalidMemberName:function(t){var e=t.enumName,i=t.memberName,r=t.suggestion;return"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `".concat(i,"`, consider using `").concat(r,"`, in enum `").concat(e,"`.")},EnumNumberMemberNotInitialized:function(t){var e=t.enumName,i=t.memberName;return"Number enum members need to be initialized, e.g. `".concat(i," = 1` in enum `").concat(e,"`.")},EnumStringMemberInconsistentlyInitailized:function(t){var e=t.enumName;return"String enum members need to consistently either all use initializers, or use no initializers, in enum `".concat(e,"`.")},GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` or `typeof` keyword.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",PatternIsOptional:Object.assign({message:"A binding pattern parameter cannot be optional in an implementation signature."},{reasonCode:"OptionalBindingPattern"}),SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:function(t){var e=t.reservedType;return"Unexpected reserved type ".concat(e,".")},UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:function(t){var e=t.unsupportedExportKind,i=t.suggestion;return"`declare export ".concat(e,"` is not supported. Use `").concat(i,"` instead.")},UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."});function fr(t){return"DeclareExportAllDeclaration"===t.type||"DeclareExportDeclaration"===t.type&&(!t.declaration||"TypeAlias"!==t.declaration.type&&"InterfaceDeclaration"!==t.declaration.type)}function mr(t){return"type"===t.importKind||"typeof"===t.importKind}function yr(t){return Ct(t)&&97!==t}var vr={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};function gr(t,e){for(var i=[],r=[],s=0;s<t.length;s++)(e(t[s],s,t)?i:r).push(t[s]);return[i,r]}var xr=/\*?\s*@((?:no)?flow)\b/,br=function(t){return function(t){y(i,t);var e=g(i);function i(){var t;L(this,i);for(var r=arguments.length,s=new Array(r),n=0;n<r;n++)s[n]=arguments[n];return t=e.call.apply(e,[this].concat(s)),t.flowPragma=void 0,t}return D(i,[{key:"getScopeHandler",value:function(){return li}},{key:"shouldParseTypes",value:function(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}},{key:"shouldParseEnums",value:function(){return!!this.getPluginOption("flow","enums")}},{key:"finishToken",value:function(t,e){return 131!==t&&13!==t&&28!==t&&void 0===this.flowPragma&&(this.flowPragma=null),f(k(i.prototype),"finishToken",this).call(this,t,e)}},{key:"addComment",value:function(t){if(void 0===this.flowPragma){var e=xr.exec(t.value);if(e)if("flow"===e[1])this.flowPragma="flow";else{if("noflow"!==e[1])throw new Error("Unexpected flow pragma");this.flowPragma="noflow"}else;}return f(k(i.prototype),"addComment",this).call(this,t)}},{key:"flowParseTypeInitialiser",value:function(t){var e=this.state.inType;this.state.inType=!0,this.expect(t||14);var i=this.flowParseType();return this.state.inType=e,i}},{key:"flowParsePredicate",value:function(){var t=this.startNode(),e=this.state.startLoc;return this.next(),this.expectContextual(108),this.state.lastTokStart>e.index+1&&this.raise(dr.UnexpectedSpaceBetweenModuloChecks,{at:e}),this.eat(10)?(t.value=f(k(i.prototype),"parseExpression",this).call(this),this.expect(11),this.finishNode(t,"DeclaredPredicate")):this.finishNode(t,"InferredPredicate")}},{key:"flowParseTypeAndPredicateInitialiser",value:function(){var t=this.state.inType;this.state.inType=!0,this.expect(14);var e=null,i=null;return this.match(54)?(this.state.inType=t,i=this.flowParsePredicate()):(e=this.flowParseType(),this.state.inType=t,this.match(54)&&(i=this.flowParsePredicate())),[e,i]}},{key:"flowParseDeclareClass",value:function(t){return this.next(),this.flowParseInterfaceish(t,!0),this.finishNode(t,"DeclareClass")}},{key:"flowParseDeclareFunction",value:function(t){this.next();var e=t.id=this.parseIdentifier(),i=this.startNode(),r=this.startNode();this.match(47)?i.typeParameters=this.flowParseTypeParameterDeclaration():i.typeParameters=null,this.expect(10);var s=this.flowParseFunctionTypeParams();i.params=s.params,i.rest=s.rest,i.this=s._this,this.expect(11);var n=this.flowParseTypeAndPredicateInitialiser(),a=w(n,2);return i.returnType=a[0],t.predicate=a[1],r.typeAnnotation=this.finishNode(i,"FunctionTypeAnnotation"),e.typeAnnotation=this.finishNode(r,"TypeAnnotation"),this.resetEndLocation(e),this.semicolon(),this.scope.declareName(t.id.name,Ye,t.id.loc.start),this.finishNode(t,"DeclareFunction")}},{key:"flowParseDeclare",value:function(t,e){if(this.match(80))return this.flowParseDeclareClass(t);if(this.match(68))return this.flowParseDeclareFunction(t);if(this.match(74))return this.flowParseDeclareVariable(t);if(this.eatContextual(125))return this.match(16)?this.flowParseDeclareModuleExports(t):(e&&this.raise(dr.NestedDeclareModule,{at:this.state.lastTokStartLoc}),this.flowParseDeclareModule(t));if(this.isContextual(128))return this.flowParseDeclareTypeAlias(t);if(this.isContextual(129))return this.flowParseDeclareOpaqueType(t);if(this.isContextual(127))return this.flowParseDeclareInterface(t);if(this.match(82))return this.flowParseDeclareExportDeclaration(t,e);throw this.unexpected()}},{key:"flowParseDeclareVariable",value:function(t){return this.next(),t.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(t.id.name,je,t.id.loc.start),this.semicolon(),this.finishNode(t,"DeclareVariable")}},{key:"flowParseDeclareModule",value:function(t){var e=this;this.scope.enter(de),this.match(131)?t.id=f(k(i.prototype),"parseExprAtom",this).call(this):t.id=this.parseIdentifier();var r=t.body=this.startNode(),s=r.body=[];this.expect(5);while(!this.match(8)){var n=this.startNode();this.match(83)?(this.next(),this.isContextual(128)||this.match(87)||this.raise(dr.InvalidNonTypeImportInDeclareModule,{at:this.state.lastTokStartLoc}),f(k(i.prototype),"parseImport",this).call(this,n)):(this.expectContextual(123,dr.UnsupportedStatementInDeclareModule),n=this.flowParseDeclare(n,!0)),s.push(n)}this.scope.exit(),this.expect(8),this.finishNode(r,"BlockStatement");var a=null,o=!1;return s.forEach((function(t){fr(t)?("CommonJS"===a&&e.raise(dr.AmbiguousDeclareModuleKind,{at:t}),a="ES"):"DeclareModuleExports"===t.type&&(o&&e.raise(dr.DuplicateDeclareModuleExports,{at:t}),"ES"===a&&e.raise(dr.AmbiguousDeclareModuleKind,{at:t}),a="CommonJS",o=!0)})),t.kind=a||"CommonJS",this.finishNode(t,"DeclareModule")}},{key:"flowParseDeclareExportDeclaration",value:function(t,e){if(this.expect(82),this.eat(65))return this.match(68)||this.match(80)?t.declaration=this.flowParseDeclare(this.startNode()):(t.declaration=this.flowParseType(),this.semicolon()),t.default=!0,this.finishNode(t,"DeclareExportDeclaration");if(this.match(75)||this.isLet()||(this.isContextual(128)||this.isContextual(127))&&!e){var i=this.state.value;throw this.raise(dr.UnsupportedDeclareExportKind,{at:this.state.startLoc,unsupportedExportKind:i,suggestion:vr[i]})}if(this.match(74)||this.match(68)||this.match(80)||this.isContextual(129))return t.declaration=this.flowParseDeclare(this.startNode()),t.default=!1,this.finishNode(t,"DeclareExportDeclaration");if(this.match(55)||this.match(5)||this.isContextual(127)||this.isContextual(128)||this.isContextual(129))return t=this.parseExport(t,null),"ExportNamedDeclaration"===t.type&&(t.type="ExportDeclaration",t.default=!1,delete t.exportKind),t.type="Declare"+t.type,t;throw this.unexpected()}},{key:"flowParseDeclareModuleExports",value:function(t){return this.next(),this.expectContextual(109),t.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(t,"DeclareModuleExports")}},{key:"flowParseDeclareTypeAlias",value:function(t){this.next();var e=this.flowParseTypeAlias(t);return e.type="DeclareTypeAlias",e}},{key:"flowParseDeclareOpaqueType",value:function(t){this.next();var e=this.flowParseOpaqueType(t,!0);return e.type="DeclareOpaqueType",e}},{key:"flowParseDeclareInterface",value:function(t){return this.next(),this.flowParseInterfaceish(t),this.finishNode(t,"DeclareInterface")}},{key:"flowParseInterfaceish",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.id=this.flowParseRestrictedIdentifier(!e,!0),this.scope.declareName(t.id.name,e?Re:Be,t.id.loc.start),this.match(47)?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.extends=[],t.implements=[],t.mixins=[],this.eat(81))do{t.extends.push(this.flowParseInterfaceExtends())}while(!e&&this.eat(12));if(this.isContextual(115)){this.next();do{t.mixins.push(this.flowParseInterfaceExtends())}while(this.eat(12))}if(this.isContextual(111)){this.next();do{t.implements.push(this.flowParseInterfaceExtends())}while(this.eat(12))}t.body=this.flowParseObjectType({allowStatic:e,allowExact:!1,allowSpread:!1,allowProto:e,allowInexact:!1})}},{key:"flowParseInterfaceExtends",value:function(){var t=this.startNode();return t.id=this.flowParseQualifiedTypeIdentifier(),this.match(47)?t.typeParameters=this.flowParseTypeParameterInstantiation():t.typeParameters=null,this.finishNode(t,"InterfaceExtends")}},{key:"flowParseInterface",value:function(t){return this.flowParseInterfaceish(t),this.finishNode(t,"InterfaceDeclaration")}},{key:"checkNotUnderscore",value:function(t){"_"===t&&this.raise(dr.UnexpectedReservedUnderscore,{at:this.state.startLoc})}},{key:"checkReservedType",value:function(t,e,i){pr.has(t)&&this.raise(i?dr.AssignReservedType:dr.UnexpectedReservedType,{at:e,reservedType:t})}},{key:"flowParseRestrictedIdentifier",value:function(t,e){return this.checkReservedType(this.state.value,this.state.startLoc,e),this.parseIdentifier(t)}},{key:"flowParseTypeAlias",value:function(t){return t.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(t.id.name,Be,t.id.loc.start),this.match(47)?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.right=this.flowParseTypeInitialiser(29),this.semicolon(),this.finishNode(t,"TypeAlias")}},{key:"flowParseOpaqueType",value:function(t,e){return this.expectContextual(128),t.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(t.id.name,Be,t.id.loc.start),this.match(47)?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.supertype=null,this.match(14)&&(t.supertype=this.flowParseTypeInitialiser(14)),t.impltype=null,e||(t.impltype=this.flowParseTypeInitialiser(29)),this.semicolon(),this.finishNode(t,"OpaqueType")}},{key:"flowParseTypeParameter",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.state.startLoc,i=this.startNode(),r=this.flowParseVariance(),s=this.flowParseTypeAnnotatableIdentifier();return i.name=s.name,i.variance=r,i.bound=s.typeAnnotation,this.match(29)?(this.eat(29),i.default=this.flowParseType()):t&&this.raise(dr.MissingTypeParamDefault,{at:e}),this.finishNode(i,"TypeParameter")}},{key:"flowParseTypeParameterDeclaration",value:function(){var t=this.state.inType,e=this.startNode();e.params=[],this.state.inType=!0,this.match(47)||this.match(140)?this.next():this.unexpected();var i=!1;do{var r=this.flowParseTypeParameter(i);e.params.push(r),r.default&&(i=!0),this.match(48)||this.expect(12)}while(!this.match(48));return this.expect(48),this.state.inType=t,this.finishNode(e,"TypeParameterDeclaration")}},{key:"flowParseTypeParameterInstantiation",value:function(){var t=this.startNode(),e=this.state.inType;t.params=[],this.state.inType=!0,this.expect(47);var i=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!1;while(!this.match(48))t.params.push(this.flowParseType()),this.match(48)||this.expect(12);return this.state.noAnonFunctionType=i,this.expect(48),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}},{key:"flowParseTypeParameterInstantiationCallOrNew",value:function(){var t=this.startNode(),e=this.state.inType;t.params=[],this.state.inType=!0,this.expect(47);while(!this.match(48))t.params.push(this.flowParseTypeOrImplicitInstantiation()),this.match(48)||this.expect(12);return this.expect(48),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}},{key:"flowParseInterfaceType",value:function(){var t=this.startNode();if(this.expectContextual(127),t.extends=[],this.eat(81))do{t.extends.push(this.flowParseInterfaceExtends())}while(this.eat(12));return t.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(t,"InterfaceTypeAnnotation")}},{key:"flowParseObjectPropertyKey",value:function(){return this.match(132)||this.match(131)?f(k(i.prototype),"parseExprAtom",this).call(this):this.parseIdentifier(!0)}},{key:"flowParseObjectTypeIndexer",value:function(t,e,i){return t.static=e,14===this.lookahead().type?(t.id=this.flowParseObjectPropertyKey(),t.key=this.flowParseTypeInitialiser()):(t.id=null,t.key=this.flowParseType()),this.expect(3),t.value=this.flowParseTypeInitialiser(),t.variance=i,this.finishNode(t,"ObjectTypeIndexer")}},{key:"flowParseObjectTypeInternalSlot",value:function(t,e){return t.static=e,t.id=this.flowParseObjectPropertyKey(),this.expect(3),this.expect(3),this.match(47)||this.match(10)?(t.method=!0,t.optional=!1,t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.loc.start))):(t.method=!1,this.eat(17)&&(t.optional=!0),t.value=this.flowParseTypeInitialiser()),this.finishNode(t,"ObjectTypeInternalSlot")}},{key:"flowParseObjectTypeMethodish",value:function(t){t.params=[],t.rest=null,t.typeParameters=null,t.this=null,this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(10),this.match(78)&&(t.this=this.flowParseFunctionTypeParam(!0),t.this.name=null,this.match(11)||this.expect(12));while(!this.match(11)&&!this.match(21))t.params.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(t.rest=this.flowParseFunctionTypeParam(!1)),this.expect(11),t.returnType=this.flowParseTypeInitialiser(),this.finishNode(t,"FunctionTypeAnnotation")}},{key:"flowParseObjectTypeCallProperty",value:function(t,e){var i=this.startNode();return t.static=e,t.value=this.flowParseObjectTypeMethodish(i),this.finishNode(t,"ObjectTypeCallProperty")}},{key:"flowParseObjectType",value:function(t){var e=t.allowStatic,i=t.allowExact,r=t.allowSpread,s=t.allowProto,n=t.allowInexact,a=this.state.inType;this.state.inType=!0;var o,l,c=this.startNode();c.callProperties=[],c.properties=[],c.indexers=[],c.internalSlots=[];var h=!1;i&&this.match(6)?(this.expect(6),o=9,l=!0):(this.expect(5),o=8,l=!1),c.exact=l;while(!this.match(o)){var u=!1,p=null,d=null,f=this.startNode();if(s&&this.isContextual(116)){var m=this.lookahead();14!==m.type&&17!==m.type&&(this.next(),p=this.state.startLoc,e=!1)}if(e&&this.isContextual(104)){var y=this.lookahead();14!==y.type&&17!==y.type&&(this.next(),u=!0)}var v=this.flowParseVariance();if(this.eat(0))null!=p&&this.unexpected(p),this.eat(0)?(v&&this.unexpected(v.loc.start),c.internalSlots.push(this.flowParseObjectTypeInternalSlot(f,u))):c.indexers.push(this.flowParseObjectTypeIndexer(f,u,v));else if(this.match(10)||this.match(47))null!=p&&this.unexpected(p),v&&this.unexpected(v.loc.start),c.callProperties.push(this.flowParseObjectTypeCallProperty(f,u));else{var g="init";if(this.isContextual(98)||this.isContextual(103)){var x=this.lookahead();It(x.type)&&(g=this.state.value,this.next())}var b=this.flowParseObjectTypeProperty(f,u,p,v,g,r,null!=n?n:!l);null===b?(h=!0,d=this.state.lastTokStartLoc):c.properties.push(b)}this.flowObjectTypeSemicolon(),!d||this.match(8)||this.match(9)||this.raise(dr.UnexpectedExplicitInexactInObject,{at:d})}this.expect(o),r&&(c.inexact=h);var P=this.finishNode(c,"ObjectTypeAnnotation");return this.state.inType=a,P}},{key:"flowParseObjectTypeProperty",value:function(t,e,i,r,s,n,a){if(this.eat(21)){var o=this.match(12)||this.match(13)||this.match(8)||this.match(9);return o?(n?a||this.raise(dr.InexactInsideExact,{at:this.state.lastTokStartLoc}):this.raise(dr.InexactInsideNonObject,{at:this.state.lastTokStartLoc}),r&&this.raise(dr.InexactVariance,{at:r}),null):(n||this.raise(dr.UnexpectedSpreadType,{at:this.state.lastTokStartLoc}),null!=i&&this.unexpected(i),r&&this.raise(dr.SpreadVariance,{at:r}),t.argument=this.flowParseType(),this.finishNode(t,"ObjectTypeSpreadProperty"))}t.key=this.flowParseObjectPropertyKey(),t.static=e,t.proto=null!=i,t.kind=s;var l=!1;return this.match(47)||this.match(10)?(t.method=!0,null!=i&&this.unexpected(i),r&&this.unexpected(r.loc.start),t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.loc.start)),"get"!==s&&"set"!==s||this.flowCheckGetterSetterParams(t),!n&&"constructor"===t.key.name&&t.value.this&&this.raise(dr.ThisParamBannedInConstructor,{at:t.value.this})):("init"!==s&&this.unexpected(),t.method=!1,this.eat(17)&&(l=!0),t.value=this.flowParseTypeInitialiser(),t.variance=r),t.optional=l,this.finishNode(t,"ObjectTypeProperty")}},{key:"flowCheckGetterSetterParams",value:function(t){var e="get"===t.kind?0:1,i=t.value.params.length+(t.value.rest?1:0);t.value.this&&this.raise("get"===t.kind?dr.GetterMayNotHaveThisParam:dr.SetterMayNotHaveThisParam,{at:t.value.this}),i!==e&&this.raise("get"===t.kind?Z.BadGetterArity:Z.BadSetterArity,{at:t}),"set"===t.kind&&t.value.rest&&this.raise(Z.BadSetterRestParameter,{at:t})}},{key:"flowObjectTypeSemicolon",value:function(){this.eat(13)||this.eat(12)||this.match(8)||this.match(9)||this.unexpected()}},{key:"flowParseQualifiedTypeIdentifier",value:function(t,e){null!=t||(t=this.state.startLoc);var i=e||this.flowParseRestrictedIdentifier(!0);while(this.eat(16)){var r=this.startNodeAt(t);r.qualification=i,r.id=this.flowParseRestrictedIdentifier(!0),i=this.finishNode(r,"QualifiedTypeIdentifier")}return i}},{key:"flowParseGenericType",value:function(t,e){var i=this.startNodeAt(t);return i.typeParameters=null,i.id=this.flowParseQualifiedTypeIdentifier(t,e),this.match(47)&&(i.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(i,"GenericTypeAnnotation")}},{key:"flowParseTypeofType",value:function(){var t=this.startNode();return this.expect(87),t.argument=this.flowParsePrimaryType(),this.finishNode(t,"TypeofTypeAnnotation")}},{key:"flowParseTupleType",value:function(){var t=this.startNode();t.types=[],this.expect(0);while(this.state.pos<this.length&&!this.match(3)){if(t.types.push(this.flowParseType()),this.match(3))break;this.expect(12)}return this.expect(3),this.finishNode(t,"TupleTypeAnnotation")}},{key:"flowParseFunctionTypeParam",value:function(t){var e=null,i=!1,r=null,s=this.startNode(),n=this.lookahead(),a=78===this.state.type;return 14===n.type||17===n.type?(a&&!t&&this.raise(dr.ThisParamMustBeFirst,{at:s}),e=this.parseIdentifier(a),this.eat(17)&&(i=!0,a&&this.raise(dr.ThisParamMayNotBeOptional,{at:s})),r=this.flowParseTypeInitialiser()):r=this.flowParseType(),s.name=e,s.optional=i,s.typeAnnotation=r,this.finishNode(s,"FunctionTypeParam")}},{key:"reinterpretTypeAsFunctionTypeParam",value:function(t){var e=this.startNodeAt(t.loc.start);return e.name=null,e.optional=!1,e.typeAnnotation=t,this.finishNode(e,"FunctionTypeParam")}},{key:"flowParseFunctionTypeParams",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=null,i=null;this.match(78)&&(i=this.flowParseFunctionTypeParam(!0),i.name=null,this.match(11)||this.expect(12));while(!this.match(11)&&!this.match(21))t.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(e=this.flowParseFunctionTypeParam(!1)),{params:t,rest:e,_this:i}}},{key:"flowIdentToTypeAnnotation",value:function(t,e,i){switch(i.name){case"any":return this.finishNode(e,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(e,"BooleanTypeAnnotation");case"mixed":return this.finishNode(e,"MixedTypeAnnotation");case"empty":return this.finishNode(e,"EmptyTypeAnnotation");case"number":return this.finishNode(e,"NumberTypeAnnotation");case"string":return this.finishNode(e,"StringTypeAnnotation");case"symbol":return this.finishNode(e,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(i.name),this.flowParseGenericType(t,i)}}},{key:"flowParsePrimaryType",value:function(){var t,e,r=this.state.startLoc,s=this.startNode(),n=!1,a=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case 6:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case 0:return this.state.noAnonFunctionType=!1,e=this.flowParseTupleType(),this.state.noAnonFunctionType=a,e;case 47:return s.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(10),t=this.flowParseFunctionTypeParams(),s.params=t.params,s.rest=t.rest,s.this=t._this,this.expect(11),this.expect(19),s.returnType=this.flowParseType(),this.finishNode(s,"FunctionTypeAnnotation");case 10:if(this.next(),!this.match(11)&&!this.match(21))if(At(this.state.type)||this.match(78)){var o=this.lookahead().type;n=17!==o&&14!==o}else n=!0;if(n){if(this.state.noAnonFunctionType=!1,e=this.flowParseType(),this.state.noAnonFunctionType=a,this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&19===this.lookahead().type))return this.expect(11),e;this.eat(12)}return t=e?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(e)]):this.flowParseFunctionTypeParams(),s.params=t.params,s.rest=t.rest,s.this=t._this,this.expect(11),this.expect(19),s.returnType=this.flowParseType(),s.typeParameters=null,this.finishNode(s,"FunctionTypeAnnotation");case 131:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case 85:case 86:return s.value=this.match(85),this.next(),this.finishNode(s,"BooleanLiteralTypeAnnotation");case 53:if("-"===this.state.value){if(this.next(),this.match(132))return this.parseLiteralAtNode(-this.state.value,"NumberLiteralTypeAnnotation",s);if(this.match(133))return this.parseLiteralAtNode(-this.state.value,"BigIntLiteralTypeAnnotation",s);throw this.raise(dr.UnexpectedSubtractionOperand,{at:this.state.startLoc})}throw this.unexpected();case 132:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case 133:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case 88:return this.next(),this.finishNode(s,"VoidTypeAnnotation");case 84:return this.next(),this.finishNode(s,"NullLiteralTypeAnnotation");case 78:return this.next(),this.finishNode(s,"ThisTypeAnnotation");case 55:return this.next(),this.finishNode(s,"ExistsTypeAnnotation");case 87:return this.flowParseTypeofType();default:if(Ft(this.state.type)){var l=zt(this.state.type);return this.next(),f(k(i.prototype),"createIdentifier",this).call(this,s,l)}if(At(this.state.type))return this.isContextual(127)?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(r,s,this.parseIdentifier())}throw this.unexpected()}},{key:"flowParsePostfixType",value:function(){var t=this.state.startLoc,e=this.flowParsePrimaryType(),i=!1;while((this.match(0)||this.match(18))&&!this.canInsertSemicolon()){var r=this.startNodeAt(t),s=this.eat(18);i=i||s,this.expect(0),!s&&this.match(3)?(r.elementType=e,this.next(),e=this.finishNode(r,"ArrayTypeAnnotation")):(r.objectType=e,r.indexType=this.flowParseType(),this.expect(3),i?(r.optional=s,e=this.finishNode(r,"OptionalIndexedAccessType")):e=this.finishNode(r,"IndexedAccessType"))}return e}},{key:"flowParsePrefixType",value:function(){var t=this.startNode();return this.eat(17)?(t.typeAnnotation=this.flowParsePrefixType(),this.finishNode(t,"NullableTypeAnnotation")):this.flowParsePostfixType()}},{key:"flowParseAnonFunctionWithoutParens",value:function(){var t=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){var e=this.startNodeAt(t.loc.start);return e.params=[this.reinterpretTypeAsFunctionTypeParam(t)],e.rest=null,e.this=null,e.returnType=this.flowParseType(),e.typeParameters=null,this.finishNode(e,"FunctionTypeAnnotation")}return t}},{key:"flowParseIntersectionType",value:function(){var t=this.startNode();this.eat(45);var e=this.flowParseAnonFunctionWithoutParens();t.types=[e];while(this.eat(45))t.types.push(this.flowParseAnonFunctionWithoutParens());return 1===t.types.length?e:this.finishNode(t,"IntersectionTypeAnnotation")}},{key:"flowParseUnionType",value:function(){var t=this.startNode();this.eat(43);var e=this.flowParseIntersectionType();t.types=[e];while(this.eat(43))t.types.push(this.flowParseIntersectionType());return 1===t.types.length?e:this.finishNode(t,"UnionTypeAnnotation")}},{key:"flowParseType",value:function(){var t=this.state.inType;this.state.inType=!0;var e=this.flowParseUnionType();return this.state.inType=t,e}},{key:"flowParseTypeOrImplicitInstantiation",value:function(){if(130===this.state.type&&"_"===this.state.value){var t=this.state.startLoc,e=this.parseIdentifier();return this.flowParseGenericType(t,e)}return this.flowParseType()}},{key:"flowParseTypeAnnotation",value:function(){var t=this.startNode();return t.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(t,"TypeAnnotation")}},{key:"flowParseTypeAnnotatableIdentifier",value:function(t){var e=t?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e)),e}},{key:"typeCastToParameter",value:function(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.loc.end),t.expression}},{key:"flowParseVariance",value:function(){var t=null;return this.match(53)?(t=this.startNode(),"+"===this.state.value?t.kind="plus":t.kind="minus",this.next(),this.finishNode(t,"Variance")):t}},{key:"parseFunctionBody",value:function(t,e){var r=this,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return e?this.forwardNoArrowParamsConversionAt(t,(function(){return f(k(i.prototype),"parseFunctionBody",r).call(r,t,!0,s)})):f(k(i.prototype),"parseFunctionBody",this).call(this,t,!1,s)}},{key:"parseFunctionBodyAndFinish",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.match(14)){var s=this.startNode(),n=this.flowParseTypeAndPredicateInitialiser(),a=w(n,2);s.typeAnnotation=a[0],t.predicate=a[1],t.returnType=s.typeAnnotation?this.finishNode(s,"TypeAnnotation"):null}return f(k(i.prototype),"parseFunctionBodyAndFinish",this).call(this,t,e,r)}},{key:"parseStatementLike",value:function(t){if(this.state.strict&&this.isContextual(127)){var e=this.lookahead();if(Ct(e.type)){var r=this.startNode();return this.next(),this.flowParseInterface(r)}}else if(this.shouldParseEnums()&&this.isContextual(124)){var s=this.startNode();return this.next(),this.flowParseEnumDeclaration(s)}var n=f(k(i.prototype),"parseStatementLike",this).call(this,t);return void 0!==this.flowPragma||this.isValidDirective(n)||(this.flowPragma=null),n}},{key:"parseExpressionStatement",value:function(t,e,r){if("Identifier"===e.type)if("declare"===e.name){if(this.match(80)||At(this.state.type)||this.match(68)||this.match(74)||this.match(82))return this.flowParseDeclare(t)}else if(At(this.state.type)){if("interface"===e.name)return this.flowParseInterface(t);if("type"===e.name)return this.flowParseTypeAlias(t);if("opaque"===e.name)return this.flowParseOpaqueType(t,!1)}return f(k(i.prototype),"parseExpressionStatement",this).call(this,t,e,r)}},{key:"shouldParseExportDeclaration",value:function(){var t=this.state.type;return Mt(t)||this.shouldParseEnums()&&124===t?!this.state.containsEsc:f(k(i.prototype),"shouldParseExportDeclaration",this).call(this)}},{key:"isExportDefaultSpecifier",value:function(){var t=this.state.type;return Mt(t)||this.shouldParseEnums()&&124===t?this.state.containsEsc:f(k(i.prototype),"isExportDefaultSpecifier",this).call(this)}},{key:"parseExportDefaultExpression",value:function(){if(this.shouldParseEnums()&&this.isContextual(124)){var t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}return f(k(i.prototype),"parseExportDefaultExpression",this).call(this)}},{key:"parseConditional",value:function(t,e,i){var r=this;if(!this.match(17))return t;if(this.state.maybeInArrowParameters){var s=this.lookaheadCharCode();if(44===s||61===s||58===s||41===s)return this.setOptionalParametersError(i),t}this.expect(17);var n=this.state.clone(),a=this.state.noArrowAt,o=this.startNodeAt(e),l=this.tryParseConditionalConsequent(),c=l.consequent,u=l.failed,p=this.getArrowLikeExpressions(c),d=w(p,2),f=d[0],m=d[1];if(u||m.length>0){var y=h(a);if(m.length>0){this.state=n,this.state.noArrowAt=y;for(var v=0;v<m.length;v++)y.push(m[v].start);var g=this.tryParseConditionalConsequent();c=g.consequent,u=g.failed;var x=this.getArrowLikeExpressions(c),b=w(x,2);f=b[0],m=b[1]}if(u&&f.length>1&&this.raise(dr.AmbiguousConditionalArrow,{at:n.startLoc}),u&&1===f.length){this.state=n,y.push(f[0].start),this.state.noArrowAt=y;var P=this.tryParseConditionalConsequent();c=P.consequent,u=P.failed}}return this.getArrowLikeExpressions(c,!0),this.state.noArrowAt=a,this.expect(14),o.test=t,o.consequent=c,o.alternate=this.forwardNoArrowParamsConversionAt(o,(function(){return r.parseMaybeAssign(void 0,void 0)})),this.finishNode(o,"ConditionalExpression")}},{key:"tryParseConditionalConsequent",value:function(){this.state.noArrowParamsConversionAt.push(this.state.start);var t=this.parseMaybeAssignAllowIn(),e=!this.match(14);return this.state.noArrowParamsConversionAt.pop(),{consequent:t,failed:e}}},{key:"getArrowLikeExpressions",value:function(t,e){var i=this,r=[t],s=[];while(0!==r.length){var n=r.pop();"ArrowFunctionExpression"===n.type?(n.typeParameters||!n.returnType?this.finishArrowValidation(n):s.push(n),r.push(n.body)):"ConditionalExpression"===n.type&&(r.push(n.consequent),r.push(n.alternate))}return e?(s.forEach((function(t){return i.finishArrowValidation(t)})),[s,[]]):gr(s,(function(t){return t.params.every((function(t){return i.isAssignable(t,!0)}))}))}},{key:"finishArrowValidation",value:function(t){var e;this.toAssignableList(t.params,null==(e=t.extra)?void 0:e.trailingCommaLoc,!1),this.scope.enter(me|ye),f(k(i.prototype),"checkParams",this).call(this,t,!1,!0),this.scope.exit()}},{key:"forwardNoArrowParamsConversionAt",value:function(t,e){var i;return-1!==this.state.noArrowParamsConversionAt.indexOf(t.start)?(this.state.noArrowParamsConversionAt.push(this.state.start),i=e(),this.state.noArrowParamsConversionAt.pop()):i=e(),i}},{key:"parseParenItem",value:function(t,e){if(t=f(k(i.prototype),"parseParenItem",this).call(this,t,e),this.eat(17)&&(t.optional=!0,this.resetEndLocation(t)),this.match(14)){var r=this.startNodeAt(e);return r.expression=t,r.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(r,"TypeCastExpression")}return t}},{key:"assertModuleNodeAllowed",value:function(t){"ImportDeclaration"===t.type&&("type"===t.importKind||"typeof"===t.importKind)||"ExportNamedDeclaration"===t.type&&"type"===t.exportKind||"ExportAllDeclaration"===t.type&&"type"===t.exportKind||f(k(i.prototype),"assertModuleNodeAllowed",this).call(this,t)}},{key:"parseExport",value:function(t,e){var r=f(k(i.prototype),"parseExport",this).call(this,t,e);return"ExportNamedDeclaration"!==r.type&&"ExportAllDeclaration"!==r.type||(r.exportKind=r.exportKind||"value"),r}},{key:"parseExportDeclaration",value:function(t){if(this.isContextual(128)){t.exportKind="type";var e=this.startNode();return this.next(),this.match(5)?(t.specifiers=this.parseExportSpecifiers(!0),f(k(i.prototype),"parseExportFrom",this).call(this,t),null):this.flowParseTypeAlias(e)}if(this.isContextual(129)){t.exportKind="type";var r=this.startNode();return this.next(),this.flowParseOpaqueType(r,!1)}if(this.isContextual(127)){t.exportKind="type";var s=this.startNode();return this.next(),this.flowParseInterface(s)}if(this.shouldParseEnums()&&this.isContextual(124)){t.exportKind="value";var n=this.startNode();return this.next(),this.flowParseEnumDeclaration(n)}return f(k(i.prototype),"parseExportDeclaration",this).call(this,t)}},{key:"eatExportStar",value:function(t){return!!f(k(i.prototype),"eatExportStar",this).call(this,t)||!(!this.isContextual(128)||55!==this.lookahead().type)&&(t.exportKind="type",this.next(),this.next(),!0)}},{key:"maybeParseExportNamespaceSpecifier",value:function(t){var e=this.state.startLoc,r=f(k(i.prototype),"maybeParseExportNamespaceSpecifier",this).call(this,t);return r&&"type"===t.exportKind&&this.unexpected(e),r}},{key:"parseClassId",value:function(t,e,r){f(k(i.prototype),"parseClassId",this).call(this,t,e,r),this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration())}},{key:"parseClassMember",value:function(t,e,r){var s=this.state.startLoc;if(this.isContextual(123)){if(f(k(i.prototype),"parseClassMemberFromModifier",this).call(this,t,e))return;e.declare=!0}f(k(i.prototype),"parseClassMember",this).call(this,t,e,r),e.declare&&("ClassProperty"!==e.type&&"ClassPrivateProperty"!==e.type&&"PropertyDefinition"!==e.type?this.raise(dr.DeclareClassElement,{at:s}):e.value&&this.raise(dr.DeclareClassFieldInitializer,{at:e.value}))}},{key:"isIterator",value:function(t){return"iterator"===t||"asyncIterator"===t}},{key:"readIterator",value:function(){var t=f(k(i.prototype),"readWord1",this).call(this),e="@@"+t;this.isIterator(t)&&this.state.inType||this.raise(Z.InvalidIdentifier,{at:this.state.curPosition(),identifierName:e}),this.finishToken(130,e)}},{key:"getTokenFromCode",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);return 123===t&&124===e?this.finishOp(6,2):!this.state.inType||62!==t&&60!==t?this.state.inType&&63===t?46===e?this.finishOp(18,2):this.finishOp(17,1):he(t,e,this.input.charCodeAt(this.state.pos+2))?(this.state.pos+=2,this.readIterator()):f(k(i.prototype),"getTokenFromCode",this).call(this,t):this.finishOp(62===t?48:47,1)}},{key:"isAssignable",value:function(t,e){return"TypeCastExpression"===t.type?this.isAssignable(t.expression,e):f(k(i.prototype),"isAssignable",this).call(this,t,e)}},{key:"toAssignable",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e||"AssignmentExpression"!==t.type||"TypeCastExpression"!==t.left.type||(t.left=this.typeCastToParameter(t.left)),f(k(i.prototype),"toAssignable",this).call(this,t,e)}},{key:"toAssignableList",value:function(t,e,r){for(var s=0;s<t.length;s++){var n=t[s];"TypeCastExpression"===(null==n?void 0:n.type)&&(t[s]=this.typeCastToParameter(n))}f(k(i.prototype),"toAssignableList",this).call(this,t,e,r)}},{key:"toReferencedList",value:function(t,e){for(var i=0;i<t.length;i++){var r,s=t[i];!s||"TypeCastExpression"!==s.type||null!=(r=s.extra)&&r.parenthesized||!(t.length>1)&&e||this.raise(dr.TypeCastInPattern,{at:s.typeAnnotation})}return t}},{key:"parseArrayLike",value:function(t,e,r,s){var n=f(k(i.prototype),"parseArrayLike",this).call(this,t,e,r,s);return e&&!this.state.maybeInArrowParameters&&this.toReferencedList(n.elements),n}},{key:"isValidLVal",value:function(t,e,r){return"TypeCastExpression"===t||f(k(i.prototype),"isValidLVal",this).call(this,t,e,r)}},{key:"parseClassProperty",value:function(t){return this.match(14)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),f(k(i.prototype),"parseClassProperty",this).call(this,t)}},{key:"parseClassPrivateProperty",value:function(t){return this.match(14)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),f(k(i.prototype),"parseClassPrivateProperty",this).call(this,t)}},{key:"isClassMethod",value:function(){return this.match(47)||f(k(i.prototype),"isClassMethod",this).call(this)}},{key:"isClassProperty",value:function(){return this.match(14)||f(k(i.prototype),"isClassProperty",this).call(this)}},{key:"isNonstaticConstructor",value:function(t){return!this.match(14)&&f(k(i.prototype),"isNonstaticConstructor",this).call(this,t)}},{key:"pushClassMethod",value:function(t,e,r,s,n,a){if(e.variance&&this.unexpected(e.variance.loc.start),delete e.variance,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),f(k(i.prototype),"pushClassMethod",this).call(this,t,e,r,s,n,a),e.params&&n){var o=e.params;o.length>0&&this.isThisParam(o[0])&&this.raise(dr.ThisParamBannedInConstructor,{at:e})}else if("MethodDefinition"===e.type&&n&&e.value.params){var l=e.value.params;l.length>0&&this.isThisParam(l[0])&&this.raise(dr.ThisParamBannedInConstructor,{at:e})}}},{key:"pushClassPrivateMethod",value:function(t,e,r,s){e.variance&&this.unexpected(e.variance.loc.start),delete e.variance,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),f(k(i.prototype),"pushClassPrivateMethod",this).call(this,t,e,r,s)}},{key:"parseClassSuper",value:function(t){if(f(k(i.prototype),"parseClassSuper",this).call(this,t),t.superClass&&this.match(47)&&(t.superTypeParameters=this.flowParseTypeParameterInstantiation()),this.isContextual(111)){this.next();var e=t.implements=[];do{var r=this.startNode();r.id=this.flowParseRestrictedIdentifier(!0),this.match(47)?r.typeParameters=this.flowParseTypeParameterInstantiation():r.typeParameters=null,e.push(this.finishNode(r,"ClassImplements"))}while(this.eat(12))}}},{key:"checkGetterSetterParams",value:function(t){f(k(i.prototype),"checkGetterSetterParams",this).call(this,t);var e=this.getObjectOrClassMethodParams(t);if(e.length>0){var r=e[0];this.isThisParam(r)&&"get"===t.kind?this.raise(dr.GetterMayNotHaveThisParam,{at:r}):this.isThisParam(r)&&this.raise(dr.SetterMayNotHaveThisParam,{at:r})}}},{key:"parsePropertyNamePrefixOperator",value:function(t){t.variance=this.flowParseVariance()}},{key:"parseObjPropValue",value:function(t,e,r,s,n,a,o){var l;t.variance&&this.unexpected(t.variance.loc.start),delete t.variance,this.match(47)&&!a&&(l=this.flowParseTypeParameterDeclaration(),this.match(10)||this.unexpected());var c=f(k(i.prototype),"parseObjPropValue",this).call(this,t,e,r,s,n,a,o);return l&&((c.value||c).typeParameters=l),c}},{key:"parseAssignableListItemTypes",value:function(t){return this.eat(17)&&("Identifier"!==t.type&&this.raise(dr.PatternIsOptional,{at:t}),this.isThisParam(t)&&this.raise(dr.ThisParamMayNotBeOptional,{at:t}),t.optional=!0),this.match(14)?t.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(t)&&this.raise(dr.ThisParamAnnotationRequired,{at:t}),this.match(29)&&this.isThisParam(t)&&this.raise(dr.ThisParamNoDefault,{at:t}),this.resetEndLocation(t),t}},{key:"parseMaybeDefault",value:function(t,e){var r=f(k(i.prototype),"parseMaybeDefault",this).call(this,t,e);return"AssignmentPattern"===r.type&&r.typeAnnotation&&r.right.start<r.typeAnnotation.start&&this.raise(dr.TypeBeforeInitializer,{at:r.typeAnnotation}),r}},{key:"shouldParseDefaultImport",value:function(t){return mr(t)?yr(this.state.type):f(k(i.prototype),"shouldParseDefaultImport",this).call(this,t)}},{key:"checkImportReflection",value:function(t){f(k(i.prototype),"checkImportReflection",this).call(this,t),t.module&&"value"!==t.importKind&&this.raise(dr.ImportReflectionHasImportType,{at:t.specifiers[0].loc.start})}},{key:"parseImportSpecifierLocal",value:function(t,e,i){e.local=mr(t)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),t.specifiers.push(this.finishImportSpecifier(e,i))}},{key:"maybeParseDefaultImportSpecifier",value:function(t){t.importKind="value";var e=null;if(this.match(87)?e="typeof":this.isContextual(128)&&(e="type"),e){var r=this.lookahead(),s=r.type;"type"===e&&55===s&&this.unexpected(null,r.type),(yr(s)||5===s||55===s)&&(this.next(),t.importKind=e)}return f(k(i.prototype),"maybeParseDefaultImportSpecifier",this).call(this,t)}},{key:"parseImportSpecifier",value:function(t,e,i,r,s){var n=t.imported,a=null;"Identifier"===n.type&&("type"===n.name?a="type":"typeof"===n.name&&(a="typeof"));var o=!1;if(this.isContextual(93)&&!this.isLookaheadContextual("as")){var l=this.parseIdentifier(!0);null===a||Ct(this.state.type)?(t.imported=n,t.importKind=null,t.local=this.parseIdentifier()):(t.imported=l,t.importKind=a,t.local=cr(l))}else{if(null!==a&&Ct(this.state.type))t.imported=this.parseIdentifier(!0),t.importKind=a;else{if(e)throw this.raise(Z.ImportBindingIsString,{at:t,importName:n.value});t.imported=n,t.importKind=null}this.eatContextual(93)?t.local=this.parseIdentifier():(o=!0,t.local=cr(t.imported))}var c=mr(t);return i&&c&&this.raise(dr.ImportTypeShorthandOnlyInPureImport,{at:t}),(i||c)&&this.checkReservedType(t.local.name,t.local.loc.start,!0),!o||i||c||this.checkReservedWord(t.local.name,t.loc.start,!0,!0),this.finishImportSpecifier(t,"ImportSpecifier")}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return f(k(i.prototype),"parseBindingAtom",this).call(this)}}},{key:"parseFunctionParams",value:function(t,e){var r=t.kind;"get"!==r&&"set"!==r&&this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),f(k(i.prototype),"parseFunctionParams",this).call(this,t,e)}},{key:"parseVarId",value:function(t,e){f(k(i.prototype),"parseVarId",this).call(this,t,e),this.match(14)&&(t.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t.id))}},{key:"parseAsyncArrowFromCallExpression",value:function(t,e){if(this.match(14)){var r=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,t.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=r}return f(k(i.prototype),"parseAsyncArrowFromCallExpression",this).call(this,t,e)}},{key:"shouldParseAsyncArrow",value:function(){return this.match(14)||f(k(i.prototype),"shouldParseAsyncArrow",this).call(this)}},{key:"parseMaybeAssign",value:function(t,e){var r,s,n=this,a=null;if(this.hasPlugin("jsx")&&(this.match(140)||this.match(47))){if(a=this.state.clone(),s=this.tryParse((function(){return f(k(i.prototype),"parseMaybeAssign",n).call(n,t,e)}),a),!s.error)return s.node;var o=this.state.context,l=o[o.length-1];l!==nt.j_oTag&&l!==nt.j_expr||o.pop()}if(null!=(r=s)&&r.error||this.match(47)){var c,h,u;a=a||this.state.clone();var p=this.tryParse((function(r){var s;u=n.flowParseTypeParameterDeclaration();var a=n.forwardNoArrowParamsConversionAt(u,(function(){var r=f(k(i.prototype),"parseMaybeAssign",n).call(n,t,e);return n.resetStartLocationFromNode(r,u),r}));null!=(s=a.extra)&&s.parenthesized&&r();var o=n.maybeUnwrapTypeCastExpression(a);return"ArrowFunctionExpression"!==o.type&&r(),o.typeParameters=u,n.resetStartLocationFromNode(o,u),a}),a),d=null;if(p.node&&"ArrowFunctionExpression"===this.maybeUnwrapTypeCastExpression(p.node).type){if(!p.error&&!p.aborted)return p.node.async&&this.raise(dr.UnexpectedTypeParameterBeforeAsyncArrowFunction,{at:u}),p.node;d=p.node}if(null!=(c=s)&&c.node)return this.state=s.failState,s.node;if(d)return this.state=p.failState,d;if(null!=(h=s)&&h.thrown)throw s.error;if(p.thrown)throw p.error;throw this.raise(dr.UnexpectedTokenAfterTypeParameter,{at:u})}return f(k(i.prototype),"parseMaybeAssign",this).call(this,t,e)}},{key:"parseArrow",value:function(t){var e=this;if(this.match(14)){var r=this.tryParse((function(){var i=e.state.noAnonFunctionType;e.state.noAnonFunctionType=!0;var r=e.startNode(),s=e.flowParseTypeAndPredicateInitialiser(),n=w(s,2);return r.typeAnnotation=n[0],t.predicate=n[1],e.state.noAnonFunctionType=i,e.canInsertSemicolon()&&e.unexpected(),e.match(19)||e.unexpected(),r}));if(r.thrown)return null;r.error&&(this.state=r.failState),t.returnType=r.node.typeAnnotation?this.finishNode(r.node,"TypeAnnotation"):null}return f(k(i.prototype),"parseArrow",this).call(this,t)}},{key:"shouldParseArrow",value:function(t){return this.match(14)||f(k(i.prototype),"shouldParseArrow",this).call(this,t)}},{key:"setArrowFunctionParameters",value:function(t,e){-1!==this.state.noArrowParamsConversionAt.indexOf(t.start)?t.params=e:f(k(i.prototype),"setArrowFunctionParameters",this).call(this,t,e)}},{key:"checkParams",value:function(t,e,r){var s=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!r||-1===this.state.noArrowParamsConversionAt.indexOf(t.start)){for(var n=0;n<t.params.length;n++)this.isThisParam(t.params[n])&&n>0&&this.raise(dr.ThisParamMustBeFirst,{at:t.params[n]});return f(k(i.prototype),"checkParams",this).call(this,t,e,r,s)}}},{key:"parseParenAndDistinguishExpression",value:function(t){return f(k(i.prototype),"parseParenAndDistinguishExpression",this).call(this,t&&-1===this.state.noArrowAt.indexOf(this.state.start))}},{key:"parseSubscripts",value:function(t,e,r){var s=this;if("Identifier"===t.type&&"async"===t.name&&-1!==this.state.noArrowAt.indexOf(e.index)){this.next();var n=this.startNodeAt(e);n.callee=t,n.arguments=f(k(i.prototype),"parseCallExpressionArguments",this).call(this,11,!1),t=this.finishNode(n,"CallExpression")}else if("Identifier"===t.type&&"async"===t.name&&this.match(47)){var a=this.state.clone(),o=this.tryParse((function(t){return s.parseAsyncArrowWithTypeParameters(e)||t()}),a);if(!o.error&&!o.aborted)return o.node;var l=this.tryParse((function(){return f(k(i.prototype),"parseSubscripts",s).call(s,t,e,r)}),a);if(l.node&&!l.error)return l.node;if(o.node)return this.state=o.failState,o.node;if(l.node)return this.state=l.failState,l.node;throw o.error||l.error}return f(k(i.prototype),"parseSubscripts",this).call(this,t,e,r)}},{key:"parseSubscript",value:function(t,e,r,s){var n=this;if(this.match(18)&&this.isLookaheadToken_lt()){if(s.optionalChainMember=!0,r)return s.stop=!0,t;this.next();var a=this.startNodeAt(e);return a.callee=t,a.typeArguments=this.flowParseTypeParameterInstantiation(),this.expect(10),a.arguments=this.parseCallExpressionArguments(11,!1),a.optional=!0,this.finishCallExpression(a,!0)}if(!r&&this.shouldParseTypes()&&this.match(47)){var o=this.startNodeAt(e);o.callee=t;var l=this.tryParse((function(){return o.typeArguments=n.flowParseTypeParameterInstantiationCallOrNew(),n.expect(10),o.arguments=f(k(i.prototype),"parseCallExpressionArguments",n).call(n,11,!1),s.optionalChainMember&&(o.optional=!1),n.finishCallExpression(o,s.optionalChainMember)}));if(l.node)return l.error&&(this.state=l.failState),l.node}return f(k(i.prototype),"parseSubscript",this).call(this,t,e,r,s)}},{key:"parseNewCallee",value:function(t){var e=this;f(k(i.prototype),"parseNewCallee",this).call(this,t);var r=null;this.shouldParseTypes()&&this.match(47)&&(r=this.tryParse((function(){return e.flowParseTypeParameterInstantiationCallOrNew()})).node),t.typeArguments=r}},{key:"parseAsyncArrowWithTypeParameters",value:function(t){var e=this.startNodeAt(t);if(this.parseFunctionParams(e),this.parseArrow(e))return f(k(i.prototype),"parseArrowExpression",this).call(this,e,void 0,!0)}},{key:"readToken_mult_modulo",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);if(42===t&&47===e&&this.state.hasFlowComment)return this.state.hasFlowComment=!1,this.state.pos+=2,void this.nextToken();f(k(i.prototype),"readToken_mult_modulo",this).call(this,t)}},{key:"readToken_pipe_amp",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);124!==t||125!==e?f(k(i.prototype),"readToken_pipe_amp",this).call(this,t):this.finishOp(9,2)}},{key:"parseTopLevel",value:function(t,e){var r=f(k(i.prototype),"parseTopLevel",this).call(this,t,e);return this.state.hasFlowComment&&this.raise(dr.UnterminatedFlowComment,{at:this.state.curPosition()}),r}},{key:"skipBlockComment",value:function(){if(!this.hasPlugin("flowComments")||!this.skipFlowComment())return f(k(i.prototype),"skipBlockComment",this).call(this,this.state.hasFlowComment?"*-/":"*/");if(this.state.hasFlowComment)throw this.raise(dr.NestedFlowComment,{at:this.state.startLoc});this.hasFlowCommentCompletion();var t=this.skipFlowComment();t&&(this.state.pos+=t,this.state.hasFlowComment=!0)}},{key:"skipFlowComment",value:function(){var t=this.state.pos,e=2;while([32,9].includes(this.input.charCodeAt(t+e)))e++;var i=this.input.charCodeAt(e+t),r=this.input.charCodeAt(e+t+1);return 58===i&&58===r?e+2:"flow-include"===this.input.slice(e+t,e+t+12)?e+12:58===i&&58!==r&&e}},{key:"hasFlowCommentCompletion",value:function(){var t=this.input.indexOf("*/",this.state.pos);if(-1===t)throw this.raise(Z.UnterminatedComment,{at:this.state.curPosition()})}},{key:"flowEnumErrorBooleanMemberNotInitialized",value:function(t,e){var i=e.enumName,r=e.memberName;this.raise(dr.EnumBooleanMemberNotInitialized,{at:t,memberName:r,enumName:i})}},{key:"flowEnumErrorInvalidMemberInitializer",value:function(t,e){return this.raise(e.explicitType?"symbol"===e.explicitType?dr.EnumInvalidMemberInitializerSymbolType:dr.EnumInvalidMemberInitializerPrimaryType:dr.EnumInvalidMemberInitializerUnknownType,Object.assign({at:t},e))}},{key:"flowEnumErrorNumberMemberNotInitialized",value:function(t,e){var i=e.enumName,r=e.memberName;this.raise(dr.EnumNumberMemberNotInitialized,{at:t,enumName:i,memberName:r})}},{key:"flowEnumErrorStringMemberInconsistentlyInitailized",value:function(t,e){var i=e.enumName;this.raise(dr.EnumStringMemberInconsistentlyInitailized,{at:t,enumName:i})}},{key:"flowEnumMemberInit",value:function(){var t=this,e=this.state.startLoc,i=function(){return t.match(12)||t.match(8)};switch(this.state.type){case 132:var r=this.parseNumericLiteral(this.state.value);return i()?{type:"number",loc:r.loc.start,value:r}:{type:"invalid",loc:e};case 131:var s=this.parseStringLiteral(this.state.value);return i()?{type:"string",loc:s.loc.start,value:s}:{type:"invalid",loc:e};case 85:case 86:var n=this.parseBooleanLiteral(this.match(85));return i()?{type:"boolean",loc:n.loc.start,value:n}:{type:"invalid",loc:e};default:return{type:"invalid",loc:e}}}},{key:"flowEnumMemberRaw",value:function(){var t=this.state.startLoc,e=this.parseIdentifier(!0),i=this.eat(29)?this.flowEnumMemberInit():{type:"none",loc:t};return{id:e,init:i}}},{key:"flowEnumCheckExplicitTypeMismatch",value:function(t,e,i){var r=e.explicitType;null!==r&&r!==i&&this.flowEnumErrorInvalidMemberInitializer(t,e)}},{key:"flowEnumMembers",value:function(t){var e=t.enumName,i=t.explicitType,r=new Set,s={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]},n=!1;while(!this.match(8)){if(this.eat(21)){n=!0;break}var a=this.startNode(),o=this.flowEnumMemberRaw(),l=o.id,c=o.init,h=l.name;if(""!==h){/^[a-z]/.test(h)&&this.raise(dr.EnumInvalidMemberName,{at:l,memberName:h,suggestion:h[0].toUpperCase()+h.slice(1),enumName:e}),r.has(h)&&this.raise(dr.EnumDuplicateMemberName,{at:l,memberName:h,enumName:e}),r.add(h);var u={enumName:e,explicitType:i,memberName:h};switch(a.id=l,c.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(c.loc,u,"boolean"),a.init=c.value,s.booleanMembers.push(this.finishNode(a,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(c.loc,u,"number"),a.init=c.value,s.numberMembers.push(this.finishNode(a,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(c.loc,u,"string"),a.init=c.value,s.stringMembers.push(this.finishNode(a,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(c.loc,u);case"none":switch(i){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(c.loc,u);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(c.loc,u);break;default:s.defaultedMembers.push(this.finishNode(a,"EnumDefaultedMember"))}}this.match(8)||this.expect(12)}}return{members:s,hasUnknownMembers:n}}},{key:"flowEnumStringMembers",value:function(t,e,i){var r=i.enumName;if(0===t.length)return e;if(0===e.length)return t;if(e.length>t.length){var s,n=c(t);try{for(n.s();!(s=n.n()).done;){var a=s.value;this.flowEnumErrorStringMemberInconsistentlyInitailized(a,{enumName:r})}}catch(u){n.e(u)}finally{n.f()}return e}var o,l=c(e);try{for(l.s();!(o=l.n()).done;){var h=o.value;this.flowEnumErrorStringMemberInconsistentlyInitailized(h,{enumName:r})}}catch(u){l.e(u)}finally{l.f()}return t}},{key:"flowEnumParseExplicitType",value:function(t){var e=t.enumName;if(!this.eatContextual(101))return null;if(!At(this.state.type))throw this.raise(dr.EnumInvalidExplicitTypeUnknownSupplied,{at:this.state.startLoc,enumName:e});var i=this.state.value;return this.next(),"boolean"!==i&&"number"!==i&&"string"!==i&&"symbol"!==i&&this.raise(dr.EnumInvalidExplicitType,{at:this.state.startLoc,enumName:e,invalidEnumType:i}),i}},{key:"flowEnumBody",value:function(t,e){var i=this,r=e.name,s=e.loc.start,n=this.flowEnumParseExplicitType({enumName:r});this.expect(5);var a=this.flowEnumMembers({enumName:r,explicitType:n}),o=a.members,l=a.hasUnknownMembers;switch(t.hasUnknownMembers=l,n){case"boolean":return t.explicitType=!0,t.members=o.booleanMembers,this.expect(8),this.finishNode(t,"EnumBooleanBody");case"number":return t.explicitType=!0,t.members=o.numberMembers,this.expect(8),this.finishNode(t,"EnumNumberBody");case"string":return t.explicitType=!0,t.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:r}),this.expect(8),this.finishNode(t,"EnumStringBody");case"symbol":return t.members=o.defaultedMembers,this.expect(8),this.finishNode(t,"EnumSymbolBody");default:var h=function(){return t.members=[],i.expect(8),i.finishNode(t,"EnumStringBody")};t.explicitType=!1;var u=o.booleanMembers.length,p=o.numberMembers.length,d=o.stringMembers.length,f=o.defaultedMembers.length;if(u||p||d||f){if(u||p){if(!p&&!d&&u>=f){var m,y=c(o.defaultedMembers);try{for(y.s();!(m=y.n()).done;){var v=m.value;this.flowEnumErrorBooleanMemberNotInitialized(v.loc.start,{enumName:r,memberName:v.id.name})}}catch(P){y.e(P)}finally{y.f()}return t.members=o.booleanMembers,this.expect(8),this.finishNode(t,"EnumBooleanBody")}if(!u&&!d&&p>=f){var g,x=c(o.defaultedMembers);try{for(x.s();!(g=x.n()).done;){var b=g.value;this.flowEnumErrorNumberMemberNotInitialized(b.loc.start,{enumName:r,memberName:b.id.name})}}catch(P){x.e(P)}finally{x.f()}return t.members=o.numberMembers,this.expect(8),this.finishNode(t,"EnumNumberBody")}return this.raise(dr.EnumInconsistentMemberValues,{at:s,enumName:r}),h()}return t.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:r}),this.expect(8),this.finishNode(t,"EnumStringBody")}return h()}}},{key:"flowParseEnumDeclaration",value:function(t){var e=this.parseIdentifier();return t.id=e,t.body=this.flowEnumBody(this.startNode(),e),this.finishNode(t,"EnumDeclaration")}},{key:"isLookaheadToken_lt",value:function(){var t=this.nextTokenStart();if(60===this.input.charCodeAt(t)){var e=this.input.charCodeAt(t+1);return 60!==e&&61!==e}return!1}},{key:"maybeUnwrapTypeCastExpression",value:function(t){return"TypeCastExpression"===t.type?t.expression:t}}]),i}(t)},Pr={__proto__:null,quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},kr=Q(a||(a=T(["jsx"])))({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:function(t){var e=t.openingTagName;return"Expected corresponding JSX closing tag for <".concat(e,">.")},MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnexpectedToken:function(t){var e=t.unexpected,i=t.HTMLEntity;return"Unexpected token `".concat(e,"`. Did you mean `").concat(i,"` or `{'").concat(e,"'}`?")},UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function Tr(t){return!!t&&("JSXOpeningFragment"===t.type||"JSXClosingFragment"===t.type)}function wr(t){if("JSXIdentifier"===t.type)return t.name;if("JSXNamespacedName"===t.type)return t.namespace.name+":"+t.name.name;if("JSXMemberExpression"===t.type)return wr(t.object)+"."+wr(t.property);throw new Error("Node had unexpected type: "+t.type)}var Er=function(t){return function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"jsxReadToken",value:function(){for(var t="",e=this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(kr.UnterminatedJsxContent,{at:this.state.startLoc});var r=this.input.charCodeAt(this.state.pos);switch(r){case 60:case 123:return this.state.pos===this.state.start?60===r&&this.state.canStartJSXElement?(++this.state.pos,this.finishToken(140)):f(k(i.prototype),"getTokenFromCode",this).call(this,r):(t+=this.input.slice(e,this.state.pos),this.finishToken(139,t));case 38:t+=this.input.slice(e,this.state.pos),t+=this.jsxReadEntity(),e=this.state.pos;break;case 62:case 125:default:vi(r)?(t+=this.input.slice(e,this.state.pos),t+=this.jsxReadNewLine(!0),e=this.state.pos):++this.state.pos}}}},{key:"jsxReadNewLine",value:function(t){var e,i=this.input.charCodeAt(this.state.pos);return++this.state.pos,13===i&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,e=t?"\n":"\r\n"):e=String.fromCharCode(i),++this.state.curLine,this.state.lineStart=this.state.pos,e}},{key:"jsxReadString",value:function(t){for(var e="",i=++this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(Z.UnterminatedString,{at:this.state.startLoc});var r=this.input.charCodeAt(this.state.pos);if(r===t)break;38===r?(e+=this.input.slice(i,this.state.pos),e+=this.jsxReadEntity(),i=this.state.pos):vi(r)?(e+=this.input.slice(i,this.state.pos),e+=this.jsxReadNewLine(!1),i=this.state.pos):++this.state.pos}return e+=this.input.slice(i,this.state.pos++),this.finishToken(131,e)}},{key:"jsxReadEntity",value:function(){var t=++this.state.pos;if(35===this.codePointAtPos(this.state.pos)){++this.state.pos;var e=10;120===this.codePointAtPos(this.state.pos)&&(e=16,++this.state.pos);var i=this.readInt(e,void 0,!1,"bail");if(null!==i&&59===this.codePointAtPos(this.state.pos))return++this.state.pos,String.fromCodePoint(i)}else{var r=0,s=!1;while(r++<10&&this.state.pos<this.length&&!(s=59==this.codePointAtPos(this.state.pos)))++this.state.pos;if(s){var n=this.input.slice(t,this.state.pos),a=Pr[n];if(++this.state.pos,a)return a}}return this.state.pos=t,"&"}},{key:"jsxReadWord",value:function(){var t,e=this.state.pos;do{t=this.input.charCodeAt(++this.state.pos)}while(te(t)||45===t);return this.finishToken(138,this.input.slice(e,this.state.pos))}},{key:"jsxParseIdentifier",value:function(){var t=this.startNode();return this.match(138)?t.name=this.state.value:Ft(this.state.type)?t.name=zt(this.state.type):this.unexpected(),this.next(),this.finishNode(t,"JSXIdentifier")}},{key:"jsxParseNamespacedName",value:function(){var t=this.state.startLoc,e=this.jsxParseIdentifier();if(!this.eat(14))return e;var i=this.startNodeAt(t);return i.namespace=e,i.name=this.jsxParseIdentifier(),this.finishNode(i,"JSXNamespacedName")}},{key:"jsxParseElementName",value:function(){var t=this.state.startLoc,e=this.jsxParseNamespacedName();if("JSXNamespacedName"===e.type)return e;while(this.eat(16)){var i=this.startNodeAt(t);i.object=e,i.property=this.jsxParseIdentifier(),e=this.finishNode(i,"JSXMemberExpression")}return e}},{key:"jsxParseAttributeValue",value:function(){var t;switch(this.state.type){case 5:return t=this.startNode(),this.setContext(nt.brace),this.next(),t=this.jsxParseExpressionContainer(t,nt.j_oTag),"JSXEmptyExpression"===t.expression.type&&this.raise(kr.AttributeIsEmpty,{at:t}),t;case 140:case 131:return this.parseExprAtom();default:throw this.raise(kr.UnsupportedJsxValue,{at:this.state.startLoc})}}},{key:"jsxParseEmptyExpression",value:function(){var t=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(t,"JSXEmptyExpression",this.state.startLoc)}},{key:"jsxParseSpreadChild",value:function(t){return this.next(),t.expression=this.parseExpression(),this.setContext(nt.j_expr),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(t,"JSXSpreadChild")}},{key:"jsxParseExpressionContainer",value:function(t,e){if(this.match(8))t.expression=this.jsxParseEmptyExpression();else{var i=this.parseExpression();t.expression=i}return this.setContext(e),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(t,"JSXExpressionContainer")}},{key:"jsxParseAttribute",value:function(){var t=this.startNode();return this.match(5)?(this.setContext(nt.brace),this.next(),this.expect(21),t.argument=this.parseMaybeAssignAllowIn(),this.setContext(nt.j_oTag),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(t,"JSXSpreadAttribute")):(t.name=this.jsxParseNamespacedName(),t.value=this.eat(29)?this.jsxParseAttributeValue():null,this.finishNode(t,"JSXAttribute"))}},{key:"jsxParseOpeningElementAt",value:function(t){var e=this.startNodeAt(t);return this.eat(141)?this.finishNode(e,"JSXOpeningFragment"):(e.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(e))}},{key:"jsxParseOpeningElementAfterName",value:function(t){var e=[];while(!this.match(56)&&!this.match(141))e.push(this.jsxParseAttribute());return t.attributes=e,t.selfClosing=this.eat(56),this.expect(141),this.finishNode(t,"JSXOpeningElement")}},{key:"jsxParseClosingElementAt",value:function(t){var e=this.startNodeAt(t);return this.eat(141)?this.finishNode(e,"JSXClosingFragment"):(e.name=this.jsxParseElementName(),this.expect(141),this.finishNode(e,"JSXClosingElement"))}},{key:"jsxParseElementAt",value:function(t){var e=this.startNodeAt(t),i=[],r=this.jsxParseOpeningElementAt(t),s=null;if(!r.selfClosing){t:for(;;)switch(this.state.type){case 140:if(t=this.state.startLoc,this.next(),this.eat(56)){s=this.jsxParseClosingElementAt(t);break t}i.push(this.jsxParseElementAt(t));break;case 139:i.push(this.parseExprAtom());break;case 5:var n=this.startNode();this.setContext(nt.brace),this.next(),this.match(21)?i.push(this.jsxParseSpreadChild(n)):i.push(this.jsxParseExpressionContainer(n,nt.j_expr));break;default:throw this.unexpected()}Tr(r)&&!Tr(s)&&null!==s?this.raise(kr.MissingClosingTagFragment,{at:s}):!Tr(r)&&Tr(s)?this.raise(kr.MissingClosingTagElement,{at:s,openingTagName:wr(r.name)}):Tr(r)||Tr(s)||wr(s.name)!==wr(r.name)&&this.raise(kr.MissingClosingTagElement,{at:s,openingTagName:wr(r.name)})}if(Tr(r)?(e.openingFragment=r,e.closingFragment=s):(e.openingElement=r,e.closingElement=s),e.children=i,this.match(47))throw this.raise(kr.UnwrappedAdjacentJSXElements,{at:this.state.startLoc});return Tr(r)?this.finishNode(e,"JSXFragment"):this.finishNode(e,"JSXElement")}},{key:"jsxParseElement",value:function(){var t=this.state.startLoc;return this.next(),this.jsxParseElementAt(t)}},{key:"setContext",value:function(t){var e=this.state.context;e[e.length-1]=t}},{key:"parseExprAtom",value:function(t){return this.match(139)?this.parseLiteral(this.state.value,"JSXText"):this.match(140)?this.jsxParseElement():this.match(47)&&33!==this.input.charCodeAt(this.state.pos)?(this.replaceToken(140),this.jsxParseElement()):f(k(i.prototype),"parseExprAtom",this).call(this,t)}},{key:"skipSpace",value:function(){var t=this.curContext();t.preserveSpace||f(k(i.prototype),"skipSpace",this).call(this)}},{key:"getTokenFromCode",value:function(t){var e=this.curContext();if(e===nt.j_expr)return this.jsxReadToken();if(e===nt.j_oTag||e===nt.j_cTag){if(Zt(t))return this.jsxReadWord();if(62===t)return++this.state.pos,this.finishToken(141);if((34===t||39===t)&&e===nt.j_oTag)return this.jsxReadString(t)}return 60===t&&this.state.canStartJSXElement&&33!==this.input.charCodeAt(this.state.pos+1)?(++this.state.pos,this.finishToken(140)):f(k(i.prototype),"getTokenFromCode",this).call(this,t)}},{key:"updateContext",value:function(t){var e=this.state,i=e.context,r=e.type;if(56===r&&140===t)i.splice(-2,2,nt.j_cTag),this.state.canStartJSXElement=!1;else if(140===r)i.push(nt.j_oTag);else if(141===r){var s=i[i.length-1];s===nt.j_oTag&&56===t||s===nt.j_cTag?(i.pop(),this.state.canStartJSXElement=i[i.length-1]===nt.j_expr):(this.setContext(nt.j_expr),this.state.canStartJSXElement=!0)}else this.state.canStartJSXElement=Nt(r)}}]),i}(t)},Ar=function(t){y(i,t);var e=g(i);function i(){var t;L(this,i);for(var r=arguments.length,s=new Array(r),n=0;n<r;n++)s[n]=arguments[n];return t=e.call.apply(e,[this].concat(s)),t.types=new Set,t.enums=new Set,t.constEnums=new Set,t.classes=new Set,t.exportOnlyBindings=new Set,t}return D(i)}(ni),Sr=function(t){y(i,t);var e=g(i);function i(){var t;L(this,i);for(var r=arguments.length,s=new Array(r),n=0;n<r;n++)s[n]=arguments[n];return t=e.call.apply(e,[this].concat(s)),t.importsStack=[],t}return D(i,[{key:"createScope",value:function(t){return this.importsStack.push(new Set),new Ar(t)}},{key:"enter",value:function(t){t==ke&&this.importsStack.push(new Set),f(k(i.prototype),"enter",this).call(this,t)}},{key:"exit",value:function(){var t=f(k(i.prototype),"exit",this).call(this);return t==ke&&this.importsStack.pop(),t}},{key:"hasImport",value:function(t,e){var i=this.importsStack.length;if(this.importsStack[i-1].has(t))return!0;if(!e&&i>1)for(var r=0;r<i-1;r++)if(this.importsStack[r].has(t))return!0;return!1}},{key:"declareName",value:function(t,e,r){if(e&Fe)return this.hasImport(t,!0)&&this.parser.raise(Z.VarRedeclaration,{at:r,identifierName:t}),void this.importsStack[this.importsStack.length-1].add(t);var s=this.currentScope();if(e&Me)return this.maybeExportDefined(s,t),void s.exportOnlyBindings.add(t);f(k(i.prototype),"declareName",this).call(this,t,e,r),e&Ee&&(e&we||(this.checkRedeclarationInScope(s,t,e,r),this.maybeExportDefined(s,t)),s.types.add(t)),e&De&&s.enums.add(t),e&Oe&&s.constEnums.add(t),e&Ne&&s.classes.add(t)}},{key:"isRedeclaredInScope",value:function(t,e,r){if(t.enums.has(e)){if(r&De){var s=!!(r&Oe),n=t.constEnums.has(e);return s!==n}return!0}return r&Ne&&t.classes.has(e)?!!t.lexical.has(e)&&!!(r&we):!!(r&Ee&&t.types.has(e))||f(k(i.prototype),"isRedeclaredInScope",this).call(this,t,e,r)}},{key:"checkLocalExport",value:function(t){var e=t.name;if(!this.hasImport(e)){for(var r=this.scopeStack.length,s=r-1;s>=0;s--){var n=this.scopeStack[s];if(n.types.has(e)||n.exportOnlyBindings.has(e))return}f(k(i.prototype),"checkLocalExport",this).call(this,t)}}}]),i}(ai),Cr=function(t,e){return Object.hasOwnProperty.call(t,e)&&t[e]};function Ir(t){if(null==t)throw new Error("Unexpected ".concat(t," value."));return t}function Nr(t){if(!t)throw new Error("Assert fail")}var Dr=Q(o||(o=T(["typescript"])))({AbstractMethodHasImplementation:function(t){var e=t.methodName;return"Method '".concat(e,"' cannot have an implementation because it is marked abstract.")},AbstractPropertyHasInitializer:function(t){var e=t.propertyName;return"Property '".concat(e,"' cannot have an initializer because it is marked abstract.")},AccesorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccesorCannotHaveTypeParameters:"An accessor cannot have type parameters.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference:"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:function(t){var e=t.kind;return"'declare' is not allowed in ".concat(e,"ters.")},DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:function(t){t.modifier;return"Accessibility modifier already seen."},DuplicateModifier:function(t){var e=t.modifier;return"Duplicate modifier: '".concat(e,"'.")},EmptyHeritageClauseType:function(t){var e=t.token;return"'".concat(e,"' list cannot be empty.")},EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` modifier",IncompatibleModifiers:function(t){var e=t.modifiers;return"'".concat(e[0],"' modifier cannot be used with '").concat(e[1],"' modifier.")},IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:function(t){var e=t.modifier;return"Index signatures cannot have an accessibility modifier ('".concat(e,"').")},IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InitializerNotAllowedInAmbientContext:"Initializers are not allowed in ambient contexts.",InvalidModifierOnTypeMember:function(t){var e=t.modifier;return"'".concat(e,"' modifier cannot appear on a type member.")},InvalidModifierOnTypeParameter:function(t){var e=t.modifier;return"'".concat(e,"' modifier cannot appear on a type parameter.")},InvalidModifierOnTypeParameterPositions:function(t){var e=t.modifier;return"'".concat(e,"' modifier can only appear on a type parameter of a class, interface or type alias.")},InvalidModifiersOrder:function(t){var e=t.orderedModifiers;return"'".concat(e[0],"' modifier must precede '").concat(e[1],"' modifier.")},InvalidPropertyAccessAfterInstantiationExpression:"Invalid property access after an instantiation expression. You can either wrap the instantiation expression in parentheses, or delete the type arguments.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MissingInterfaceName:"'interface' declarations must be followed by an identifier.",MixedLabeledAndUnlabeledElements:"Tuple members must all have names or all not have names.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifer:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:function(t){var e=t.modifier;return"Private elements cannot have an accessibility modifier ('".concat(e,"').")},ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",ReservedArrowTypeParam:"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.",ReservedTypeAssertion:"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.",SetAccesorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccesorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccesorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",SingleTypeParameterWithoutTrailingComma:function(t){var e=t.typeParameterName;return"Single type parameter ".concat(e," should have a trailing comma. Example usage: <").concat(e,",>.")},StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",TypeModifierIsUsedInTypeExports:"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.",TypeModifierIsUsedInTypeImports:"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:function(t){var e=t.type;return"Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ".concat(e,".")}});function Or(t){switch(t){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}function Mr(t){return"private"===t||"public"===t||"protected"===t}function Lr(t){return"in"===t||"out"===t}var Fr=function(t){return function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"getScopeHandler",value:function(){return Sr}},{key:"tsIsIdentifier",value:function(){return At(this.state.type)}},{key:"tsTokenCanFollowModifier",value:function(){return(this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(136)||this.isLiteralPropertyName())&&!this.hasPrecedingLineBreak()}},{key:"tsNextTokenCanFollowModifier",value:function(){return this.next(),this.tsTokenCanFollowModifier()}},{key:"tsParseModifier",value:function(t,e){if(At(this.state.type)||58===this.state.type){var i=this.state.value;if(-1!==t.indexOf(i)){if(e&&this.tsIsStartOfStaticBlocks())return;if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this)))return i}}}},{key:"tsParseModifiers",value:function(t){for(var e=this,i=t.modified,r=t.allowedModifiers,s=t.disallowedModifiers,n=t.stopOnStartOfClassStaticBlock,a=t.errorTemplate,o=void 0===a?Dr.InvalidModifierOnTypeMember:a,l=function(t,r,s,n){r===s&&i[n]&&e.raise(Dr.InvalidModifiersOrder,{at:t,orderedModifiers:[s,n]})},c=function(t,r,s,n){(i[s]&&r===n||i[n]&&r===s)&&e.raise(Dr.IncompatibleModifiers,{at:t,modifiers:[s,n]})};;){var h=this.state.startLoc,u=this.tsParseModifier(r.concat(null!=s?s:[]),n);if(!u)break;Mr(u)?i.accessibility?this.raise(Dr.DuplicateAccessibilityModifier,{at:h,modifier:u}):(l(h,u,u,"override"),l(h,u,u,"static"),l(h,u,u,"readonly"),i.accessibility=u):Lr(u)?(i[u]&&this.raise(Dr.DuplicateModifier,{at:h,modifier:u}),i[u]=!0,l(h,u,"in","out")):(Object.hasOwnProperty.call(i,u)?this.raise(Dr.DuplicateModifier,{at:h,modifier:u}):(l(h,u,"static","readonly"),l(h,u,"static","override"),l(h,u,"override","readonly"),l(h,u,"abstract","override"),c(h,u,"declare","override"),c(h,u,"static","abstract")),i[u]=!0),null!=s&&s.includes(u)&&this.raise(o,{at:h,modifier:u})}}},{key:"tsIsListTerminator",value:function(t){switch(t){case"EnumMembers":case"TypeMembers":return this.match(8);case"HeritageClauseElement":return this.match(5);case"TupleElementTypes":return this.match(3);case"TypeParametersOrArguments":return this.match(48)}throw new Error("Unreachable")}},{key:"tsParseList",value:function(t,e){var i=[];while(!this.tsIsListTerminator(t))i.push(e());return i}},{key:"tsParseDelimitedList",value:function(t,e,i){return Ir(this.tsParseDelimitedListWorker(t,e,!0,i))}},{key:"tsParseDelimitedListWorker",value:function(t,e,i,r){for(var s=[],n=-1;;){if(this.tsIsListTerminator(t))break;n=-1;var a=e();if(null==a)return;if(s.push(a),!this.eat(12)){if(this.tsIsListTerminator(t))break;return void(i&&this.expect(12))}n=this.state.lastTokStart}return r&&(r.value=n),s}},{key:"tsParseBracketedList",value:function(t,e,i,r,s){r||(i?this.expect(0):this.expect(47));var n=this.tsParseDelimitedList(t,e,s);return i?this.expect(3):this.expect(48),n}},{key:"tsParseImportType",value:function(){var t=this.startNode();return this.expect(83),this.expect(10),this.match(131)||this.raise(Dr.UnsupportedImportTypeArgument,{at:this.state.startLoc}),t.argument=f(k(i.prototype),"parseExprAtom",this).call(this),this.expect(11),this.eat(16)&&(t.qualifier=this.tsParseEntityName()),this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSImportType")}},{key:"tsParseEntityName",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=this.parseIdentifier(t);while(this.eat(16)){var i=this.startNodeAtNode(e);i.left=e,i.right=this.parseIdentifier(t),e=this.finishNode(i,"TSQualifiedName")}return e}},{key:"tsParseTypeReference",value:function(){var t=this.startNode();return t.typeName=this.tsParseEntityName(),!this.hasPrecedingLineBreak()&&this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSTypeReference")}},{key:"tsParseThisTypePredicate",value:function(t){this.next();var e=this.startNodeAtNode(t);return e.parameterName=t,e.typeAnnotation=this.tsParseTypeAnnotation(!1),e.asserts=!1,this.finishNode(e,"TSTypePredicate")}},{key:"tsParseThisTypeNode",value:function(){var t=this.startNode();return this.next(),this.finishNode(t,"TSThisType")}},{key:"tsParseTypeQuery",value:function(){var t=this.startNode();return this.expect(87),this.match(83)?t.exprName=this.tsParseImportType():t.exprName=this.tsParseEntityName(),!this.hasPrecedingLineBreak()&&this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSTypeQuery")}},{key:"tsParseInOutModifiers",value:function(t){this.tsParseModifiers({modified:t,allowedModifiers:["in","out"],disallowedModifiers:["public","private","protected","readonly","declare","abstract","override"],errorTemplate:Dr.InvalidModifierOnTypeParameter})}},{key:"tsParseNoneModifiers",value:function(t){this.tsParseModifiers({modified:t,allowedModifiers:[],disallowedModifiers:["in","out"],errorTemplate:Dr.InvalidModifierOnTypeParameterPositions})}},{key:"tsParseTypeParameter",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.tsParseNoneModifiers.bind(this),e=this.startNode();return t(e),e.name=this.tsParseTypeParameterName(),e.constraint=this.tsEatThenParseType(81),e.default=this.tsEatThenParseType(29),this.finishNode(e,"TSTypeParameter")}},{key:"tsTryParseTypeParameters",value:function(t){if(this.match(47))return this.tsParseTypeParameters(t)}},{key:"tsParseTypeParameters",value:function(t){var e=this.startNode();this.match(47)||this.match(140)?this.next():this.unexpected();var i={value:-1};return e.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this,t),!1,!0,i),0===e.params.length&&this.raise(Dr.EmptyTypeParameters,{at:e}),-1!==i.value&&this.addExtra(e,"trailingComma",i.value),this.finishNode(e,"TSTypeParameterDeclaration")}},{key:"tsFillSignature",value:function(t,e){var i=19===t,r="parameters",s="typeAnnotation";e.typeParameters=this.tsTryParseTypeParameters(),this.expect(10),e[r]=this.tsParseBindingListForSignature(),(i||this.match(t))&&(e[s]=this.tsParseTypeOrTypePredicateAnnotation(t))}},{key:"tsParseBindingListForSignature",value:function(){var t=this;return f(k(i.prototype),"parseBindingList",this).call(this,11,41).map((function(e){return"Identifier"!==e.type&&"RestElement"!==e.type&&"ObjectPattern"!==e.type&&"ArrayPattern"!==e.type&&t.raise(Dr.UnsupportedSignatureParameterKind,{at:e,type:e.type}),e}))}},{key:"tsParseTypeMemberSemicolon",value:function(){this.eat(12)||this.isLineTerminator()||this.expect(13)}},{key:"tsParseSignatureMember",value:function(t,e){return this.tsFillSignature(14,e),this.tsParseTypeMemberSemicolon(),this.finishNode(e,t)}},{key:"tsIsUnambiguouslyIndexSignature",value:function(){return this.next(),!!At(this.state.type)&&(this.next(),this.match(14))}},{key:"tsTryParseIndexSignature",value:function(t){if(this.match(0)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))){this.expect(0);var e=this.parseIdentifier();e.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(e),this.expect(3),t.parameters=[e];var i=this.tsTryParseTypeAnnotation();return i&&(t.typeAnnotation=i),this.tsParseTypeMemberSemicolon(),this.finishNode(t,"TSIndexSignature")}}},{key:"tsParsePropertyOrMethodSignature",value:function(t,e){this.eat(17)&&(t.optional=!0);var i=t;if(this.match(10)||this.match(47)){e&&this.raise(Dr.ReadonlyForMethodSignature,{at:t});var r=i;r.kind&&this.match(47)&&this.raise(Dr.AccesorCannotHaveTypeParameters,{at:this.state.curPosition()}),this.tsFillSignature(14,r),this.tsParseTypeMemberSemicolon();var s="parameters",n="typeAnnotation";if("get"===r.kind)r[s].length>0&&(this.raise(Z.BadGetterArity,{at:this.state.curPosition()}),this.isThisParam(r[s][0])&&this.raise(Dr.AccesorCannotDeclareThisParameter,{at:this.state.curPosition()}));else if("set"===r.kind){if(1!==r[s].length)this.raise(Z.BadSetterArity,{at:this.state.curPosition()});else{var a=r[s][0];this.isThisParam(a)&&this.raise(Dr.AccesorCannotDeclareThisParameter,{at:this.state.curPosition()}),"Identifier"===a.type&&a.optional&&this.raise(Dr.SetAccesorCannotHaveOptionalParameter,{at:this.state.curPosition()}),"RestElement"===a.type&&this.raise(Dr.SetAccesorCannotHaveRestParameter,{at:this.state.curPosition()})}r[n]&&this.raise(Dr.SetAccesorCannotHaveReturnType,{at:r[n]})}else r.kind="method";return this.finishNode(r,"TSMethodSignature")}var o=i;e&&(o.readonly=!0);var l=this.tsTryParseTypeAnnotation();return l&&(o.typeAnnotation=l),this.tsParseTypeMemberSemicolon(),this.finishNode(o,"TSPropertySignature")}},{key:"tsParseTypeMember",value:function(){var t=this.startNode();if(this.match(10)||this.match(47))return this.tsParseSignatureMember("TSCallSignatureDeclaration",t);if(this.match(77)){var e=this.startNode();return this.next(),this.match(10)||this.match(47)?this.tsParseSignatureMember("TSConstructSignatureDeclaration",t):(t.key=this.createIdentifier(e,"new"),this.tsParsePropertyOrMethodSignature(t,!1))}this.tsParseModifiers({modified:t,allowedModifiers:["readonly"],disallowedModifiers:["declare","abstract","private","protected","public","static","override"]});var r=this.tsTryParseIndexSignature(t);return r||(f(k(i.prototype),"parsePropertyName",this).call(this,t),t.computed||"Identifier"!==t.key.type||"get"!==t.key.name&&"set"!==t.key.name||!this.tsTokenCanFollowModifier()||(t.kind=t.key.name,f(k(i.prototype),"parsePropertyName",this).call(this,t)),this.tsParsePropertyOrMethodSignature(t,!!t.readonly))}},{key:"tsParseTypeLiteral",value:function(){var t=this.startNode();return t.members=this.tsParseObjectTypeMembers(),this.finishNode(t,"TSTypeLiteral")}},{key:"tsParseObjectTypeMembers",value:function(){this.expect(5);var t=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(8),t}},{key:"tsIsStartOfMappedType",value:function(){return this.next(),this.eat(53)?this.isContextual(120):(this.isContextual(120)&&this.next(),!!this.match(0)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(58))))}},{key:"tsParseMappedTypeParameter",value:function(){var t=this.startNode();return t.name=this.tsParseTypeParameterName(),t.constraint=this.tsExpectThenParseType(58),this.finishNode(t,"TSTypeParameter")}},{key:"tsParseMappedType",value:function(){var t=this.startNode();return this.expect(5),this.match(53)?(t.readonly=this.state.value,this.next(),this.expectContextual(120)):this.eatContextual(120)&&(t.readonly=!0),this.expect(0),t.typeParameter=this.tsParseMappedTypeParameter(),t.nameType=this.eatContextual(93)?this.tsParseType():null,this.expect(3),this.match(53)?(t.optional=this.state.value,this.next(),this.expect(17)):this.eat(17)&&(t.optional=!0),t.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(8),this.finishNode(t,"TSMappedType")}},{key:"tsParseTupleType",value:function(){var t=this,e=this.startNode();e.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);var i=!1,r=null;return e.elementTypes.forEach((function(e){var s=e,n=s.type;!i||"TSRestType"===n||"TSOptionalType"===n||"TSNamedTupleMember"===n&&e.optional||t.raise(Dr.OptionalTypeBeforeRequired,{at:e}),i||(i="TSNamedTupleMember"===n&&e.optional||"TSOptionalType"===n);var a=n;"TSRestType"===n&&(e=e.typeAnnotation,a=e.type);var o="TSNamedTupleMember"===a;null!=r||(r=o),r!==o&&t.raise(Dr.MixedLabeledAndUnlabeledElements,{at:e})})),this.finishNode(e,"TSTupleType")}},{key:"tsParseTupleElementType",value:function(){var t=this.state.startLoc,e=this.eat(21),i=this.tsParseType(),r=this.eat(17),s=this.eat(14);if(s){var n=this.startNodeAtNode(i);n.optional=r,"TSTypeReference"!==i.type||i.typeParameters||"Identifier"!==i.typeName.type?(this.raise(Dr.InvalidTupleMemberLabel,{at:i}),n.label=i):n.label=i.typeName,n.elementType=this.tsParseType(),i=this.finishNode(n,"TSNamedTupleMember")}else if(r){var a=this.startNodeAtNode(i);a.typeAnnotation=i,i=this.finishNode(a,"TSOptionalType")}if(e){var o=this.startNodeAt(t);o.typeAnnotation=i,i=this.finishNode(o,"TSRestType")}return i}},{key:"tsParseParenthesizedType",value:function(){var t=this.startNode();return this.expect(10),t.typeAnnotation=this.tsParseType(),this.expect(11),this.finishNode(t,"TSParenthesizedType")}},{key:"tsParseFunctionOrConstructorType",value:function(t,e){var i=this,r=this.startNode();return"TSConstructorType"===t&&(r.abstract=!!e,e&&this.next(),this.next()),this.tsInAllowConditionalTypesContext((function(){return i.tsFillSignature(19,r)})),this.finishNode(r,t)}},{key:"tsParseLiteralTypeNode",value:function(){var t=this,e=this.startNode();return e.literal=function(){switch(t.state.type){case 132:case 133:case 131:case 85:case 86:return f(k(i.prototype),"parseExprAtom",t).call(t);default:throw t.unexpected()}}(),this.finishNode(e,"TSLiteralType")}},{key:"tsParseTemplateLiteralType",value:function(){var t=this.startNode();return t.literal=f(k(i.prototype),"parseTemplate",this).call(this,!1),this.finishNode(t,"TSLiteralType")}},{key:"parseTemplateSubstitution",value:function(){return this.state.inType?this.tsParseType():f(k(i.prototype),"parseTemplateSubstitution",this).call(this)}},{key:"tsParseThisTypeOrThisTypePredicate",value:function(){var t=this.tsParseThisTypeNode();return this.isContextual(114)&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(t):t}},{key:"tsParseNonArrayType",value:function(){switch(this.state.type){case 131:case 132:case 133:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if("-"===this.state.value){var t=this.startNode(),e=this.lookahead();if(132!==e.type&&133!==e.type)throw this.unexpected();return t.literal=this.parseMaybeUnary(),this.finishNode(t,"TSLiteralType")}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:var i=this.state.type;if(At(i)||88===i||84===i){var r=88===i?"TSVoidKeyword":84===i?"TSNullKeyword":Or(this.state.value);if(void 0!==r&&46!==this.lookaheadCharCode()){var s=this.startNode();return this.next(),this.finishNode(s,r)}return this.tsParseTypeReference()}}throw this.unexpected()}},{key:"tsParseArrayTypeOrHigher",value:function(){var t=this.tsParseNonArrayType();while(!this.hasPrecedingLineBreak()&&this.eat(0))if(this.match(3)){var e=this.startNodeAtNode(t);e.elementType=t,this.expect(3),t=this.finishNode(e,"TSArrayType")}else{var i=this.startNodeAtNode(t);i.objectType=t,i.indexType=this.tsParseType(),this.expect(3),t=this.finishNode(i,"TSIndexedAccessType")}return t}},{key:"tsParseTypeOperator",value:function(){var t=this.startNode(),e=this.state.value;return this.next(),t.operator=e,t.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===e&&this.tsCheckTypeAnnotationForReadOnly(t),this.finishNode(t,"TSTypeOperator")}},{key:"tsCheckTypeAnnotationForReadOnly",value:function(t){switch(t.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(Dr.UnexpectedReadonly,{at:t})}}},{key:"tsParseInferType",value:function(){var t=this,e=this.startNode();this.expectContextual(113);var i=this.startNode();return i.name=this.tsParseTypeParameterName(),i.constraint=this.tsTryParse((function(){return t.tsParseConstraintForInferType()})),e.typeParameter=this.finishNode(i,"TSTypeParameter"),this.finishNode(e,"TSInferType")}},{key:"tsParseConstraintForInferType",value:function(){var t=this;if(this.eat(81)){var e=this.tsInDisallowConditionalTypesContext((function(){return t.tsParseType()}));if(this.state.inDisallowConditionalTypesContext||!this.match(17))return e}}},{key:"tsParseTypeOperatorOrHigher",value:function(){var t=this,e=Rt(this.state.type)&&!this.state.containsEsc;return e?this.tsParseTypeOperator():this.isContextual(113)?this.tsParseInferType():this.tsInAllowConditionalTypesContext((function(){return t.tsParseArrayTypeOrHigher()}))}},{key:"tsParseUnionOrIntersectionType",value:function(t,e,i){var r=this.startNode(),s=this.eat(i),n=[];do{n.push(e())}while(this.eat(i));return 1!==n.length||s?(r.types=n,this.finishNode(r,t)):n[0]}},{key:"tsParseIntersectionTypeOrHigher",value:function(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),45)}},{key:"tsParseUnionTypeOrHigher",value:function(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),43)}},{key:"tsIsStartOfFunctionType",value:function(){return!!this.match(47)||this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}},{key:"tsSkipParameterStart",value:function(){if(At(this.state.type)||this.match(78))return this.next(),!0;if(this.match(5)){var t=this.state.errors,e=t.length;try{return this.parseObjectLike(8,!0),t.length===e}catch(n){return!1}}if(this.match(0)){this.next();var r=this.state.errors,s=r.length;try{return f(k(i.prototype),"parseBindingList",this).call(this,3,93,!0),r.length===s}catch(a){return!1}}return!1}},{key:"tsIsUnambiguouslyStartOfFunctionType",value:function(){if(this.next(),this.match(11)||this.match(21))return!0;if(this.tsSkipParameterStart()){if(this.match(14)||this.match(12)||this.match(17)||this.match(29))return!0;if(this.match(11)&&(this.next(),this.match(19)))return!0}return!1}},{key:"tsParseTypeOrTypePredicateAnnotation",value:function(t){var e=this;return this.tsInType((function(){var i=e.startNode();e.expect(t);var r=e.startNode(),s=!!e.tsTryParse(e.tsParseTypePredicateAsserts.bind(e));if(s&&e.match(78)){var n=e.tsParseThisTypeOrThisTypePredicate();return"TSThisType"===n.type?(r.parameterName=n,r.asserts=!0,r.typeAnnotation=null,n=e.finishNode(r,"TSTypePredicate")):(e.resetStartLocationFromNode(n,r),n.asserts=!0),i.typeAnnotation=n,e.finishNode(i,"TSTypeAnnotation")}var a=e.tsIsIdentifier()&&e.tsTryParse(e.tsParseTypePredicatePrefix.bind(e));if(!a)return s?(r.parameterName=e.parseIdentifier(),r.asserts=s,r.typeAnnotation=null,i.typeAnnotation=e.finishNode(r,"TSTypePredicate"),e.finishNode(i,"TSTypeAnnotation")):e.tsParseTypeAnnotation(!1,i);var o=e.tsParseTypeAnnotation(!1);return r.parameterName=a,r.typeAnnotation=o,r.asserts=s,i.typeAnnotation=e.finishNode(r,"TSTypePredicate"),e.finishNode(i,"TSTypeAnnotation")}))}},{key:"tsTryParseTypeOrTypePredicateAnnotation",value:function(){return this.match(14)?this.tsParseTypeOrTypePredicateAnnotation(14):void 0}},{key:"tsTryParseTypeAnnotation",value:function(){return this.match(14)?this.tsParseTypeAnnotation():void 0}},{key:"tsTryParseType",value:function(){return this.tsEatThenParseType(14)}},{key:"tsParseTypePredicatePrefix",value:function(){var t=this.parseIdentifier();if(this.isContextual(114)&&!this.hasPrecedingLineBreak())return this.next(),t}},{key:"tsParseTypePredicateAsserts",value:function(){if(107!==this.state.type)return!1;var t=this.state.containsEsc;return this.next(),!(!At(this.state.type)&&!this.match(78))&&(t&&this.raise(Z.InvalidEscapedReservedWord,{at:this.state.lastTokStartLoc,reservedWord:"asserts"}),!0)}},{key:"tsParseTypeAnnotation",value:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.startNode();return this.tsInType((function(){e&&t.expect(14),i.typeAnnotation=t.tsParseType()})),this.finishNode(i,"TSTypeAnnotation")}},{key:"tsParseType",value:function(){var t=this;Nr(this.state.inType);var e=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81))return e;var i=this.startNodeAtNode(e);return i.checkType=e,i.extendsType=this.tsInDisallowConditionalTypesContext((function(){return t.tsParseNonConditionalType()})),this.expect(17),i.trueType=this.tsInAllowConditionalTypesContext((function(){return t.tsParseType()})),this.expect(14),i.falseType=this.tsInAllowConditionalTypesContext((function(){return t.tsParseType()})),this.finishNode(i,"TSConditionalType")}},{key:"isAbstractConstructorSignature",value:function(){return this.isContextual(122)&&77===this.lookahead().type}},{key:"tsParseNonConditionalType",value:function(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(77)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}},{key:"tsParseTypeAssertion",value:function(){var t=this;this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(Dr.ReservedTypeAssertion,{at:this.state.startLoc});var e=this.startNode();return e.typeAnnotation=this.tsInType((function(){return t.next(),t.match(75)?t.tsParseTypeReference():t.tsParseType()})),this.expect(48),e.expression=this.parseMaybeUnary(),this.finishNode(e,"TSTypeAssertion")}},{key:"tsParseHeritageClause",value:function(t){var e=this,i=this.state.startLoc,r=this.tsParseDelimitedList("HeritageClauseElement",(function(){var t=e.startNode();return t.expression=e.tsParseEntityName(),e.match(47)&&(t.typeParameters=e.tsParseTypeArguments()),e.finishNode(t,"TSExpressionWithTypeArguments")}));return r.length||this.raise(Dr.EmptyHeritageClauseType,{at:i,token:t}),r}},{key:"tsParseInterfaceDeclaration",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.hasFollowingLineBreak())return null;this.expectContextual(127),e.declare&&(t.declare=!0),At(this.state.type)?(t.id=this.parseIdentifier(),this.checkIdentifier(t.id,Ue)):(t.id=null,this.raise(Dr.MissingInterfaceName,{at:this.state.startLoc})),t.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutModifiers.bind(this)),this.eat(81)&&(t.extends=this.tsParseHeritageClause("extends"));var i=this.startNode();return i.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),t.body=this.finishNode(i,"TSInterfaceBody"),this.finishNode(t,"TSInterfaceDeclaration")}},{key:"tsParseTypeAliasDeclaration",value:function(t){var e=this;return t.id=this.parseIdentifier(),this.checkIdentifier(t.id,ze),t.typeAnnotation=this.tsInType((function(){if(t.typeParameters=e.tsTryParseTypeParameters(e.tsParseInOutModifiers.bind(e)),e.expect(29),e.isContextual(112)&&16!==e.lookahead().type){var i=e.startNode();return e.next(),e.finishNode(i,"TSIntrinsicKeyword")}return e.tsParseType()})),this.semicolon(),this.finishNode(t,"TSTypeAliasDeclaration")}},{key:"tsInNoContext",value:function(t){var e=this.state.context;this.state.context=[e[0]];try{return t()}finally{this.state.context=e}}},{key:"tsInType",value:function(t){var e=this.state.inType;this.state.inType=!0;try{return t()}finally{this.state.inType=e}}},{key:"tsInDisallowConditionalTypesContext",value:function(t){var e=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!0;try{return t()}finally{this.state.inDisallowConditionalTypesContext=e}}},{key:"tsInAllowConditionalTypesContext",value:function(t){var e=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!1;try{return t()}finally{this.state.inDisallowConditionalTypesContext=e}}},{key:"tsEatThenParseType",value:function(t){return this.match(t)?this.tsNextThenParseType():void 0}},{key:"tsExpectThenParseType",value:function(t){var e=this;return this.tsDoThenParseType((function(){return e.expect(t)}))}},{key:"tsNextThenParseType",value:function(){var t=this;return this.tsDoThenParseType((function(){return t.next()}))}},{key:"tsDoThenParseType",value:function(t){var e=this;return this.tsInType((function(){return t(),e.tsParseType()}))}},{key:"tsParseEnumMember",value:function(){var t=this.startNode();return t.id=this.match(131)?f(k(i.prototype),"parseStringLiteral",this).call(this,this.state.value):this.parseIdentifier(!0),this.eat(29)&&(t.initializer=f(k(i.prototype),"parseMaybeAssignAllowIn",this).call(this)),this.finishNode(t,"TSEnumMember")}},{key:"tsParseEnumDeclaration",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.const&&(t.const=!0),e.declare&&(t.declare=!0),this.expectContextual(124),t.id=this.parseIdentifier(),this.checkIdentifier(t.id,t.const?Xe:He),this.expect(5),t.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(t,"TSEnumDeclaration")}},{key:"tsParseModuleBlock",value:function(){var t=this.startNode();return this.scope.enter(de),this.expect(5),f(k(i.prototype),"parseBlockOrModuleBlockBody",this).call(this,t.body=[],void 0,!0,8),this.scope.exit(),this.finishNode(t,"TSModuleBlock")}},{key:"tsParseModuleOrNamespaceDeclaration",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.id=this.parseIdentifier(),e||this.checkIdentifier(t.id,We),this.eat(16)){var i=this.startNode();this.tsParseModuleOrNamespaceDeclaration(i,!0),t.body=i}else this.scope.enter(ke),this.prodParam.enter($i),t.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(t,"TSModuleDeclaration")}},{key:"tsParseAmbientExternalModuleDeclaration",value:function(t){return this.isContextual(110)?(t.global=!0,t.id=this.parseIdentifier()):this.match(131)?t.id=f(k(i.prototype),"parseStringLiteral",this).call(this,this.state.value):this.unexpected(),this.match(5)?(this.scope.enter(ke),this.prodParam.enter($i),t.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(t,"TSModuleDeclaration")}},{key:"tsParseImportEqualsDeclaration",value:function(t,e){t.isExport=e||!1,t.id=this.parseIdentifier(),this.checkIdentifier(t.id,Fe),this.expect(29);var i=this.tsParseModuleReference();return"type"===t.importKind&&"TSExternalModuleReference"!==i.type&&this.raise(Dr.ImportAliasHasImportType,{at:i}),t.moduleReference=i,this.semicolon(),this.finishNode(t,"TSImportEqualsDeclaration")}},{key:"tsIsExternalModuleReference",value:function(){return this.isContextual(117)&&40===this.lookaheadCharCode()}},{key:"tsParseModuleReference",value:function(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(!1)}},{key:"tsParseExternalModuleReference",value:function(){var t=this.startNode();if(this.expectContextual(117),this.expect(10),!this.match(131))throw this.unexpected();return t.expression=f(k(i.prototype),"parseExprAtom",this).call(this),this.expect(11),this.finishNode(t,"TSExternalModuleReference")}},{key:"tsLookAhead",value:function(t){var e=this.state.clone(),i=t();return this.state=e,i}},{key:"tsTryParseAndCatch",value:function(t){var e=this.tryParse((function(e){return t()||e()}));if(!e.aborted&&e.node)return e.error&&(this.state=e.failState),e.node}},{key:"tsTryParse",value:function(t){var e=this.state.clone(),i=t();return void 0!==i&&!1!==i?i:void(this.state=e)}},{key:"tsTryParseDeclare",value:function(t){var e=this;if(!this.isLineTerminator()){var r,s=this.state.type;return this.isContextual(99)&&(s=74,r="let"),this.tsInAmbientContext((function(){if(68===s)return t.declare=!0,f(k(i.prototype),"parseFunctionStatement",e).call(e,t,!1,!1);if(80===s)return t.declare=!0,e.parseClass(t,!0,!1);if(124===s)return e.tsParseEnumDeclaration(t,{declare:!0});if(110===s)return e.tsParseAmbientExternalModuleDeclaration(t);if(75===s||74===s)return e.match(75)&&e.isLookaheadContextual("enum")?(e.expect(75),e.tsParseEnumDeclaration(t,{const:!0,declare:!0})):(t.declare=!0,e.parseVarStatement(t,r||e.state.value,!0));if(127===s){var n=e.tsParseInterfaceDeclaration(t,{declare:!0});if(n)return n}return At(s)?e.tsParseDeclaration(t,e.state.value,!0,null):void 0}))}}},{key:"tsTryParseExportDeclaration",value:function(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0,null)}},{key:"tsParseExpressionStatement",value:function(t,e,i){switch(e.name){case"declare":var r=this.tsTryParseDeclare(t);if(r)return r.declare=!0,r;break;case"global":if(this.match(5)){this.scope.enter(ke),this.prodParam.enter($i);var s=t;return s.global=!0,s.id=e,s.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(s,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(t,e.name,!1,i)}}},{key:"tsParseDeclaration",value:function(t,e,i,r){switch(e){case"abstract":if(this.tsCheckLineTerminator(i)&&(this.match(80)||At(this.state.type)))return this.tsParseAbstractDeclaration(t,r);break;case"module":if(this.tsCheckLineTerminator(i)){if(this.match(131))return this.tsParseAmbientExternalModuleDeclaration(t);if(At(this.state.type))return this.tsParseModuleOrNamespaceDeclaration(t)}break;case"namespace":if(this.tsCheckLineTerminator(i)&&At(this.state.type))return this.tsParseModuleOrNamespaceDeclaration(t);break;case"type":if(this.tsCheckLineTerminator(i)&&At(this.state.type))return this.tsParseTypeAliasDeclaration(t);break}}},{key:"tsCheckLineTerminator",value:function(t){return t?!this.hasFollowingLineBreak()&&(this.next(),!0):!this.isLineTerminator()}},{key:"tsTryParseGenericAsyncArrowFunction",value:function(t){var e=this;if(this.match(47)){var r=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;var s=this.tsTryParseAndCatch((function(){var r=e.startNodeAt(t);return r.typeParameters=e.tsParseTypeParameters(),f(k(i.prototype),"parseFunctionParams",e).call(e,r),r.returnType=e.tsTryParseTypeOrTypePredicateAnnotation(),e.expect(19),r}));if(this.state.maybeInArrowParameters=r,s)return f(k(i.prototype),"parseArrowExpression",this).call(this,s,null,!0)}}},{key:"tsParseTypeArgumentsInExpression",value:function(){if(47===this.reScan_lt())return this.tsParseTypeArguments()}},{key:"tsParseTypeArguments",value:function(){var t=this,e=this.startNode();return e.params=this.tsInType((function(){return t.tsInNoContext((function(){return t.expect(47),t.tsParseDelimitedList("TypeParametersOrArguments",t.tsParseType.bind(t))}))})),0===e.params.length&&this.raise(Dr.EmptyTypeArguments,{at:e}),this.expect(48),this.finishNode(e,"TSTypeParameterInstantiation")}},{key:"tsIsDeclarationStart",value:function(){return Ut(this.state.type)}},{key:"isExportDefaultSpecifier",value:function(){return!this.tsIsDeclarationStart()&&f(k(i.prototype),"isExportDefaultSpecifier",this).call(this)}},{key:"parseAssignableListItem",value:function(t,e){var i,r=this.state.startLoc,s=!1,n=!1;if(void 0!==t){var a={};this.tsParseModifiers({modified:a,allowedModifiers:["public","private","protected","override","readonly"]}),i=a.accessibility,n=a.override,s=a.readonly,!1===t&&(i||s||n)&&this.raise(Dr.UnexpectedParameterModifier,{at:r})}var o=this.parseMaybeDefault();this.parseAssignableListItemTypes(o);var l=this.parseMaybeDefault(o.loc.start,o);if(i||s||n){var c=this.startNodeAt(r);return e.length&&(c.decorators=e),i&&(c.accessibility=i),s&&(c.readonly=s),n&&(c.override=n),"Identifier"!==l.type&&"AssignmentPattern"!==l.type&&this.raise(Dr.UnsupportedParameterPropertyKind,{at:c}),c.parameter=l,this.finishNode(c,"TSParameterProperty")}return e.length&&(o.decorators=e),l}},{key:"isSimpleParameter",value:function(t){return"TSParameterProperty"===t.type&&f(k(i.prototype),"isSimpleParameter",this).call(this,t.parameter)||f(k(i.prototype),"isSimpleParameter",this).call(this,t)}},{key:"parseFunctionBodyAndFinish",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.match(14)&&(t.returnType=this.tsParseTypeOrTypePredicateAnnotation(14));var s="FunctionDeclaration"===e?"TSDeclareFunction":"ClassMethod"===e||"ClassPrivateMethod"===e?"TSDeclareMethod":void 0;return s&&!this.match(5)&&this.isLineTerminator()?this.finishNode(t,s):"TSDeclareFunction"===s&&this.state.isAmbientContext&&(this.raise(Dr.DeclareFunctionHasImplementation,{at:t}),t.declare)?f(k(i.prototype),"parseFunctionBodyAndFinish",this).call(this,t,s,r):f(k(i.prototype),"parseFunctionBodyAndFinish",this).call(this,t,e,r)}},{key:"registerFunctionStatementId",value:function(t){!t.body&&t.id?this.checkIdentifier(t.id,Ve):f(k(i.prototype),"registerFunctionStatementId",this).call(this,t)}},{key:"tsCheckForInvalidTypeCasts",value:function(t){var e=this;t.forEach((function(t){"TSTypeCastExpression"===(null==t?void 0:t.type)&&e.raise(Dr.UnexpectedTypeAnnotation,{at:t.typeAnnotation})}))}},{key:"toReferencedList",value:function(t,e){return this.tsCheckForInvalidTypeCasts(t),t}},{key:"parseArrayLike",value:function(t,e,r,s){var n=f(k(i.prototype),"parseArrayLike",this).call(this,t,e,r,s);return"ArrayExpression"===n.type&&this.tsCheckForInvalidTypeCasts(n.elements),n}},{key:"parseSubscript",value:function(t,e,r,s){var n=this;if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=!1,this.next();var a=this.startNodeAt(e);return a.expression=t,this.finishNode(a,"TSNonNullExpression")}var o=!1;if(this.match(18)&&60===this.lookaheadCharCode()){if(r)return s.stop=!0,t;s.optionalChainMember=o=!0,this.next()}if(this.match(47)||this.match(51)){var l,c=this.tsTryParseAndCatch((function(){if(!r&&n.atPossibleAsyncArrow(t)){var a=n.tsTryParseGenericAsyncArrowFunction(e);if(a)return a}var c=n.tsParseTypeArgumentsInExpression();if(c)if(!o||n.match(10)){if(qt(n.state.type)){var h=f(k(i.prototype),"parseTaggedTemplateExpression",n).call(n,t,e,s);return h.typeParameters=c,h}if(!r&&n.eat(10)){var u=n.startNodeAt(e);return u.callee=t,u.arguments=n.parseCallExpressionArguments(11,!1),n.tsCheckForInvalidTypeCasts(u.arguments),u.typeParameters=c,s.optionalChainMember&&(u.optional=o),n.finishCallExpression(u,s.optionalChainMember)}var p=n.state.type;if(48!==p&&52!==p&&(10===p||!Dt(p)||n.hasPrecedingLineBreak())){var d=n.startNodeAt(e);return d.expression=t,d.typeParameters=c,n.finishNode(d,"TSInstantiationExpression")}}else l=n.state.curPosition()}));if(l&&this.unexpected(l,10),c)return"TSInstantiationExpression"===c.type&&(this.match(16)||this.match(18)&&40!==this.lookaheadCharCode())&&this.raise(Dr.InvalidPropertyAccessAfterInstantiationExpression,{at:this.state.startLoc}),c}return f(k(i.prototype),"parseSubscript",this).call(this,t,e,r,s)}},{key:"parseNewCallee",value:function(t){var e;f(k(i.prototype),"parseNewCallee",this).call(this,t);var r=t.callee;"TSInstantiationExpression"!==r.type||null!=(e=r.extra)&&e.parenthesized||(t.typeParameters=r.typeParameters,t.callee=r.expression)}},{key:"parseExprOp",value:function(t,e,r){var s,n=this;if(Ht(58)>r&&!this.hasPrecedingLineBreak()&&(this.isContextual(93)||(s=this.isContextual(118)))){var a=this.startNodeAt(e);return a.expression=t,a.typeAnnotation=this.tsInType((function(){return n.next(),n.match(75)?(s&&n.raise(Z.UnexpectedKeyword,{at:n.state.startLoc,keyword:"const"}),n.tsParseTypeReference()):n.tsParseType()})),this.finishNode(a,s?"TSSatisfiesExpression":"TSAsExpression"),this.reScan_lt_gt(),this.parseExprOp(a,e,r)}return f(k(i.prototype),"parseExprOp",this).call(this,t,e,r)}},{key:"checkReservedWord",value:function(t,e,r,s){this.state.isAmbientContext||f(k(i.prototype),"checkReservedWord",this).call(this,t,e,r,s)}},{key:"checkImportReflection",value:function(t){f(k(i.prototype),"checkImportReflection",this).call(this,t),t.module&&"value"!==t.importKind&&this.raise(Dr.ImportReflectionHasImportType,{at:t.specifiers[0].loc.start})}},{key:"checkDuplicateExports",value:function(){}},{key:"parseImport",value:function(t){if(t.importKind="value",At(this.state.type)||this.match(55)||this.match(5)){var e=this.lookahead();if(this.isContextual(128)&&12!==e.type&&97!==e.type&&29!==e.type&&(t.importKind="type",this.next(),e=this.lookahead()),At(this.state.type)&&29===e.type)return this.tsParseImportEqualsDeclaration(t)}var r=f(k(i.prototype),"parseImport",this).call(this,t);return"type"===r.importKind&&r.specifiers.length>1&&"ImportDefaultSpecifier"===r.specifiers[0].type&&this.raise(Dr.TypeImportCannotSpecifyDefaultAndNamed,{at:r}),r}},{key:"parseExport",value:function(t,e){if(this.match(83))return this.next(),this.isContextual(128)&&61!==this.lookaheadCharCode()?(t.importKind="type",this.next()):t.importKind="value",this.tsParseImportEqualsDeclaration(t,!0);if(this.eat(29)){var r=t;return r.expression=f(k(i.prototype),"parseExpression",this).call(this),this.semicolon(),this.finishNode(r,"TSExportAssignment")}if(this.eatContextual(93)){var s=t;return this.expectContextual(126),s.id=this.parseIdentifier(),this.semicolon(),this.finishNode(s,"TSNamespaceExportDeclaration")}return this.isContextual(128)&&5===this.lookahead().type?(this.next(),t.exportKind="type"):t.exportKind="value",f(k(i.prototype),"parseExport",this).call(this,t,e)}},{key:"isAbstractClass",value:function(){return this.isContextual(122)&&80===this.lookahead().type}},{key:"parseExportDefaultExpression",value:function(){if(this.isAbstractClass()){var t=this.startNode();return this.next(),t.abstract=!0,this.parseClass(t,!0,!0)}if(this.match(127)){var e=this.tsParseInterfaceDeclaration(this.startNode());if(e)return e}return f(k(i.prototype),"parseExportDefaultExpression",this).call(this)}},{key:"parseVarStatement",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=this.state.isAmbientContext,n=f(k(i.prototype),"parseVarStatement",this).call(this,t,e,r||s);if(!s)return n;var a,o=c(n.declarations);try{for(o.s();!(a=o.n()).done;){var l=a.value,h=l.id,u=l.init;u&&("const"!==e||h.typeAnnotation?this.raise(Dr.InitializerNotAllowedInAmbientContext,{at:u}):"StringLiteral"!==u.type&&"BooleanLiteral"!==u.type&&"NumericLiteral"!==u.type&&"BigIntLiteral"!==u.type&&("TemplateLiteral"!==u.type||u.expressions.length>0)&&!_r(u)&&this.raise(Dr.ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference,{at:u}))}}catch(p){o.e(p)}finally{o.f()}return n}},{key:"parseStatementContent",value:function(t,e){if(this.match(75)&&this.isLookaheadContextual("enum")){var r=this.startNode();return this.expect(75),this.tsParseEnumDeclaration(r,{const:!0})}if(this.isContextual(124))return this.tsParseEnumDeclaration(this.startNode());if(this.isContextual(127)){var s=this.tsParseInterfaceDeclaration(this.startNode());if(s)return s}return f(k(i.prototype),"parseStatementContent",this).call(this,t,e)}},{key:"parseAccessModifier",value:function(){return this.tsParseModifier(["public","protected","private"])}},{key:"tsHasSomeModifiers",value:function(t,e){return e.some((function(e){return Mr(e)?t.accessibility===e:!!t[e]}))}},{key:"tsIsStartOfStaticBlocks",value:function(){return this.isContextual(104)&&123===this.lookaheadCharCode()}},{key:"parseClassMember",value:function(t,e,r){var s=this,n=["declare","private","public","protected","override","abstract","readonly","static"];this.tsParseModifiers({modified:e,allowedModifiers:n,disallowedModifiers:["in","out"],stopOnStartOfClassStaticBlock:!0,errorTemplate:Dr.InvalidModifierOnTypeParameterPositions});var a=function(){s.tsIsStartOfStaticBlocks()?(s.next(),s.next(),s.tsHasSomeModifiers(e,n)&&s.raise(Dr.StaticBlockCannotHaveModifier,{at:s.state.curPosition()}),f(k(i.prototype),"parseClassStaticBlock",s).call(s,t,e)):s.parseClassMemberWithIsStatic(t,e,r,!!e.static)};e.declare?this.tsInAmbientContext(a):a()}},{key:"parseClassMemberWithIsStatic",value:function(t,e,r,s){var n=this.tsTryParseIndexSignature(e);if(n)return t.body.push(n),e.abstract&&this.raise(Dr.IndexSignatureHasAbstract,{at:e}),e.accessibility&&this.raise(Dr.IndexSignatureHasAccessibility,{at:e,modifier:e.accessibility}),e.declare&&this.raise(Dr.IndexSignatureHasDeclare,{at:e}),void(e.override&&this.raise(Dr.IndexSignatureHasOverride,{at:e}));!this.state.inAbstractClass&&e.abstract&&this.raise(Dr.NonAbstractClassHasAbstractMethod,{at:e}),e.override&&(r.hadSuperClass||this.raise(Dr.OverrideNotInSubClass,{at:e})),f(k(i.prototype),"parseClassMemberWithIsStatic",this).call(this,t,e,r,s)}},{key:"parsePostMemberNameModifiers",value:function(t){var e=this.eat(17);e&&(t.optional=!0),t.readonly&&this.match(10)&&this.raise(Dr.ClassMethodHasReadonly,{at:t}),t.declare&&this.match(10)&&this.raise(Dr.ClassMethodHasDeclare,{at:t})}},{key:"parseExpressionStatement",value:function(t,e,r){var s="Identifier"===e.type?this.tsParseExpressionStatement(t,e,r):void 0;return s||f(k(i.prototype),"parseExpressionStatement",this).call(this,t,e,r)}},{key:"shouldParseExportDeclaration",value:function(){return!!this.tsIsDeclarationStart()||f(k(i.prototype),"shouldParseExportDeclaration",this).call(this)}},{key:"parseConditional",value:function(t,e,r){var s=this;if(!this.state.maybeInArrowParameters||!this.match(17))return f(k(i.prototype),"parseConditional",this).call(this,t,e,r);var n=this.tryParse((function(){return f(k(i.prototype),"parseConditional",s).call(s,t,e)}));return n.node?(n.error&&(this.state=n.failState),n.node):(n.error&&f(k(i.prototype),"setOptionalParametersError",this).call(this,r,n.error),t)}},{key:"parseParenItem",value:function(t,e){if(t=f(k(i.prototype),"parseParenItem",this).call(this,t,e),this.eat(17)&&(t.optional=!0,this.resetEndLocation(t)),this.match(14)){var r=this.startNodeAt(e);return r.expression=t,r.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(r,"TSTypeCastExpression")}return t}},{key:"parseExportDeclaration",value:function(t){var e=this;if(!this.state.isAmbientContext&&this.isContextual(123))return this.tsInAmbientContext((function(){return e.parseExportDeclaration(t)}));var r=this.state.startLoc,s=this.eatContextual(123);if(s&&(this.isContextual(123)||!this.shouldParseExportDeclaration()))throw this.raise(Dr.ExpectedAmbientAfterExportDeclare,{at:this.state.startLoc});var n=At(this.state.type),a=n&&this.tsTryParseExportDeclaration()||f(k(i.prototype),"parseExportDeclaration",this).call(this,t);return a?(("TSInterfaceDeclaration"===a.type||"TSTypeAliasDeclaration"===a.type||s)&&(t.exportKind="type"),s&&(this.resetStartLocation(a,r),a.declare=!0),a):null}},{key:"parseClassId",value:function(t,e,r,s){if(e&&!r||!this.isContextual(111)){f(k(i.prototype),"parseClassId",this).call(this,t,e,r,t.declare?Ve:_e);var n=this.tsTryParseTypeParameters(this.tsParseInOutModifiers.bind(this));n&&(t.typeParameters=n)}}},{key:"parseClassPropertyAnnotation",value:function(t){!t.optional&&this.eat(35)&&(t.definite=!0);var e=this.tsTryParseTypeAnnotation();e&&(t.typeAnnotation=e)}},{key:"parseClassProperty",value:function(t){if(this.parseClassPropertyAnnotation(t),this.state.isAmbientContext&&(!t.readonly||t.typeAnnotation)&&this.match(29)&&this.raise(Dr.DeclareClassFieldHasInitializer,{at:this.state.startLoc}),t.abstract&&this.match(29)){var e=t.key;this.raise(Dr.AbstractPropertyHasInitializer,{at:this.state.startLoc,propertyName:"Identifier"!==e.type||t.computed?"[".concat(this.input.slice(e.start,e.end),"]"):e.name})}return f(k(i.prototype),"parseClassProperty",this).call(this,t)}},{key:"parseClassPrivateProperty",value:function(t){return t.abstract&&this.raise(Dr.PrivateElementHasAbstract,{at:t}),t.accessibility&&this.raise(Dr.PrivateElementHasAccessibility,{at:t,modifier:t.accessibility}),this.parseClassPropertyAnnotation(t),f(k(i.prototype),"parseClassPrivateProperty",this).call(this,t)}},{key:"pushClassMethod",value:function(t,e,r,s,n,a){var o=this.tsTryParseTypeParameters();o&&n&&this.raise(Dr.ConstructorHasTypeParameters,{at:o});var l=e.declare,c=void 0!==l&&l,h=e.kind;!c||"get"!==h&&"set"!==h||this.raise(Dr.DeclareAccessor,{at:e,kind:h}),o&&(e.typeParameters=o),f(k(i.prototype),"pushClassMethod",this).call(this,t,e,r,s,n,a)}},{key:"pushClassPrivateMethod",value:function(t,e,r,s){var n=this.tsTryParseTypeParameters();n&&(e.typeParameters=n),f(k(i.prototype),"pushClassPrivateMethod",this).call(this,t,e,r,s)}},{key:"declareClassPrivateMethodInScope",value:function(t,e){"TSDeclareMethod"!==t.type&&("MethodDefinition"!==t.type||t.value.body)&&f(k(i.prototype),"declareClassPrivateMethodInScope",this).call(this,t,e)}},{key:"parseClassSuper",value:function(t){f(k(i.prototype),"parseClassSuper",this).call(this,t),t.superClass&&(this.match(47)||this.match(51))&&(t.superTypeParameters=this.tsParseTypeArgumentsInExpression()),this.eatContextual(111)&&(t.implements=this.tsParseHeritageClause("implements"))}},{key:"parseObjPropValue",value:function(t,e,r,s,n,a,o){var l=this.tsTryParseTypeParameters();return l&&(t.typeParameters=l),f(k(i.prototype),"parseObjPropValue",this).call(this,t,e,r,s,n,a,o)}},{key:"parseFunctionParams",value:function(t,e){var r=this.tsTryParseTypeParameters();r&&(t.typeParameters=r),f(k(i.prototype),"parseFunctionParams",this).call(this,t,e)}},{key:"parseVarId",value:function(t,e){f(k(i.prototype),"parseVarId",this).call(this,t,e),"Identifier"===t.id.type&&!this.hasPrecedingLineBreak()&&this.eat(35)&&(t.definite=!0);var r=this.tsTryParseTypeAnnotation();r&&(t.id.typeAnnotation=r,this.resetEndLocation(t.id))}},{key:"parseAsyncArrowFromCallExpression",value:function(t,e){return this.match(14)&&(t.returnType=this.tsParseTypeAnnotation()),f(k(i.prototype),"parseAsyncArrowFromCallExpression",this).call(this,t,e)}},{key:"parseMaybeAssign",value:function(t,e){var r,s,n,a,o,l,c,h,u,p,d,m=this;if(this.hasPlugin("jsx")&&(this.match(140)||this.match(47))){if(h=this.state.clone(),u=this.tryParse((function(){return f(k(i.prototype),"parseMaybeAssign",m).call(m,t,e)}),h),!u.error)return u.node;var y=this.state.context,v=y[y.length-1];v!==nt.j_oTag&&v!==nt.j_expr||y.pop()}if((null==(r=u)||!r.error)&&!this.match(47))return f(k(i.prototype),"parseMaybeAssign",this).call(this,t,e);h&&h!==this.state||(h=this.state.clone());var g=this.tryParse((function(r){var s,n;d=m.tsParseTypeParameters();var a=f(k(i.prototype),"parseMaybeAssign",m).call(m,t,e);return("ArrowFunctionExpression"!==a.type||null!=(s=a.extra)&&s.parenthesized)&&r(),0!==(null==(n=d)?void 0:n.params.length)&&m.resetStartLocationFromNode(a,d),a.typeParameters=d,a}),h);if(!g.error&&!g.aborted)return d&&this.reportReservedArrowTypeParam(d),g.node;if(!u&&(Nr(!this.hasPlugin("jsx")),p=this.tryParse((function(){return f(k(i.prototype),"parseMaybeAssign",m).call(m,t,e)}),h),!p.error))return p.node;if(null!=(s=u)&&s.node)return this.state=u.failState,u.node;if(g.node)return this.state=g.failState,d&&this.reportReservedArrowTypeParam(d),g.node;if(null!=(n=p)&&n.node)return this.state=p.failState,p.node;if(null!=(a=u)&&a.thrown)throw u.error;if(g.thrown)throw g.error;if(null!=(o=p)&&o.thrown)throw p.error;throw(null==(l=u)?void 0:l.error)||g.error||(null==(c=p)?void 0:c.error)}},{key:"reportReservedArrowTypeParam",value:function(t){var e;1!==t.params.length||null!=(e=t.extra)&&e.trailingComma||!this.getPluginOption("typescript","disallowAmbiguousJSXLike")||this.raise(Dr.ReservedArrowTypeParam,{at:t})}},{key:"parseMaybeUnary",value:function(t,e){return!this.hasPlugin("jsx")&&this.match(47)?this.tsParseTypeAssertion():f(k(i.prototype),"parseMaybeUnary",this).call(this,t,e)}},{key:"parseArrow",value:function(t){var e=this;if(this.match(14)){var r=this.tryParse((function(t){var i=e.tsParseTypeOrTypePredicateAnnotation(14);return!e.canInsertSemicolon()&&e.match(19)||t(),i}));if(r.aborted)return;r.thrown||(r.error&&(this.state=r.failState),t.returnType=r.node)}return f(k(i.prototype),"parseArrow",this).call(this,t)}},{key:"parseAssignableListItemTypes",value:function(t){this.eat(17)&&("Identifier"===t.type||this.state.isAmbientContext||this.state.inType||this.raise(Dr.PatternIsOptional,{at:t}),t.optional=!0);var e=this.tsTryParseTypeAnnotation();return e&&(t.typeAnnotation=e),this.resetEndLocation(t),t}},{key:"isAssignable",value:function(t,e){switch(t.type){case"TSTypeCastExpression":return this.isAssignable(t.expression,e);case"TSParameterProperty":return!0;default:return f(k(i.prototype),"isAssignable",this).call(this,t,e)}}},{key:"toAssignable",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];switch(t.type){case"ParenthesizedExpression":this.toAssignableParenthesizedExpression(t,e);break;case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":e?this.expressionScope.recordArrowParemeterBindingError(Dr.UnexpectedTypeCastInParameter,{at:t}):this.raise(Dr.UnexpectedTypeCastInParameter,{at:t}),this.toAssignable(t.expression,e);break;case"AssignmentExpression":e||"TSTypeCastExpression"!==t.left.type||(t.left=this.typeCastToParameter(t.left));default:f(k(i.prototype),"toAssignable",this).call(this,t,e)}}},{key:"toAssignableParenthesizedExpression",value:function(t,e){switch(t.expression.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":case"ParenthesizedExpression":this.toAssignable(t.expression,e);break;default:f(k(i.prototype),"toAssignable",this).call(this,t,e)}}},{key:"checkToRestConversion",value:function(t,e){switch(t.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":this.checkToRestConversion(t.expression,!1);break;default:f(k(i.prototype),"checkToRestConversion",this).call(this,t,e)}}},{key:"isValidLVal",value:function(t,e,r){return Cr({TSTypeCastExpression:!0,TSParameterProperty:"parameter",TSNonNullExpression:"expression",TSAsExpression:(r!==qe||!e)&&["expression",!0],TSSatisfiesExpression:(r!==qe||!e)&&["expression",!0],TSTypeAssertion:(r!==qe||!e)&&["expression",!0]},t)||f(k(i.prototype),"isValidLVal",this).call(this,t,e,r)}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return f(k(i.prototype),"parseBindingAtom",this).call(this)}}},{key:"parseMaybeDecoratorArguments",value:function(t){if(this.match(47)||this.match(51)){var e=this.tsParseTypeArgumentsInExpression();if(this.match(10)){var r=f(k(i.prototype),"parseMaybeDecoratorArguments",this).call(this,t);return r.typeParameters=e,r}this.unexpected(null,10)}return f(k(i.prototype),"parseMaybeDecoratorArguments",this).call(this,t)}},{key:"checkCommaAfterRest",value:function(t){return this.state.isAmbientContext&&this.match(12)&&this.lookaheadCharCode()===t?(this.next(),!1):f(k(i.prototype),"checkCommaAfterRest",this).call(this,t)}},{key:"isClassMethod",value:function(){return this.match(47)||f(k(i.prototype),"isClassMethod",this).call(this)}},{key:"isClassProperty",value:function(){return this.match(35)||this.match(14)||f(k(i.prototype),"isClassProperty",this).call(this)}},{key:"parseMaybeDefault",value:function(t,e){var r=f(k(i.prototype),"parseMaybeDefault",this).call(this,t,e);return"AssignmentPattern"===r.type&&r.typeAnnotation&&r.right.start<r.typeAnnotation.start&&this.raise(Dr.TypeAnnotationAfterAssign,{at:r.typeAnnotation}),r}},{key:"getTokenFromCode",value:function(t){if(this.state.inType){if(62===t)return this.finishOp(48,1);if(60===t)return this.finishOp(47,1)}return f(k(i.prototype),"getTokenFromCode",this).call(this,t)}},{key:"reScan_lt_gt",value:function(){var t=this.state.type;47===t?(this.state.pos-=1,this.readToken_lt()):48===t&&(this.state.pos-=1,this.readToken_gt())}},{key:"reScan_lt",value:function(){var t=this.state.type;return 51===t?(this.state.pos-=2,this.finishOp(47,1),47):t}},{key:"toAssignableList",value:function(t,e,r){for(var s=0;s<t.length;s++){var n=t[s];"TSTypeCastExpression"===(null==n?void 0:n.type)&&(t[s]=this.typeCastToParameter(n))}f(k(i.prototype),"toAssignableList",this).call(this,t,e,r)}},{key:"typeCastToParameter",value:function(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.loc.end),t.expression}},{key:"shouldParseArrow",value:function(t){var e=this;return this.match(14)?t.every((function(t){return e.isAssignable(t,!0)})):f(k(i.prototype),"shouldParseArrow",this).call(this,t)}},{key:"shouldParseAsyncArrow",value:function(){return this.match(14)||f(k(i.prototype),"shouldParseAsyncArrow",this).call(this)}},{key:"canHaveLeadingDecorator",value:function(){return f(k(i.prototype),"canHaveLeadingDecorator",this).call(this)||this.isAbstractClass()}},{key:"jsxParseOpeningElementAfterName",value:function(t){var e=this;if(this.match(47)||this.match(51)){var r=this.tsTryParseAndCatch((function(){return e.tsParseTypeArgumentsInExpression()}));r&&(t.typeParameters=r)}return f(k(i.prototype),"jsxParseOpeningElementAfterName",this).call(this,t)}},{key:"getGetterSetterExpectedParamCount",value:function(t){var e=f(k(i.prototype),"getGetterSetterExpectedParamCount",this).call(this,t),r=this.getObjectOrClassMethodParams(t),s=r[0],n=s&&this.isThisParam(s);return n?e+1:e}},{key:"parseCatchClauseParam",value:function(){var t=f(k(i.prototype),"parseCatchClauseParam",this).call(this),e=this.tsTryParseTypeAnnotation();return e&&(t.typeAnnotation=e,this.resetEndLocation(t)),t}},{key:"tsInAmbientContext",value:function(t){var e=this.state.isAmbientContext;this.state.isAmbientContext=!0;try{return t()}finally{this.state.isAmbientContext=e}}},{key:"parseClass",value:function(t,e,r){var s=this.state.inAbstractClass;this.state.inAbstractClass=!!t.abstract;try{return f(k(i.prototype),"parseClass",this).call(this,t,e,r)}finally{this.state.inAbstractClass=s}}},{key:"tsParseAbstractDeclaration",value:function(t,e){if(this.match(80))return t.abstract=!0,this.maybeTakeDecorators(e,this.parseClass(t,!0,!1));if(this.isContextual(127)){if(!this.hasFollowingLineBreak())return t.abstract=!0,this.raise(Dr.NonClassMethodPropertyHasAbstractModifer,{at:t}),this.tsParseInterfaceDeclaration(t)}else this.unexpected(null,80)}},{key:"parseMethod",value:function(t,e,r,s,n,a,o){var l=f(k(i.prototype),"parseMethod",this).call(this,t,e,r,s,n,a,o);if(l.abstract){var c=this.hasPlugin("estree")?!!l.value.body:!!l.body;if(c){var h=l.key;this.raise(Dr.AbstractMethodHasImplementation,{at:l,methodName:"Identifier"!==h.type||l.computed?"[".concat(this.input.slice(h.start,h.end),"]"):h.name})}}return l}},{key:"tsParseTypeParameterName",value:function(){var t=this.parseIdentifier();return t.name}},{key:"shouldParseAsAmbientContext",value:function(){return!!this.getPluginOption("typescript","dts")}},{key:"parse",value:function(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),f(k(i.prototype),"parse",this).call(this)}},{key:"getExpression",value:function(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),f(k(i.prototype),"getExpression",this).call(this)}},{key:"parseExportSpecifier",value:function(t,e,r,s){return!e&&s?(this.parseTypeOnlyImportExportSpecifier(t,!1,r),this.finishNode(t,"ExportSpecifier")):(t.exportKind="value",f(k(i.prototype),"parseExportSpecifier",this).call(this,t,e,r,s))}},{key:"parseImportSpecifier",value:function(t,e,r,s,n){return!e&&s?(this.parseTypeOnlyImportExportSpecifier(t,!0,r),this.finishNode(t,"ImportSpecifier")):(t.importKind="value",f(k(i.prototype),"parseImportSpecifier",this).call(this,t,e,r,s,r?Je:Fe))}},{key:"parseTypeOnlyImportExportSpecifier",value:function(t,e,i){var r,s=e?"imported":"local",n=e?"local":"exported",a=t[s],o=!1,l=!0,c=a.loc.start;if(this.isContextual(93)){var h=this.parseIdentifier();if(this.isContextual(93)){var u=this.parseIdentifier();Ct(this.state.type)?(o=!0,a=h,r=e?this.parseIdentifier():this.parseModuleExportName(),l=!1):(r=u,l=!1)}else Ct(this.state.type)?(l=!1,r=e?this.parseIdentifier():this.parseModuleExportName()):(o=!0,a=h)}else Ct(this.state.type)&&(o=!0,e?(a=this.parseIdentifier(!0),this.isContextual(93)||this.checkReservedWord(a.name,a.loc.start,!0,!0)):a=this.parseModuleExportName());o&&i&&this.raise(e?Dr.TypeModifierIsUsedInTypeImports:Dr.TypeModifierIsUsedInTypeExports,{at:c}),t[s]=a,t[n]=r;var p=e?"importKind":"exportKind";t[p]=o?"type":"value",l&&this.eatContextual(93)&&(t[n]=e?this.parseIdentifier():this.parseModuleExportName()),t[n]||(t[n]=cr(t[s])),e&&this.checkIdentifier(t[n],o?Je:Fe)}}]),i}(t)};function _r(t){if("MemberExpression"!==t.type)return!1;var e=t.computed,i=t.property;return(!e||"StringLiteral"===i.type||!("TemplateLiteral"!==i.type||i.expressions.length>0))&&Br(t.object)}function Br(t){return"Identifier"===t.type||"MemberExpression"===t.type&&(!t.computed&&Br(t.object))}var jr=Q(l||(l=T(["placeholders"])))({ClassNameIsRequired:"A class name is required.",UnexpectedSpace:"Unexpected space in placeholder."}),Rr=function(t){return function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"parsePlaceholder",value:function(t){if(this.match(142)){var e=this.startNode();return this.next(),this.assertNoSpace(),e.name=f(k(i.prototype),"parseIdentifier",this).call(this,!0),this.assertNoSpace(),this.expect(142),this.finishPlaceholder(e,t)}}},{key:"finishPlaceholder",value:function(t,e){var i=!(!t.expectedNode||"Placeholder"!==t.type);return t.expectedNode=e,i?t:this.finishNode(t,"Placeholder")}},{key:"getTokenFromCode",value:function(t){return 37===t&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(142,2):f(k(i.prototype),"getTokenFromCode",this).call(this,t)}},{key:"parseExprAtom",value:function(t){return this.parsePlaceholder("Expression")||f(k(i.prototype),"parseExprAtom",this).call(this,t)}},{key:"parseIdentifier",value:function(t){return this.parsePlaceholder("Identifier")||f(k(i.prototype),"parseIdentifier",this).call(this,t)}},{key:"checkReservedWord",value:function(t,e,r,s){void 0!==t&&f(k(i.prototype),"checkReservedWord",this).call(this,t,e,r,s)}},{key:"parseBindingAtom",value:function(){return this.parsePlaceholder("Pattern")||f(k(i.prototype),"parseBindingAtom",this).call(this)}},{key:"isValidLVal",value:function(t,e,r){return"Placeholder"===t||f(k(i.prototype),"isValidLVal",this).call(this,t,e,r)}},{key:"toAssignable",value:function(t,e){t&&"Placeholder"===t.type&&"Expression"===t.expectedNode?t.expectedNode="Pattern":f(k(i.prototype),"toAssignable",this).call(this,t,e)}},{key:"chStartsBindingIdentifier",value:function(t,e){if(f(k(i.prototype),"chStartsBindingIdentifier",this).call(this,t,e))return!0;var r=this.lookahead();return 142===r.type}},{key:"verifyBreakContinue",value:function(t,e){t.label&&"Placeholder"===t.label.type||f(k(i.prototype),"verifyBreakContinue",this).call(this,t,e)}},{key:"parseExpressionStatement",value:function(t,e){if("Placeholder"!==e.type||e.extra&&e.extra.parenthesized)return f(k(i.prototype),"parseExpressionStatement",this).call(this,t,e);if(this.match(14)){var r=t;return r.label=this.finishPlaceholder(e,"Identifier"),this.next(),r.body=f(k(i.prototype),"parseStatementOrFunctionDeclaration",this).call(this,!1),this.finishNode(r,"LabeledStatement")}return this.semicolon(),t.name=e.name,this.finishPlaceholder(t,"Statement")}},{key:"parseBlock",value:function(t,e,r){return this.parsePlaceholder("BlockStatement")||f(k(i.prototype),"parseBlock",this).call(this,t,e,r)}},{key:"parseFunctionId",value:function(t){return this.parsePlaceholder("Identifier")||f(k(i.prototype),"parseFunctionId",this).call(this,t)}},{key:"parseClass",value:function(t,e,r){var s=e?"ClassDeclaration":"ClassExpression";this.next();var n=this.state.strict,a=this.parsePlaceholder("Identifier");if(a){if(!(this.match(81)||this.match(142)||this.match(5))){if(r||!e)return t.id=null,t.body=this.finishPlaceholder(a,"ClassBody"),this.finishNode(t,s);throw this.raise(jr.ClassNameIsRequired,{at:this.state.startLoc})}t.id=a}else this.parseClassId(t,e,r);return f(k(i.prototype),"parseClassSuper",this).call(this,t),t.body=this.parsePlaceholder("ClassBody")||f(k(i.prototype),"parseClassBody",this).call(this,!!t.superClass,n),this.finishNode(t,s)}},{key:"parseExport",value:function(t,e){var r=this.parsePlaceholder("Identifier");if(!r)return f(k(i.prototype),"parseExport",this).call(this,t,e);if(!this.isContextual(97)&&!this.match(12))return t.specifiers=[],t.source=null,t.declaration=this.finishPlaceholder(r,"Declaration"),this.finishNode(t,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");var s=this.startNode();return s.exported=r,t.specifiers=[this.finishNode(s,"ExportDefaultSpecifier")],f(k(i.prototype),"parseExport",this).call(this,t,e)}},{key:"isExportDefaultSpecifier",value:function(){if(this.match(65)){var t=this.nextTokenStart();if(this.isUnparsedContextual(t,"from")&&this.input.startsWith(zt(142),this.nextTokenStartSince(t+4)))return!0}return f(k(i.prototype),"isExportDefaultSpecifier",this).call(this)}},{key:"maybeParseExportDefaultSpecifier",value:function(t){return!!(t.specifiers&&t.specifiers.length>0)||f(k(i.prototype),"maybeParseExportDefaultSpecifier",this).call(this,t)}},{key:"checkExport",value:function(t){var e=t.specifiers;null!=e&&e.length&&(t.specifiers=e.filter((function(t){return"Placeholder"===t.exported.type}))),f(k(i.prototype),"checkExport",this).call(this,t),t.specifiers=e}},{key:"parseImport",value:function(t){var e=this.parsePlaceholder("Identifier");if(!e)return f(k(i.prototype),"parseImport",this).call(this,t);if(t.specifiers=[],!this.isContextual(97)&&!this.match(12))return t.source=this.finishPlaceholder(e,"StringLiteral"),this.semicolon(),this.finishNode(t,"ImportDeclaration");var r=this.startNodeAtNode(e);if(r.local=e,t.specifiers.push(this.finishNode(r,"ImportDefaultSpecifier")),this.eat(12)){var s=this.maybeParseStarImportSpecifier(t);s||this.parseNamedImportSpecifiers(t)}return this.expectContextual(97),t.source=this.parseImportSource(),this.semicolon(),this.finishNode(t,"ImportDeclaration")}},{key:"parseImportSource",value:function(){return this.parsePlaceholder("StringLiteral")||f(k(i.prototype),"parseImportSource",this).call(this)}},{key:"assertNoSpace",value:function(){this.state.start>this.state.lastTokEndLoc.index&&this.raise(jr.UnexpectedSpace,{at:this.state.lastTokEndLoc})}}]),i}(t)},Ur=function(t){return function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"parseV8Intrinsic",value:function(){if(this.match(54)){var t=this.state.startLoc,e=this.startNode();if(this.next(),At(this.state.type)){var i=this.parseIdentifierName(),r=this.createIdentifier(e,i);if(r.type="V8IntrinsicIdentifier",this.match(10))return r}this.unexpected(t)}}},{key:"parseExprAtom",value:function(t){return this.parseV8Intrinsic()||f(k(i.prototype),"parseExprAtom",this).call(this,t)}}]),i}(t)};function zr(t,e){var i="string"===typeof e?[e,{}]:e,r=w(i,2),s=r[0],n=r[1],a=Object.keys(n),o=0===a.length;return t.some((function(t){if("string"===typeof t)return o&&t===s;var e=w(t,2),i=e[0],r=e[1];if(i!==s)return!1;var l,h=c(a);try{for(h.s();!(l=h.n()).done;){var u=l.value;if(r[u]!==n[u])return!1}}catch(p){h.e(p)}finally{h.f()}return!0}))}function Hr(t,e,i){var r=t.find((function(t){return Array.isArray(t)?t[0]===e:t===e}));return r&&Array.isArray(r)&&r.length>1?r[1][i]:null}var Vr=["minimal","fsharp","hack","smart"],qr=["^^","@@","^","%","#"],Kr=["hash","bar"];function Xr(t){if(zr(t,"decorators")){if(zr(t,"decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");var e=Hr(t,"decorators","decoratorsBeforeExport");if(null!=e&&"boolean"!==typeof e)throw new Error("'decoratorsBeforeExport' must be a boolean.");var i=Hr(t,"decorators","allowCallParenthesized");if(null!=i&&"boolean"!==typeof i)throw new Error("'allowCallParenthesized' must be a boolean.")}if(zr(t,"flow")&&zr(t,"typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(zr(t,"placeholders")&&zr(t,"v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(zr(t,"pipelineOperator")){var r=Hr(t,"pipelineOperator","proposal");if(!Vr.includes(r)){var s=Vr.map((function(t){return'"'.concat(t,'"')})).join(", ");throw new Error('"pipelineOperator" requires "proposal" option whose value must be one of: '.concat(s,"."))}var n=zr(t,["recordAndTuple",{syntaxType:"hash"}]);if("hack"===r){if(zr(t,"placeholders"))throw new Error("Cannot combine placeholders plugin and Hack-style pipes.");if(zr(t,"v8intrinsic"))throw new Error("Cannot combine v8intrinsic plugin and Hack-style pipes.");var a=Hr(t,"pipelineOperator","topicToken");if(!qr.includes(a)){var o=qr.map((function(t){return'"'.concat(t,'"')})).join(", ");throw new Error('"pipelineOperator" in "proposal": "hack" mode also requires a "topicToken" option whose value must be one of: '.concat(o,"."))}if("#"===a&&n)throw new Error('Plugin conflict between `["pipelineOperator", { proposal: "hack", topicToken: "#" }]` and `["recordAndtuple", { syntaxType: "hash"}]`.')}else if("smart"===r&&n)throw new Error('Plugin conflict between `["pipelineOperator", { proposal: "smart" }]` and `["recordAndtuple", { syntaxType: "hash"}]`.')}if(zr(t,"moduleAttributes")){if(zr(t,"importAssertions"))throw new Error("Cannot combine importAssertions and moduleAttributes plugins.");var l=Hr(t,"moduleAttributes","version");if("may-2020"!==l)throw new Error("The 'moduleAttributes' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is 'may-2020'.")}if(zr(t,"recordAndTuple")&&null!=Hr(t,"recordAndTuple","syntaxType")&&!Kr.includes(Hr(t,"recordAndTuple","syntaxType")))throw new Error("The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: "+Kr.map((function(t){return"'".concat(t,"'")})).join(", "));if(zr(t,"asyncDoExpressions")&&!zr(t,"doExpressions")){var c=new Error("'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.");throw c.missingPlugins="doExpressions",c}}var Wr={estree:rt,jsx:Er,flow:br,typescript:Fr,v8intrinsic:Ur,placeholders:Rr},Jr=Object.keys(Wr),Yr={sourceType:"script",sourceFilename:void 0,startColumn:0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createParenthesizedExpressions:!1,errorRecovery:!1,attachComment:!0};function Gr(t){for(var e={},i=0,r=Object.keys(Yr);i<r.length;i++){var s=r[i];e[s]=t&&null!=t[s]?t[s]:Yr[s]}return e}var $r=function(t,e){return Object.hasOwnProperty.call(t,e)&&t[e]},Qr=function t(e){return"ParenthesizedExpression"===e.type?t(e.expression):e},Zr=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"toAssignable",value:function(t){var e,i,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=void 0;switch(("ParenthesizedExpression"===t.type||null!=(e=t.extra)&&e.parenthesized)&&(s=Qr(t),r?"Identifier"===s.type?this.expressionScope.recordArrowParemeterBindingError(Z.InvalidParenthesizedAssignment,{at:t}):"MemberExpression"!==s.type&&this.raise(Z.InvalidParenthesizedAssignment,{at:t}):this.raise(Z.InvalidParenthesizedAssignment,{at:t})),t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":t.type="ObjectPattern";for(var n=0,a=t.properties.length,o=a-1;n<a;n++){var l,c=t.properties[n],h=n===o;this.toAssignableObjectExpressionProp(c,h,r),h&&"RestElement"===c.type&&null!=(l=t.extra)&&l.trailingCommaLoc&&this.raise(Z.RestTrailingComma,{at:t.extra.trailingCommaLoc})}break;case"ObjectProperty":var u=t.key,p=t.value;this.isPrivateName(u)&&this.classScope.usePrivateName(this.getPrivateNameSV(u),u.loc.start),this.toAssignable(p,r);break;case"SpreadElement":throw new Error("Internal @babel/parser error (this is a bug, please report it). SpreadElement should be converted by .toAssignable's caller.");case"ArrayExpression":t.type="ArrayPattern",this.toAssignableList(t.elements,null==(i=t.extra)?void 0:i.trailingCommaLoc,r);break;case"AssignmentExpression":"="!==t.operator&&this.raise(Z.MissingEqInAssignment,{at:t.left.loc.end}),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left,r);break;case"ParenthesizedExpression":this.toAssignable(s,r);break}}},{key:"toAssignableObjectExpressionProp",value:function(t,e,i){if("ObjectMethod"===t.type)this.raise("get"===t.kind||"set"===t.kind?Z.PatternHasAccessor:Z.PatternHasMethod,{at:t.key});else if("SpreadElement"===t.type){t.type="RestElement";var r=t.argument;this.checkToRestConversion(r,!1),this.toAssignable(r,i),e||this.raise(Z.RestTrailingComma,{at:t})}else this.toAssignable(t,i)}},{key:"toAssignableList",value:function(t,e,i){for(var r=t.length-1,s=0;s<=r;s++){var n=t[s];if(n){if("SpreadElement"===n.type){n.type="RestElement";var a=n.argument;this.checkToRestConversion(a,!0),this.toAssignable(a,i)}else this.toAssignable(n,i);"RestElement"===n.type&&(s<r?this.raise(Z.RestTrailingComma,{at:n}):e&&this.raise(Z.RestTrailingComma,{at:e}))}}}},{key:"isAssignable",value:function(t,e){var i=this;switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":return!0;case"ObjectExpression":var r=t.properties.length-1;return t.properties.every((function(t,e){return"ObjectMethod"!==t.type&&(e===r||"SpreadElement"!==t.type)&&i.isAssignable(t)}));case"ObjectProperty":return this.isAssignable(t.value);case"SpreadElement":return this.isAssignable(t.argument);case"ArrayExpression":return t.elements.every((function(t){return null===t||i.isAssignable(t)}));case"AssignmentExpression":return"="===t.operator;case"ParenthesizedExpression":return this.isAssignable(t.expression);case"MemberExpression":case"OptionalMemberExpression":return!e;default:return!1}}},{key:"toReferencedList",value:function(t,e){return t}},{key:"toReferencedListDeep",value:function(t,e){this.toReferencedList(t,e);var i,r=c(t);try{for(r.s();!(i=r.n()).done;){var s=i.value;"ArrayExpression"===(null==s?void 0:s.type)&&this.toReferencedListDeep(s.elements)}}catch(n){r.e(n)}finally{r.f()}}},{key:"parseSpread",value:function(t){var e=this.startNode();return this.next(),e.argument=this.parseMaybeAssignAllowIn(t,void 0),this.finishNode(e,"SpreadElement")}},{key:"parseRestBinding",value:function(){var t=this.startNode();return this.next(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case 0:var t=this.startNode();return this.next(),t.elements=this.parseBindingList(3,93,!0),this.finishNode(t,"ArrayPattern");case 5:return this.parseObjectLike(8,!0)}return this.parseIdentifier()}},{key:"parseBindingList",value:function(t,e,i,r){var s=[],n=!0;while(!this.eat(t))if(n?n=!1:this.expect(12),i&&this.match(12))s.push(null);else{if(this.eat(t))break;if(this.match(21)){if(s.push(this.parseAssignableListItemTypes(this.parseRestBinding())),!this.checkCommaAfterRest(e)){this.expect(t);break}}else{var a=[];this.match(26)&&this.hasPlugin("decorators")&&this.raise(Z.UnsupportedParameterDecorator,{at:this.state.startLoc});while(this.match(26))a.push(this.parseDecorator());s.push(this.parseAssignableListItem(r,a))}}return s}},{key:"parseBindingRestProperty",value:function(t){return this.next(),t.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(t,"RestElement")}},{key:"parseBindingProperty",value:function(){var t=this.startNode(),e=this.state,i=e.type,r=e.startLoc;return 21===i?this.parseBindingRestProperty(t):(136===i?(this.expectPlugin("destructuringPrivate",r),this.classScope.usePrivateName(this.state.value,r),t.key=this.parsePrivateName()):this.parsePropertyName(t),t.method=!1,this.parseObjPropValue(t,r,!1,!1,!0,!1))}},{key:"parseAssignableListItem",value:function(t,e){var i=this.parseMaybeDefault();this.parseAssignableListItemTypes(i);var r=this.parseMaybeDefault(i.loc.start,i);return e.length&&(i.decorators=e),r}},{key:"parseAssignableListItemTypes",value:function(t){return t}},{key:"parseMaybeDefault",value:function(t,e){var i;if(null!=t||(t=this.state.startLoc),e=null!=(i=e)?i:this.parseBindingAtom(),!this.eat(29))return e;var r=this.startNodeAt(t);return r.left=e,r.right=this.parseMaybeAssignAllowIn(),this.finishNode(r,"AssignmentPattern")}},{key:"isValidLVal",value:function(t,e,i){return $r({AssignmentPattern:"left",RestElement:"argument",ObjectProperty:"value",ParenthesizedExpression:"expression",ArrayPattern:"elements",ObjectPattern:"properties"},t)}},{key:"checkLVal",value:function(t,e){var i,r=e.in,s=e.binding,n=void 0===s?qe:s,a=e.checkClashes,o=void 0!==a&&a,l=e.strictModeChanged,h=void 0!==l&&l,u=e.allowingSloppyLetBinding,p=void 0===u?!(n&Se):u,d=e.hasParenthesizedAncestor,f=void 0!==d&&d,m=t.type;if(!this.isObjectMethod(t))if("MemberExpression"!==m)if("Identifier"!==t.type){var y=this.isValidLVal(t.type,!(f||null!=(i=t.extra)&&i.parenthesized)&&"AssignmentExpression"===r.type,n);if(!0!==y)if(!1!==y){var v,g=Array.isArray(y)?y:[y,"ParenthesizedExpression"===m],x=w(g,2),b=x[0],P=x[1],k="ArrayPattern"===t.type||"ObjectPattern"===t.type||"ParenthesizedExpression"===t.type?t:r,T=c([].concat(t[b]));try{for(T.s();!(v=T.n()).done;){var E=v.value;E&&this.checkLVal(E,{in:k,binding:n,checkClashes:o,allowingSloppyLetBinding:p,strictModeChanged:h,hasParenthesizedAncestor:P})}}catch(C){T.e(C)}finally{T.f()}}else{var A=n===qe?Z.InvalidLhs:Z.InvalidLhsBinding;this.raise(A,{at:t,ancestor:"UpdateExpression"===r.type?{type:"UpdateExpression",prefix:r.prefix}:{type:r.type}})}}else{this.checkIdentifier(t,n,h,p);var S=t.name;o&&(o.has(S)?this.raise(Z.ParamDupe,{at:t}):o.add(S))}else n!==qe&&this.raise(Z.InvalidPropertyBindingPattern,{at:t})}},{key:"checkIdentifier",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:!(e&Se);this.state.strict&&(i?le(t.name,this.inModule):oe(t.name))&&(e===qe?this.raise(Z.StrictEvalArguments,{at:t,referenceName:t.name}):this.raise(Z.StrictEvalArgumentsBinding,{at:t,bindingName:t.name})),r||"let"!==t.name||this.raise(Z.LetInLexicalBinding,{at:t}),e&qe||this.declareNameFromIdentifier(t,e)}},{key:"declareNameFromIdentifier",value:function(t,e){this.scope.declareName(t.name,e,t.loc.start)}},{key:"checkToRestConversion",value:function(t,e){switch(t.type){case"ParenthesizedExpression":this.checkToRestConversion(t.expression,e);break;case"Identifier":case"MemberExpression":break;case"ArrayExpression":case"ObjectExpression":if(e)break;default:this.raise(Z.InvalidRestAssignmentPattern,{at:t})}}},{key:"checkCommaAfterRest",value:function(t){return!!this.match(12)&&(this.raise(this.lookaheadCharCode()===t?Z.RestTrailingComma:Z.ElementAfterRest,{at:this.state.startLoc}),!0)}}]),i}(ur),ts=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"checkProto",value:function(t,e,i,r){if(!("SpreadElement"===t.type||this.isObjectMethod(t)||t.computed||t.shorthand)){var s=t.key,n="Identifier"===s.type?s.name:s.value;if("__proto__"===n){if(e)return void this.raise(Z.RecordNoProto,{at:s});i.used&&(r?null===r.doubleProtoLoc&&(r.doubleProtoLoc=s.loc.start):this.raise(Z.DuplicateProto,{at:s})),i.used=!0}}}},{key:"shouldExitDescending",value:function(t,e){return"ArrowFunctionExpression"===t.type&&t.start===e}},{key:"getExpression",value:function(){this.enterInitialScopes(),this.nextToken();var t=this.parseExpression();return this.match(137)||this.unexpected(),this.finalizeRemainingComments(),t.comments=this.state.comments,t.errors=this.state.errors,this.options.tokens&&(t.tokens=this.tokens),t}},{key:"parseExpression",value:function(t,e){var i=this;return t?this.disallowInAnd((function(){return i.parseExpressionBase(e)})):this.allowInAnd((function(){return i.parseExpressionBase(e)}))}},{key:"parseExpressionBase",value:function(t){var e=this.state.startLoc,i=this.parseMaybeAssign(t);if(this.match(12)){var r=this.startNodeAt(e);r.expressions=[i];while(this.eat(12))r.expressions.push(this.parseMaybeAssign(t));return this.toReferencedList(r.expressions),this.finishNode(r,"SequenceExpression")}return i}},{key:"parseMaybeAssignDisallowIn",value:function(t,e){var i=this;return this.disallowInAnd((function(){return i.parseMaybeAssign(t,e)}))}},{key:"parseMaybeAssignAllowIn",value:function(t,e){var i=this;return this.allowInAnd((function(){return i.parseMaybeAssign(t,e)}))}},{key:"setOptionalParametersError",value:function(t,e){var i;t.optionalParametersLoc=null!=(i=null==e?void 0:e.loc)?i:this.state.startLoc}},{key:"parseMaybeAssign",value:function(t,e){var i,r=this.state.startLoc;if(this.isContextual(106)&&this.prodParam.hasYield){var s=this.parseYield();return e&&(s=e.call(this,s,r)),s}t?i=!1:(t=new nr,i=!0);var n=this.state.type;(10===n||At(n))&&(this.state.potentialArrowAt=this.state.start);var a=this.parseMaybeConditional(t);if(e&&(a=e.call(this,a,r)),Ot(this.state.type)){var o=this.startNodeAt(r),l=this.state.value;if(o.operator=l,this.match(29)){this.toAssignable(a,!0),o.left=a;var c=r.index;null!=t.doubleProtoLoc&&t.doubleProtoLoc.index>=c&&(t.doubleProtoLoc=null),null!=t.shorthandAssignLoc&&t.shorthandAssignLoc.index>=c&&(t.shorthandAssignLoc=null),null!=t.privateKeyLoc&&t.privateKeyLoc.index>=c&&(this.checkDestructuringPrivate(t),t.privateKeyLoc=null)}else o.left=a;return this.next(),o.right=this.parseMaybeAssign(),this.checkLVal(a,{in:this.finishNode(o,"AssignmentExpression")}),o}return i&&this.checkExpressionErrors(t,!0),a}},{key:"parseMaybeConditional",value:function(t){var e=this.state.startLoc,i=this.state.potentialArrowAt,r=this.parseExprOps(t);return this.shouldExitDescending(r,i)?r:this.parseConditional(r,e,t)}},{key:"parseConditional",value:function(t,e,i){if(this.eat(17)){var r=this.startNodeAt(e);return r.test=t,r.consequent=this.parseMaybeAssignAllowIn(),this.expect(14),r.alternate=this.parseMaybeAssign(),this.finishNode(r,"ConditionalExpression")}return t}},{key:"parseMaybeUnaryOrPrivate",value:function(t){return this.match(136)?this.parsePrivateName():this.parseMaybeUnary(t)}},{key:"parseExprOps",value:function(t){var e=this.state.startLoc,i=this.state.potentialArrowAt,r=this.parseMaybeUnaryOrPrivate(t);return this.shouldExitDescending(r,i)?r:this.parseExprOp(r,e,-1)}},{key:"parseExprOp",value:function(t,e,i){if(this.isPrivateName(t)){var r=this.getPrivateNameSV(t);(i>=Ht(58)||!this.prodParam.hasIn||!this.match(58))&&this.raise(Z.PrivateInExpectedIn,{at:t,identifierName:r}),this.classScope.usePrivateName(r,t.loc.start)}var s=this.state.type;if(_t(s)&&(this.prodParam.hasIn||!this.match(58))){var n=Ht(s);if(n>i){if(39===s){if(this.expectPlugin("pipelineOperator"),this.state.inFSharpPipelineDirectBody)return t;this.checkPipelineAtInfixOperator(t,e)}var a=this.startNodeAt(e);a.left=t,a.operator=this.state.value;var o=41===s||42===s,l=40===s;if(l&&(n=Ht(42)),this.next(),39===s&&this.hasPlugin(["pipelineOperator",{proposal:"minimal"}])&&96===this.state.type&&this.prodParam.hasAwait)throw this.raise(Z.UnexpectedAwaitAfterPipelineBody,{at:this.state.startLoc});a.right=this.parseExprOpRightExpr(s,n);var c=this.finishNode(a,o||l?"LogicalExpression":"BinaryExpression"),h=this.state.type;if(l&&(41===h||42===h)||o&&40===h)throw this.raise(Z.MixingCoalesceWithLogical,{at:this.state.startLoc});return this.parseExprOp(c,e,i)}}return t}},{key:"parseExprOpRightExpr",value:function(t,e){var i=this,r=this.state.startLoc;switch(t){case 39:switch(this.getPluginOption("pipelineOperator","proposal")){case"hack":return this.withTopicBindingContext((function(){return i.parseHackPipeBody()}));case"smart":return this.withTopicBindingContext((function(){if(i.prodParam.hasYield&&i.isContextual(106))throw i.raise(Z.PipeBodyIsTighter,{at:i.state.startLoc});return i.parseSmartPipelineBodyInStyle(i.parseExprOpBaseRightExpr(t,e),r)}));case"fsharp":return this.withSoloAwaitPermittingContext((function(){return i.parseFSharpPipelineBody(e)}))}default:return this.parseExprOpBaseRightExpr(t,e)}}},{key:"parseExprOpBaseRightExpr",value:function(t,e){var i=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnaryOrPrivate(),i,Vt(t)?e-1:e)}},{key:"parseHackPipeBody",value:function(){var t,e=this.state.startLoc,i=this.parseMaybeAssign(),r=W.has(i.type);return!r||null!=(t=i.extra)&&t.parenthesized||this.raise(Z.PipeUnparenthesizedBody,{at:e,type:i.type}),this.topicReferenceWasUsedInCurrentContext()||this.raise(Z.PipeTopicUnused,{at:e}),i}},{key:"checkExponentialAfterUnary",value:function(t){this.match(57)&&this.raise(Z.UnexpectedTokenUnaryExponentiation,{at:t.argument})}},{key:"parseMaybeUnary",value:function(t,e){var i=this.state.startLoc,r=this.isContextual(96);if(r&&this.isAwaitAllowed()){this.next();var s=this.parseAwait(i);return e||this.checkExponentialAfterUnary(s),s}var n=this.match(34),a=this.startNode();if(jt(this.state.type)){a.operator=this.state.value,a.prefix=!0,this.match(72)&&this.expectPlugin("throwExpressions");var o=this.match(89);if(this.next(),a.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(t,!0),this.state.strict&&o){var l=a.argument;"Identifier"===l.type?this.raise(Z.StrictDelete,{at:a}):this.hasPropertyAsPrivateName(l)&&this.raise(Z.DeletePrivateField,{at:a})}if(!n)return e||this.checkExponentialAfterUnary(a),this.finishNode(a,"UnaryExpression")}var c=this.parseUpdate(a,n,t);if(r){var h=this.state.type,u=this.hasPlugin("v8intrinsic")?Dt(h):Dt(h)&&!this.match(54);if(u&&!this.isAmbiguousAwait())return this.raiseOverwrite(Z.AwaitNotInAsyncContext,{at:i}),this.parseAwait(i)}return c}},{key:"parseUpdate",value:function(t,e,i){if(e){var r=t;return this.checkLVal(r.argument,{in:this.finishNode(r,"UpdateExpression")}),t}var s=this.state.startLoc,n=this.parseExprSubscripts(i);if(this.checkExpressionErrors(i,!1))return n;while(Bt(this.state.type)&&!this.canInsertSemicolon()){var a=this.startNodeAt(s);a.operator=this.state.value,a.prefix=!1,a.argument=n,this.next(),this.checkLVal(n,{in:n=this.finishNode(a,"UpdateExpression")})}return n}},{key:"parseExprSubscripts",value:function(t){var e=this.state.startLoc,i=this.state.potentialArrowAt,r=this.parseExprAtom(t);return this.shouldExitDescending(r,i)?r:this.parseSubscripts(r,e)}},{key:"parseSubscripts",value:function(t,e,i){var r={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(t),stop:!1};do{t=this.parseSubscript(t,e,i,r),r.maybeAsyncArrow=!1}while(!r.stop);return t}},{key:"parseSubscript",value:function(t,e,i,r){var s=this.state.type;if(!i&&15===s)return this.parseBind(t,e,i,r);if(qt(s))return this.parseTaggedTemplateExpression(t,e,r);var n=!1;if(18===s){if(i&&40===this.lookaheadCharCode())return r.stop=!0,t;r.optionalChainMember=n=!0,this.next()}if(!i&&this.match(10))return this.parseCoverCallAndAsyncArrowHead(t,e,r,n);var a=this.eat(0);return a||n||this.eat(16)?this.parseMember(t,e,r,a,n):(r.stop=!0,t)}},{key:"parseMember",value:function(t,e,i,r,s){var n=this.startNodeAt(e);return n.object=t,n.computed=r,r?(n.property=this.parseExpression(),this.expect(3)):this.match(136)?("Super"===t.type&&this.raise(Z.SuperPrivateField,{at:e}),this.classScope.usePrivateName(this.state.value,this.state.startLoc),n.property=this.parsePrivateName()):n.property=this.parseIdentifier(!0),i.optionalChainMember?(n.optional=s,this.finishNode(n,"OptionalMemberExpression")):this.finishNode(n,"MemberExpression")}},{key:"parseBind",value:function(t,e,i,r){var s=this.startNodeAt(e);return s.object=t,this.next(),s.callee=this.parseNoCallExpr(),r.stop=!0,this.parseSubscripts(this.finishNode(s,"BindExpression"),e,i)}},{key:"parseCoverCallAndAsyncArrowHead",value:function(t,e,i,r){var s=this.state.maybeInArrowParameters,n=null;this.state.maybeInArrowParameters=!0,this.next();var a=this.startNodeAt(e);a.callee=t;var o=i.maybeAsyncArrow,l=i.optionalChainMember;o&&(this.expressionScope.enter(Yi()),n=new nr),l&&(a.optional=r),a.arguments=r?this.parseCallExpressionArguments(11):this.parseCallExpressionArguments(11,"Import"===t.type,"Super"!==t.type,a,n);var c=this.finishCallExpression(a,l);return o&&this.shouldParseAsyncArrow()&&!r?(i.stop=!0,this.checkDestructuringPrivate(n),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),c=this.parseAsyncArrowFromCallExpression(this.startNodeAt(e),c)):(o&&(this.checkExpressionErrors(n,!0),this.expressionScope.exit()),this.toReferencedArguments(c)),this.state.maybeInArrowParameters=s,c}},{key:"toReferencedArguments",value:function(t,e){this.toReferencedListDeep(t.arguments,e)}},{key:"parseTaggedTemplateExpression",value:function(t,e,i){var r=this.startNodeAt(e);return r.tag=t,r.quasi=this.parseTemplate(!0),i.optionalChainMember&&this.raise(Z.OptionalChainingNoTemplate,{at:e}),this.finishNode(r,"TaggedTemplateExpression")}},{key:"atPossibleAsyncArrow",value:function(t){return"Identifier"===t.type&&"async"===t.name&&this.state.lastTokEndLoc.index===t.end&&!this.canInsertSemicolon()&&t.end-t.start===5&&t.start===this.state.potentialArrowAt}},{key:"finishCallExpression",value:function(t,e){if("Import"===t.callee.type)if(2===t.arguments.length&&(this.hasPlugin("moduleAttributes")||this.expectPlugin("importAssertions")),0===t.arguments.length||t.arguments.length>2)this.raise(Z.ImportCallArity,{at:t,maxArgumentCount:this.hasPlugin("importAssertions")||this.hasPlugin("moduleAttributes")?2:1});else{var i,r=c(t.arguments);try{for(r.s();!(i=r.n()).done;){var s=i.value;"SpreadElement"===s.type&&this.raise(Z.ImportCallSpreadArgument,{at:s})}}catch(n){r.e(n)}finally{r.f()}}return this.finishNode(t,e?"OptionalCallExpression":"CallExpression")}},{key:"parseCallExpressionArguments",value:function(t,e,i,r,s){var n=[],a=!0,o=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;while(!this.eat(t)){if(a)a=!1;else if(this.expect(12),this.match(t)){!e||this.hasPlugin("importAssertions")||this.hasPlugin("moduleAttributes")||this.raise(Z.ImportCallArgumentTrailingComma,{at:this.state.lastTokStartLoc}),r&&this.addTrailingCommaExtraToNode(r),this.next();break}n.push(this.parseExprListItem(!1,s,i))}return this.state.inFSharpPipelineDirectBody=o,n}},{key:"shouldParseAsyncArrow",value:function(){return this.match(19)&&!this.canInsertSemicolon()}},{key:"parseAsyncArrowFromCallExpression",value:function(t,e){var i;return this.resetPreviousNodeTrailingComments(e),this.expect(19),this.parseArrowExpression(t,e.arguments,!0,null==(i=e.extra)?void 0:i.trailingCommaLoc),e.innerComments&&pi(t,e.innerComments),e.callee.trailingComments&&pi(t,e.callee.trailingComments),t}},{key:"parseNoCallExpr",value:function(){var t=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),t,!0)}},{key:"parseExprAtom",value:function(t){var e,i=null,r=this.state.type;switch(r){case 79:return this.parseSuper();case 83:return e=this.startNode(),this.next(),this.match(16)?this.parseImportMetaProperty(e):(this.match(10)||this.raise(Z.UnsupportedImport,{at:this.state.lastTokStartLoc}),this.finishNode(e,"Import"));case 78:return e=this.startNode(),this.next(),this.finishNode(e,"ThisExpression");case 90:return this.parseDo(this.startNode(),!1);case 56:case 31:return this.readRegexp(),this.parseRegExpLiteral(this.state.value);case 132:return this.parseNumericLiteral(this.state.value);case 133:return this.parseBigIntLiteral(this.state.value);case 134:return this.parseDecimalLiteral(this.state.value);case 131:return this.parseStringLiteral(this.state.value);case 84:return this.parseNullLiteral();case 85:return this.parseBooleanLiteral(!0);case 86:return this.parseBooleanLiteral(!1);case 10:var s=this.state.potentialArrowAt===this.state.start;return this.parseParenAndDistinguishExpression(s);case 2:case 1:return this.parseArrayLike(2===this.state.type?4:3,!1,!0);case 0:return this.parseArrayLike(3,!0,!1,t);case 6:case 7:return this.parseObjectLike(6===this.state.type?9:8,!1,!0);case 5:return this.parseObjectLike(8,!1,!1,t);case 68:return this.parseFunctionOrFunctionSent();case 26:i=this.parseDecorators();case 80:return this.parseClass(this.maybeTakeDecorators(i,this.startNode()),!1);case 77:return this.parseNewOrNewTarget();case 25:case 24:return this.parseTemplate(!1);case 15:e=this.startNode(),this.next(),e.object=null;var n=e.callee=this.parseNoCallExpr();if("MemberExpression"===n.type)return this.finishNode(e,"BindExpression");throw this.raise(Z.UnsupportedBind,{at:n});case 136:return this.raise(Z.PrivateInExpectedIn,{at:this.state.startLoc,identifierName:this.state.value}),this.parsePrivateName();case 33:return this.parseTopicReferenceThenEqualsSign(54,"%");case 32:return this.parseTopicReferenceThenEqualsSign(44,"^");case 37:case 38:return this.parseTopicReference("hack");case 44:case 54:case 27:var a=this.getPluginOption("pipelineOperator","proposal");if(a)return this.parseTopicReference(a);throw this.unexpected();case 47:var o=this.input.codePointAt(this.nextTokenStart());if(Zt(o)||62===o){this.expectOnePlugin(["jsx","flow","typescript"]);break}throw this.unexpected();default:if(At(r)){if(this.isContextual(125)&&123===this.lookaheadCharCode()&&!this.hasFollowingLineBreak())return this.parseModuleExpression();var l=this.state.potentialArrowAt===this.state.start,c=this.state.containsEsc,h=this.parseIdentifier();if(!c&&"async"===h.name&&!this.canInsertSemicolon()){var u=this.state.type;if(68===u)return this.resetPreviousNodeTrailingComments(h),this.next(),this.parseAsyncFunctionExpression(this.startNodeAtNode(h));if(At(u))return 61===this.lookaheadCharCode()?this.parseAsyncArrowUnaryFunction(this.startNodeAtNode(h)):h;if(90===u)return this.resetPreviousNodeTrailingComments(h),this.parseDo(this.startNodeAtNode(h),!0)}return l&&this.match(19)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(this.startNodeAtNode(h),[h],!1)):h}throw this.unexpected()}}},{key:"parseTopicReferenceThenEqualsSign",value:function(t,e){var i=this.getPluginOption("pipelineOperator","proposal");if(i)return this.state.type=t,this.state.value=e,this.state.pos--,this.state.end--,this.state.endLoc=j(this.state.endLoc,-1),this.parseTopicReference(i);throw this.unexpected()}},{key:"parseTopicReference",value:function(t){var e=this.startNode(),i=this.state.startLoc,r=this.state.type;return this.next(),this.finishTopicReference(e,i,t,r)}},{key:"finishTopicReference",value:function(t,e,i,r){if(this.testTopicReferenceConfiguration(i,e,r)){var s="smart"===i?"PipelinePrimaryTopicReference":"TopicReference";return this.topicReferenceIsAllowedInCurrentContext()||this.raise("smart"===i?Z.PrimaryTopicNotAllowed:Z.PipeTopicUnbound,{at:e}),this.registerTopicReference(),this.finishNode(t,s)}throw this.raise(Z.PipeTopicUnconfiguredToken,{at:e,token:zt(r)})}},{key:"testTopicReferenceConfiguration",value:function(t,e,i){switch(t){case"hack":return this.hasPlugin(["pipelineOperator",{topicToken:zt(i)}]);case"smart":return 27===i;default:throw this.raise(Z.PipeTopicRequiresHackPipes,{at:e})}}},{key:"parseAsyncArrowUnaryFunction",value:function(t){this.prodParam.enter(rr(!0,this.prodParam.hasYield));var e=[this.parseIdentifier()];return this.prodParam.exit(),this.hasPrecedingLineBreak()&&this.raise(Z.LineTerminatorBeforeArrow,{at:this.state.curPosition()}),this.expect(19),this.parseArrowExpression(t,e,!0)}},{key:"parseDo",value:function(t,e){this.expectPlugin("doExpressions"),e&&this.expectPlugin("asyncDoExpressions"),t.async=e,this.next();var i=this.state.labels;return this.state.labels=[],e?(this.prodParam.enter(Zi),t.body=this.parseBlock(),this.prodParam.exit()):t.body=this.parseBlock(),this.state.labels=i,this.finishNode(t,"DoExpression")}},{key:"parseSuper",value:function(){var t=this.startNode();return this.next(),!this.match(10)||this.scope.allowDirectSuper||this.options.allowSuperOutsideMethod?this.scope.allowSuper||this.options.allowSuperOutsideMethod||this.raise(Z.UnexpectedSuper,{at:t}):this.raise(Z.SuperNotAllowed,{at:t}),this.match(10)||this.match(0)||this.match(16)||this.raise(Z.UnsupportedSuper,{at:t}),this.finishNode(t,"Super")}},{key:"parsePrivateName",value:function(){var t=this.startNode(),e=this.startNodeAt(j(this.state.startLoc,1)),i=this.state.value;return this.next(),t.id=this.createIdentifier(e,i),this.finishNode(t,"PrivateName")}},{key:"parseFunctionOrFunctionSent",value:function(){var t=this.startNode();if(this.next(),this.prodParam.hasYield&&this.match(16)){var e=this.createIdentifier(this.startNodeAtNode(t),"function");return this.next(),this.match(102)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected(),this.parseMetaProperty(t,e,"sent")}return this.parseFunction(t)}},{key:"parseMetaProperty",value:function(t,e,i){t.meta=e;var r=this.state.containsEsc;return t.property=this.parseIdentifier(!0),(t.property.name!==i||r)&&this.raise(Z.UnsupportedMetaProperty,{at:t.property,target:e.name,onlyValidPropertyName:i}),this.finishNode(t,"MetaProperty")}},{key:"parseImportMetaProperty",value:function(t){var e=this.createIdentifier(this.startNodeAtNode(t),"import");return this.next(),this.isContextual(100)&&(this.inModule||this.raise(Z.ImportMetaOutsideModule,{at:e}),this.sawUnambiguousESM=!0),this.parseMetaProperty(t,e,"meta")}},{key:"parseLiteralAtNode",value:function(t,e,i){return this.addExtra(i,"rawValue",t),this.addExtra(i,"raw",this.input.slice(i.start,this.state.end)),i.value=t,this.next(),this.finishNode(i,e)}},{key:"parseLiteral",value:function(t,e){var i=this.startNode();return this.parseLiteralAtNode(t,e,i)}},{key:"parseStringLiteral",value:function(t){return this.parseLiteral(t,"StringLiteral")}},{key:"parseNumericLiteral",value:function(t){return this.parseLiteral(t,"NumericLiteral")}},{key:"parseBigIntLiteral",value:function(t){return this.parseLiteral(t,"BigIntLiteral")}},{key:"parseDecimalLiteral",value:function(t){return this.parseLiteral(t,"DecimalLiteral")}},{key:"parseRegExpLiteral",value:function(t){var e=this.parseLiteral(t.value,"RegExpLiteral");return e.pattern=t.pattern,e.flags=t.flags,e}},{key:"parseBooleanLiteral",value:function(t){var e=this.startNode();return e.value=t,this.next(),this.finishNode(e,"BooleanLiteral")}},{key:"parseNullLiteral",value:function(){var t=this.startNode();return this.next(),this.finishNode(t,"NullLiteral")}},{key:"parseParenAndDistinguishExpression",value:function(t){var e,i=this.state.startLoc;this.next(),this.expressionScope.enter(Ji());var r=this.state.maybeInArrowParameters,s=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.inFSharpPipelineDirectBody=!1;var n,a,o=this.state.startLoc,l=[],c=new nr,h=!0;while(!this.match(11)){if(h)h=!1;else if(this.expect(12,null===c.optionalParametersLoc?null:c.optionalParametersLoc),this.match(11)){a=this.state.startLoc;break}if(this.match(21)){var u=this.state.startLoc;if(n=this.state.startLoc,l.push(this.parseParenItem(this.parseRestBinding(),u)),!this.checkCommaAfterRest(41))break}else l.push(this.parseMaybeAssignAllowIn(c,this.parseParenItem))}var p=this.state.lastTokEndLoc;this.expect(11),this.state.maybeInArrowParameters=r,this.state.inFSharpPipelineDirectBody=s;var d=this.startNodeAt(i);return t&&this.shouldParseArrow(l)&&(d=this.parseArrow(d))?(this.checkDestructuringPrivate(c),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),this.parseArrowExpression(d,l,!1),d):(this.expressionScope.exit(),l.length||this.unexpected(this.state.lastTokStartLoc),a&&this.unexpected(a),n&&this.unexpected(n),this.checkExpressionErrors(c,!0),this.toReferencedListDeep(l,!0),l.length>1?(e=this.startNodeAt(o),e.expressions=l,this.finishNode(e,"SequenceExpression"),this.resetEndLocation(e,p)):e=l[0],this.wrapParenthesis(i,e))}},{key:"wrapParenthesis",value:function(t,e){if(!this.options.createParenthesizedExpressions)return this.addExtra(e,"parenthesized",!0),this.addExtra(e,"parenStart",t.index),this.takeSurroundingComments(e,t.index,this.state.lastTokEndLoc.index),e;var i=this.startNodeAt(t);return i.expression=e,this.finishNode(i,"ParenthesizedExpression")}},{key:"shouldParseArrow",value:function(t){return!this.canInsertSemicolon()}},{key:"parseArrow",value:function(t){if(this.eat(19))return t}},{key:"parseParenItem",value:function(t,e){return t}},{key:"parseNewOrNewTarget",value:function(){var t=this.startNode();if(this.next(),this.match(16)){var e=this.createIdentifier(this.startNodeAtNode(t),"new");this.next();var i=this.parseMetaProperty(t,e,"target");return this.scope.inNonArrowFunction||this.scope.inClass||this.raise(Z.UnexpectedNewTarget,{at:i}),i}return this.parseNew(t)}},{key:"parseNew",value:function(t){if(this.parseNewCallee(t),this.eat(10)){var e=this.parseExprList(11);this.toReferencedList(e),t.arguments=e}else t.arguments=[];return this.finishNode(t,"NewExpression")}},{key:"parseNewCallee",value:function(t){t.callee=this.parseNoCallExpr(),"Import"===t.callee.type?this.raise(Z.ImportCallNotNewExpression,{at:t.callee}):this.isOptionalChain(t.callee)?this.raise(Z.OptionalChainingNoNew,{at:this.state.lastTokEndLoc}):this.eat(18)&&this.raise(Z.OptionalChainingNoNew,{at:this.state.startLoc})}},{key:"parseTemplateElement",value:function(t){var e=this.state,i=e.start,r=e.startLoc,s=e.end,n=e.value,a=i+1,o=this.startNodeAt(j(r,1));null===n&&(t||this.raise(Z.InvalidEscapeSequenceTemplate,{at:j(this.state.firstInvalidTemplateEscapePos,1)}));var l=this.match(24),c=l?-1:-2,h=s+c;o.value={raw:this.input.slice(a,h).replace(/\r\n?/g,"\n"),cooked:null===n?null:n.slice(1,c)},o.tail=l,this.next();var u=this.finishNode(o,"TemplateElement");return this.resetEndLocation(u,j(this.state.lastTokEndLoc,c)),u}},{key:"parseTemplate",value:function(t){var e=this.startNode();e.expressions=[];var i=this.parseTemplateElement(t);e.quasis=[i];while(!i.tail)e.expressions.push(this.parseTemplateSubstitution()),this.readTemplateContinuation(),e.quasis.push(i=this.parseTemplateElement(t));return this.finishNode(e,"TemplateLiteral")}},{key:"parseTemplateSubstitution",value:function(){return this.parseExpression()}},{key:"parseObjectLike",value:function(t,e,i,r){i&&this.expectPlugin("recordAndTuple");var s=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;var n=Object.create(null),a=!0,o=this.startNode();o.properties=[],this.next();while(!this.match(t)){if(a)a=!1;else if(this.expect(12),this.match(t)){this.addTrailingCommaExtraToNode(o);break}var l=void 0;e?l=this.parseBindingProperty():(l=this.parsePropertyDefinition(r),this.checkProto(l,i,n,r)),i&&!this.isObjectProperty(l)&&"SpreadElement"!==l.type&&this.raise(Z.InvalidRecordProperty,{at:l}),l.shorthand&&this.addExtra(l,"shorthand",!0),o.properties.push(l)}this.next(),this.state.inFSharpPipelineDirectBody=s;var c="ObjectExpression";return e?c="ObjectPattern":i&&(c="RecordExpression"),this.finishNode(o,c)}},{key:"addTrailingCommaExtraToNode",value:function(t){this.addExtra(t,"trailingComma",this.state.lastTokStart),this.addExtra(t,"trailingCommaLoc",this.state.lastTokStartLoc,!1)}},{key:"maybeAsyncOrAccessorProp",value:function(t){return!t.computed&&"Identifier"===t.key.type&&(this.isLiteralPropertyName()||this.match(0)||this.match(55))}},{key:"parsePropertyDefinition",value:function(t){var e=[];if(this.match(26)){this.hasPlugin("decorators")&&this.raise(Z.UnsupportedPropertyDecorator,{at:this.state.startLoc});while(this.match(26))e.push(this.parseDecorator())}var i,r=this.startNode(),s=!1,n=!1;if(this.match(21))return e.length&&this.unexpected(),this.parseSpread();e.length&&(r.decorators=e,e=[]),r.method=!1,t&&(i=this.state.startLoc);var a=this.eat(55);this.parsePropertyNamePrefixOperator(r);var o=this.state.containsEsc,l=this.parsePropertyName(r,t);if(!a&&!o&&this.maybeAsyncOrAccessorProp(r)){var c=l.name;"async"!==c||this.hasPrecedingLineBreak()||(s=!0,this.resetPreviousNodeTrailingComments(l),a=this.eat(55),this.parsePropertyName(r)),"get"!==c&&"set"!==c||(n=!0,this.resetPreviousNodeTrailingComments(l),r.kind=c,this.match(55)&&(a=!0,this.raise(Z.AccessorIsGenerator,{at:this.state.curPosition(),kind:c}),this.next()),this.parsePropertyName(r))}return this.parseObjPropValue(r,i,a,s,!1,n,t)}},{key:"getGetterSetterExpectedParamCount",value:function(t){return"get"===t.kind?0:1}},{key:"getObjectOrClassMethodParams",value:function(t){return t.params}},{key:"checkGetterSetterParams",value:function(t){var e,i=this.getGetterSetterExpectedParamCount(t),r=this.getObjectOrClassMethodParams(t);r.length!==i&&this.raise("get"===t.kind?Z.BadGetterArity:Z.BadSetterArity,{at:t}),"set"===t.kind&&"RestElement"===(null==(e=r[r.length-1])?void 0:e.type)&&this.raise(Z.BadSetterRestParameter,{at:t})}},{key:"parseObjectMethod",value:function(t,e,i,r,s){if(s){var n=this.parseMethod(t,e,!1,!1,!1,"ObjectMethod");return this.checkGetterSetterParams(n),n}if(i||e||this.match(10))return r&&this.unexpected(),t.kind="method",t.method=!0,this.parseMethod(t,e,i,!1,!1,"ObjectMethod")}},{key:"parseObjectProperty",value:function(t,e,i,r){if(t.shorthand=!1,this.eat(14))return t.value=i?this.parseMaybeDefault(this.state.startLoc):this.parseMaybeAssignAllowIn(r),this.finishNode(t,"ObjectProperty");if(!t.computed&&"Identifier"===t.key.type){if(this.checkReservedWord(t.key.name,t.key.loc.start,!0,!1),i)t.value=this.parseMaybeDefault(e,cr(t.key));else if(this.match(29)){var s=this.state.startLoc;null!=r?null===r.shorthandAssignLoc&&(r.shorthandAssignLoc=s):this.raise(Z.InvalidCoverInitializedName,{at:s}),t.value=this.parseMaybeDefault(e,cr(t.key))}else t.value=cr(t.key);return t.shorthand=!0,this.finishNode(t,"ObjectProperty")}}},{key:"parseObjPropValue",value:function(t,e,i,r,s,n,a){var o=this.parseObjectMethod(t,i,r,s,n)||this.parseObjectProperty(t,e,s,a);return o||this.unexpected(),o}},{key:"parsePropertyName",value:function(t,e){if(this.eat(0))t.computed=!0,t.key=this.parseMaybeAssignAllowIn(),this.expect(3);else{var i,r=this.state,s=r.type,n=r.value;if(Ct(s))i=this.parseIdentifier(!0);else switch(s){case 132:i=this.parseNumericLiteral(n);break;case 131:i=this.parseStringLiteral(n);break;case 133:i=this.parseBigIntLiteral(n);break;case 134:i=this.parseDecimalLiteral(n);break;case 136:var a=this.state.startLoc;null!=e?null===e.privateKeyLoc&&(e.privateKeyLoc=a):this.raise(Z.UnexpectedPrivateField,{at:a}),i=this.parsePrivateName();break;default:throw this.unexpected()}t.key=i,136!==s&&(t.computed=!1)}return t.key}},{key:"initFunction",value:function(t,e){t.id=null,t.generator=!1,t.async=e}},{key:"parseMethod",value:function(t,e,i,r,s,n){var a=arguments.length>6&&void 0!==arguments[6]&&arguments[6];this.initFunction(t,i),t.generator=e;var o=r;this.scope.enter(me|ge|(a?be:0)|(s?xe:0)),this.prodParam.enter(rr(i,t.generator)),this.parseFunctionParams(t,o);var l=this.parseFunctionBodyAndFinish(t,n,!0);return this.prodParam.exit(),this.scope.exit(),l}},{key:"parseArrayLike",value:function(t,e,i,r){i&&this.expectPlugin("recordAndTuple");var s=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;var n=this.startNode();return this.next(),n.elements=this.parseExprList(t,!i,r,n),this.state.inFSharpPipelineDirectBody=s,this.finishNode(n,i?"TupleExpression":"ArrayExpression")}},{key:"parseArrowExpression",value:function(t,e,i,r){this.scope.enter(me|ye);var s=rr(i,!1);!this.match(5)&&this.prodParam.hasIn&&(s|=er),this.prodParam.enter(s),this.initFunction(t,i);var n=this.state.maybeInArrowParameters;return e&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(t,e,r)),this.state.maybeInArrowParameters=!1,this.parseFunctionBody(t,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=n,this.finishNode(t,"ArrowFunctionExpression")}},{key:"setArrowFunctionParameters",value:function(t,e,i){this.toAssignableList(e,i,!1),t.params=e}},{key:"parseFunctionBodyAndFinish",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.parseFunctionBody(t,!1,i),this.finishNode(t,e)}},{key:"parseFunctionBody",value:function(t,e){var i=this,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=e&&!this.match(5);if(this.expressionScope.enter(Gi()),s)t.body=this.parseMaybeAssign(),this.checkParams(t,!1,e,!1);else{var n=this.state.strict,a=this.state.labels;this.state.labels=[],this.prodParam.enter(this.prodParam.currentFlags()|tr),t.body=this.parseBlock(!0,!1,(function(s){var a=!i.isSimpleParamList(t.params);s&&a&&i.raise(Z.IllegalLanguageModeDirective,{at:"method"!==t.kind&&"constructor"!==t.kind||!t.key?t:t.key.loc.end});var o=!n&&i.state.strict;i.checkParams(t,!i.state.strict&&!e&&!r&&!a,e,o),i.state.strict&&t.id&&i.checkIdentifier(t.id,Ke,o)})),this.prodParam.exit(),this.state.labels=a}this.expressionScope.exit()}},{key:"isSimpleParameter",value:function(t){return"Identifier"===t.type}},{key:"isSimpleParamList",value:function(t){for(var e=0,i=t.length;e<i;e++)if(!this.isSimpleParameter(t[e]))return!1;return!0}},{key:"checkParams",value:function(t,e,i){var r,s=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],n=!e&&new Set,a={type:"FormalParameters"},o=c(t.params);try{for(o.s();!(r=o.n()).done;){var l=r.value;this.checkLVal(l,{in:a,binding:je,checkClashes:n,strictModeChanged:s})}}catch(h){o.e(h)}finally{o.f()}}},{key:"parseExprList",value:function(t,e,i,r){var s=[],n=!0;while(!this.eat(t)){if(n)n=!1;else if(this.expect(12),this.match(t)){r&&this.addTrailingCommaExtraToNode(r),this.next();break}s.push(this.parseExprListItem(e,i))}return s}},{key:"parseExprListItem",value:function(t,e,i){var r;if(this.match(12))t||this.raise(Z.UnexpectedToken,{at:this.state.curPosition(),unexpected:","}),r=null;else if(this.match(21)){var s=this.state.startLoc;r=this.parseParenItem(this.parseSpread(e),s)}else if(this.match(17)){this.expectPlugin("partialApplication"),i||this.raise(Z.UnexpectedArgumentPlaceholder,{at:this.state.startLoc});var n=this.startNode();this.next(),r=this.finishNode(n,"ArgumentPlaceholder")}else r=this.parseMaybeAssignAllowIn(e,this.parseParenItem);return r}},{key:"parseIdentifier",value:function(t){var e=this.startNode(),i=this.parseIdentifierName(t);return this.createIdentifier(e,i)}},{key:"createIdentifier",value:function(t,e){return t.name=e,t.loc.identifierName=e,this.finishNode(t,"Identifier")}},{key:"parseIdentifierName",value:function(t){var e,i=this.state,r=i.startLoc,s=i.type;if(!Ct(s))throw this.unexpected();e=this.state.value;var n=St(s);return t?n&&this.replaceToken(130):this.checkReservedWord(e,r,n,!1),this.next(),e}},{key:"checkReservedWord",value:function(t,e,i,r){if(!(t.length>10)&&pe(t)){if("yield"===t){if(this.prodParam.hasYield)return void this.raise(Z.YieldBindingIdentifier,{at:e})}else if("await"===t){if(this.prodParam.hasAwait)return void this.raise(Z.AwaitBindingIdentifier,{at:e});if(this.scope.inStaticBlock)return void this.raise(Z.AwaitBindingIdentifierInStaticBlock,{at:e});this.expressionScope.recordAsyncArrowParametersError({at:e})}else if("arguments"===t&&this.scope.inClassAndNotInNonArrowFunction)return void this.raise(Z.ArgumentsInClass,{at:e});if(i&&ce(t))this.raise(Z.UnexpectedKeyword,{at:e,keyword:t});else{var s=this.state.strict?r?le:ae:ne;s(t,this.inModule)&&this.raise(Z.UnexpectedReservedWord,{at:e,reservedWord:t})}}}},{key:"isAwaitAllowed",value:function(){return!!this.prodParam.hasAwait||!(!this.options.allowAwaitOutsideFunction||this.scope.inFunction)}},{key:"parseAwait",value:function(t){var e=this.startNodeAt(t);return this.expressionScope.recordParameterInitializerError(Z.AwaitExpressionFormalParameter,{at:e}),this.eat(55)&&this.raise(Z.ObsoleteAwaitStar,{at:e}),this.scope.inFunction||this.options.allowAwaitOutsideFunction||(this.isAmbiguousAwait()?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(e.argument=this.parseMaybeUnary(null,!0)),this.finishNode(e,"AwaitExpression")}},{key:"isAmbiguousAwait",value:function(){if(this.hasPrecedingLineBreak())return!0;var t=this.state.type;return 53===t||10===t||0===t||qt(t)||101===t&&!this.state.containsEsc||135===t||56===t||this.hasPlugin("v8intrinsic")&&54===t}},{key:"parseYield",value:function(){var t=this.startNode();this.expressionScope.recordParameterInitializerError(Z.YieldInParameter,{at:t}),this.next();var e=!1,i=null;if(!this.hasPrecedingLineBreak())switch(e=this.eat(55),this.state.type){case 13:case 137:case 8:case 11:case 3:case 9:case 14:case 12:if(!e)break;default:i=this.parseMaybeAssign()}return t.delegate=e,t.argument=i,this.finishNode(t,"YieldExpression")}},{key:"checkPipelineAtInfixOperator",value:function(t,e){this.hasPlugin(["pipelineOperator",{proposal:"smart"}])&&"SequenceExpression"===t.type&&this.raise(Z.PipelineHeadSequenceExpression,{at:e})}},{key:"parseSmartPipelineBodyInStyle",value:function(t,e){if(this.isSimpleReference(t)){var i=this.startNodeAt(e);return i.callee=t,this.finishNode(i,"PipelineBareFunction")}var r=this.startNodeAt(e);return this.checkSmartPipeTopicBodyEarlyErrors(e),r.expression=t,this.finishNode(r,"PipelineTopicExpression")}},{key:"isSimpleReference",value:function(t){switch(t.type){case"MemberExpression":return!t.computed&&this.isSimpleReference(t.object);case"Identifier":return!0;default:return!1}}},{key:"checkSmartPipeTopicBodyEarlyErrors",value:function(t){if(this.match(19))throw this.raise(Z.PipelineBodyNoArrow,{at:this.state.startLoc});this.topicReferenceWasUsedInCurrentContext()||this.raise(Z.PipelineTopicUnused,{at:t})}},{key:"withTopicBindingContext",value:function(t){var e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}},{key:"withSmartMixTopicForbiddingContext",value:function(t){if(!this.hasPlugin(["pipelineOperator",{proposal:"smart"}]))return t();var e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}},{key:"withSoloAwaitPermittingContext",value:function(t){var e=this.state.soloAwait;this.state.soloAwait=!0;try{return t()}finally{this.state.soloAwait=e}}},{key:"allowInAnd",value:function(t){var e=this.prodParam.currentFlags(),i=er&~e;if(i){this.prodParam.enter(e|er);try{return t()}finally{this.prodParam.exit()}}return t()}},{key:"disallowInAnd",value:function(t){var e=this.prodParam.currentFlags(),i=er&e;if(i){this.prodParam.enter(e&~er);try{return t()}finally{this.prodParam.exit()}}return t()}},{key:"registerTopicReference",value:function(){this.state.topicContext.maxTopicIndex=0}},{key:"topicReferenceIsAllowedInCurrentContext",value:function(){return this.state.topicContext.maxNumOfResolvableTopics>=1}},{key:"topicReferenceWasUsedInCurrentContext",value:function(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}},{key:"parseFSharpPipelineBody",value:function(t){var e=this.state.startLoc;this.state.potentialArrowAt=this.state.start;var i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;var r=this.parseExprOp(this.parseMaybeUnaryOrPrivate(),e,t);return this.state.inFSharpPipelineDirectBody=i,r}},{key:"parseModuleExpression",value:function(){this.expectPlugin("moduleBlocks");var t=this.startNode();this.next(),this.match(5)||this.unexpected(null,5);var e=this.startNodeAt(this.state.endLoc);this.next();var i=this.initializeScopes(!0);this.enterInitialScopes();try{t.body=this.parseProgram(e,8,"module")}finally{i()}return this.finishNode(t,"ModuleExpression")}},{key:"parsePropertyNamePrefixOperator",value:function(t){}}]),i}(Zr),es={kind:"loop"},is={kind:"switch"},rs={Expression:0,Declaration:1,HangingDeclaration:2,NullableId:4,Async:8},ss={StatementOnly:0,AllowImportExport:1,AllowDeclaration:2,AllowFunctionDeclaration:4,AllowLabeledFunction:8},ns=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,as=new RegExp("in(?:stanceof)?","y");function os(t,e){for(var i=0;i<t.length;i++){var r=t[i],s=r.type;if("number"===typeof s){if(136===s){var n=r,a=n.loc,o=n.start,l=n.value,c=n.end,h=o+1,u=j(a.start,1);t.splice(i,1,new _i({type:Kt(27),value:"#",start:o,end:h,startLoc:a.start,endLoc:u}),new _i({type:Kt(130),value:l,start:h,end:c,startLoc:u,endLoc:a.end})),i++;continue}if(qt(s)){var p=r,d=p.loc,f=p.start,m=p.value,y=p.end,v=f+1,g=j(d.start,1),x=void 0;x=96===e.charCodeAt(f)?new _i({type:Kt(22),value:"`",start:f,end:v,startLoc:d.start,endLoc:g}):new _i({type:Kt(8),value:"}",start:f,end:v,startLoc:d.start,endLoc:g});var b=void 0,P=void 0,k=void 0,T=void 0;24===s?(P=y-1,k=j(d.end,-1),b=null===m?null:m.slice(1,-1),T=new _i({type:Kt(22),value:"`",start:P,end:y,startLoc:k,endLoc:d.end})):(P=y-2,k=j(d.end,-2),b=null===m?null:m.slice(1,-2),T=new _i({type:Kt(23),value:"${",start:P,end:y,startLoc:k,endLoc:d.end})),t.splice(i,1,x,new _i({type:Kt(20),value:b,start:v,end:P,startLoc:g,endLoc:k}),T),i+=2;continue}r.type=Kt(s)}}return t}var ls=function(t){y(i,t);var e=g(i);function i(){return L(this,i),e.apply(this,arguments)}return D(i,[{key:"parseTopLevel",value:function(t,e){return t.program=this.parseProgram(e),t.comments=this.state.comments,this.options.tokens&&(t.tokens=os(this.tokens,this.input)),this.finishNode(t,"File")}},{key:"parseProgram",value:function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:137,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.options.sourceType;if(t.sourceType=r,t.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(t,!0,!0,i),this.inModule&&!this.options.allowUndeclaredExports&&this.scope.undefinedExports.size>0)for(var s=0,n=Array.from(this.scope.undefinedExports);s<n.length;s++){var a=w(n[s],2),o=a[0],l=a[1];this.raise(Z.ModuleExportUndefined,{at:l,localName:o})}return e=137===i?this.finishNode(t,"Program"):this.finishNodeAt(t,"Program",j(this.state.startLoc,-1)),e}},{key:"stmtToDirective",value:function(t){var e=t;e.type="Directive",e.value=e.expression,delete e.expression;var i=e.value,r=i.value,s=this.input.slice(i.start,i.end),n=i.value=s.slice(1,-1);return this.addExtra(i,"raw",s),this.addExtra(i,"rawValue",n),this.addExtra(i,"expressionValue",r),i.type="DirectiveLiteral",e}},{key:"parseInterpreterDirective",value:function(){if(!this.match(28))return null;var t=this.startNode();return t.value=this.state.value,this.next(),this.finishNode(t,"InterpreterDirective")}},{key:"isLet",value:function(){return!!this.isContextual(99)&&this.hasFollowingBindingAtom()}},{key:"chStartsBindingIdentifier",value:function(t,e){if(Zt(t)){if(as.lastIndex=e,as.test(this.input)){var i=this.codePointAtPos(as.lastIndex);if(!te(i)&&92!==i)return!1}return!0}return 92===t}},{key:"chStartsBindingPattern",value:function(t){return 91===t||123===t}},{key:"hasFollowingBindingAtom",value:function(){var t=this.nextTokenStart(),e=this.codePointAtPos(t);return this.chStartsBindingPattern(e)||this.chStartsBindingIdentifier(e,t)}},{key:"hasFollowingBindingIdentifier",value:function(){var t=this.nextTokenStart(),e=this.codePointAtPos(t);return this.chStartsBindingIdentifier(e,t)}},{key:"startsUsingForOf",value:function(){var t=this.lookahead();return!(101===t.type&&!t.containsEsc)&&(this.expectPlugin("explicitResourceManagement"),!0)}},{key:"parseModuleItem",value:function(){return this.parseStatementLike(ss.AllowImportExport|ss.AllowDeclaration|ss.AllowFunctionDeclaration|ss.AllowLabeledFunction)}},{key:"parseStatementListItem",value:function(){return this.parseStatementLike(ss.AllowDeclaration|ss.AllowFunctionDeclaration|ss.AllowLabeledFunction)}},{key:"parseStatementOrFunctionDeclaration",value:function(t){return this.parseStatementLike(ss.AllowFunctionDeclaration|(t?0:ss.AllowLabeledFunction))}},{key:"parseStatement",value:function(){return this.parseStatementLike(ss.StatementOnly)}},{key:"parseStatementLike",value:function(t){var e=null;return this.match(26)&&(e=this.parseDecorators(!0)),this.parseStatementContent(t,e)}},{key:"parseStatementContent",value:function(t,e){var i=this.state.type,r=this.startNode(),s=!!(t&ss.AllowDeclaration),n=!!(t&ss.AllowFunctionDeclaration),a=t&ss.AllowImportExport;switch(i){case 60:return this.parseBreakContinueStatement(r,!0);case 63:return this.parseBreakContinueStatement(r,!1);case 64:return this.parseDebuggerStatement(r);case 90:return this.parseDoWhileStatement(r);case 91:return this.parseForStatement(r);case 68:if(46===this.lookaheadCharCode())break;return s||(this.state.strict?this.raise(Z.StrictFunction,{at:this.state.startLoc}):n||this.raise(Z.SloppyFunction,{at:this.state.startLoc})),this.parseFunctionStatement(r,!1,!s&&n);case 80:return s||this.unexpected(),this.parseClass(this.maybeTakeDecorators(e,r),!0);case 69:return this.parseIfStatement(r);case 70:return this.parseReturnStatement(r);case 71:return this.parseSwitchStatement(r);case 72:return this.parseThrowStatement(r);case 73:return this.parseTryStatement(r);case 105:if(this.hasFollowingLineBreak()||this.state.containsEsc||!this.hasFollowingBindingIdentifier())break;return this.expectPlugin("explicitResourceManagement"),!this.scope.inModule&&this.scope.inTopLevel?this.raise(Z.UnexpectedUsingDeclaration,{at:this.state.startLoc}):s||this.raise(Z.UnexpectedLexicalDeclaration,{at:this.state.startLoc}),this.parseVarStatement(r,"using");case 99:if(this.state.containsEsc)break;var o=this.nextTokenStart(),l=this.codePointAtPos(o);if(91!==l){if(!s&&this.hasFollowingLineBreak())break;if(!this.chStartsBindingIdentifier(l,o)&&123!==l)break}case 75:s||this.raise(Z.UnexpectedLexicalDeclaration,{at:this.state.startLoc});case 74:var c=this.state.value;return this.parseVarStatement(r,c);case 92:return this.parseWhileStatement(r);case 76:return this.parseWithStatement(r);case 5:return this.parseBlock();case 13:return this.parseEmptyStatement(r);case 83:var h=this.lookaheadCharCode();if(40===h||46===h)break;case 82:var u;return this.options.allowImportExportEverywhere||a||this.raise(Z.UnexpectedImportExport,{at:this.state.startLoc}),this.next(),83===i?(u=this.parseImport(r),"ImportDeclaration"!==u.type||u.importKind&&"value"!==u.importKind||(this.sawUnambiguousESM=!0)):(u=this.parseExport(r,e),("ExportNamedDeclaration"!==u.type||u.exportKind&&"value"!==u.exportKind)&&("ExportAllDeclaration"!==u.type||u.exportKind&&"value"!==u.exportKind)&&"ExportDefaultDeclaration"!==u.type||(this.sawUnambiguousESM=!0)),this.assertModuleNodeAllowed(u),u;default:if(this.isAsyncFunction())return s||this.raise(Z.AsyncFunctionInSingleStatementContext,{at:this.state.startLoc}),this.next(),this.parseFunctionStatement(r,!0,!s&&n)}var p=this.state.value,d=this.parseExpression();return At(i)&&"Identifier"===d.type&&this.eat(14)?this.parseLabeledStatement(r,p,d,t):this.parseExpressionStatement(r,d,e)}},{key:"assertModuleNodeAllowed",value:function(t){this.options.allowImportExportEverywhere||this.inModule||this.raise(Z.ImportOutsideModule,{at:t})}},{key:"decoratorsEnabledBeforeExport",value:function(){return!!this.hasPlugin("decorators-legacy")||this.hasPlugin("decorators")&&!!this.getPluginOption("decorators","decoratorsBeforeExport")}},{key:"maybeTakeDecorators",value:function(t,e,i){return t&&(e.decorators=t,this.resetStartLocationFromNode(e,t[0]),i&&this.resetStartLocationFromNode(i,e)),e}},{key:"canHaveLeadingDecorator",value:function(){return this.match(80)}},{key:"parseDecorators",value:function(t){var e=[];do{e.push(this.parseDecorator())}while(this.match(26));if(this.match(82))t||this.unexpected(),this.decoratorsEnabledBeforeExport()||this.raise(Z.DecoratorExportClass,{at:this.state.startLoc});else if(!this.canHaveLeadingDecorator())throw this.raise(Z.UnexpectedLeadingDecorator,{at:this.state.startLoc});return e}},{key:"parseDecorator",value:function(){this.expectOnePlugin(["decorators","decorators-legacy"]);var t=this.startNode();if(this.next(),this.hasPlugin("decorators")){var e,i=this.state.startLoc;if(this.match(10)){var r=this.state.startLoc;this.next(),e=this.parseExpression(),this.expect(11),e=this.wrapParenthesis(r,e);var s=this.state.startLoc;t.expression=this.parseMaybeDecoratorArguments(e),!1===this.getPluginOption("decorators","allowCallParenthesized")&&t.expression!==e&&this.raise(Z.DecoratorArgumentsOutsideParentheses,{at:s})}else{e=this.parseIdentifier(!1);while(this.eat(16)){var n=this.startNodeAt(i);n.object=e,this.match(136)?(this.classScope.usePrivateName(this.state.value,this.state.startLoc),n.property=this.parsePrivateName()):n.property=this.parseIdentifier(!0),n.computed=!1,e=this.finishNode(n,"MemberExpression")}t.expression=this.parseMaybeDecoratorArguments(e)}}else t.expression=this.parseExprSubscripts();return this.finishNode(t,"Decorator")}},{key:"parseMaybeDecoratorArguments",value:function(t){if(this.eat(10)){var e=this.startNodeAtNode(t);return e.callee=t,e.arguments=this.parseCallExpressionArguments(11,!1),this.toReferencedList(e.arguments),this.finishNode(e,"CallExpression")}return t}},{key:"parseBreakContinueStatement",value:function(t,e){return this.next(),this.isLineTerminator()?t.label=null:(t.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(t,e),this.finishNode(t,e?"BreakStatement":"ContinueStatement")}},{key:"verifyBreakContinue",value:function(t,e){var i;for(i=0;i<this.state.labels.length;++i){var r=this.state.labels[i];if(null==t.label||r.name===t.label.name){if(null!=r.kind&&(e||"loop"===r.kind))break;if(t.label&&e)break}}if(i===this.state.labels.length){var s=e?"BreakStatement":"ContinueStatement";this.raise(Z.IllegalBreakContinue,{at:t,type:s})}}},{key:"parseDebuggerStatement",value:function(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")}},{key:"parseHeaderExpression",value:function(){this.expect(10);var t=this.parseExpression();return this.expect(11),t}},{key:"parseDoWhileStatement",value:function(t){var e=this;return this.next(),this.state.labels.push(es),t.body=this.withSmartMixTopicForbiddingContext((function(){return e.parseStatement()})),this.state.labels.pop(),this.expect(92),t.test=this.parseHeaderExpression(),this.eat(13),this.finishNode(t,"DoWhileStatement")}},{key:"parseForStatement",value:function(t){this.next(),this.state.labels.push(es);var e=null;if(this.isAwaitAllowed()&&this.eatContextual(96)&&(e=this.state.lastTokStartLoc),this.scope.enter(de),this.expect(10),this.match(13))return null!==e&&this.unexpected(e),this.parseFor(t,null);var i=this.isContextual(99),r=this.isContextual(105)&&!this.hasFollowingLineBreak(),s=i&&this.hasFollowingBindingAtom()||r&&this.hasFollowingBindingIdentifier()&&this.startsUsingForOf();if(this.match(74)||this.match(75)||s){var n=this.startNode(),a=this.state.value;this.next(),this.parseVar(n,!0,a);var o=this.finishNode(n,"VariableDeclaration"),l=this.match(58);return l&&r&&this.raise(Z.ForInUsing,{at:o}),(l||this.isContextual(101))&&1===o.declarations.length?this.parseForIn(t,o,e):(null!==e&&this.unexpected(e),this.parseFor(t,o))}var c=this.isContextual(95),h=new nr,u=this.parseExpression(!0,h),p=this.isContextual(101);if(p&&(i&&this.raise(Z.ForOfLet,{at:u}),null===e&&c&&"Identifier"===u.type&&this.raise(Z.ForOfAsync,{at:u})),p||this.match(58)){this.checkDestructuringPrivate(h),this.toAssignable(u,!0);var d=p?"ForOfStatement":"ForInStatement";return this.checkLVal(u,{in:{type:d}}),this.parseForIn(t,u,e)}return this.checkExpressionErrors(h,!0),null!==e&&this.unexpected(e),this.parseFor(t,u)}},{key:"parseFunctionStatement",value:function(t,e,i){return this.next(),this.parseFunction(t,rs.Declaration|(i?rs.HangingDeclaration:0)|(e?rs.Async:0))}},{key:"parseIfStatement",value:function(t){return this.next(),t.test=this.parseHeaderExpression(),t.consequent=this.parseStatementOrFunctionDeclaration(!0),t.alternate=this.eat(66)?this.parseStatementOrFunctionDeclaration(!0):null,this.finishNode(t,"IfStatement")}},{key:"parseReturnStatement",value:function(t){return this.prodParam.hasReturn||this.options.allowReturnOutsideFunction||this.raise(Z.IllegalReturn,{at:this.state.startLoc}),this.next(),this.isLineTerminator()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")}},{key:"parseSwitchStatement",value:function(t){this.next(),t.discriminant=this.parseHeaderExpression();var e,i,r=t.cases=[];for(this.expect(5),this.state.labels.push(is),this.scope.enter(de);!this.match(8);)if(this.match(61)||this.match(65)){var s=this.match(61);e&&this.finishNode(e,"SwitchCase"),r.push(e=this.startNode()),e.consequent=[],this.next(),s?e.test=this.parseExpression():(i&&this.raise(Z.MultipleDefaultsInSwitch,{at:this.state.lastTokStartLoc}),i=!0,e.test=null),this.expect(14)}else e?e.consequent.push(this.parseStatementListItem()):this.unexpected();return this.scope.exit(),e&&this.finishNode(e,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(t,"SwitchStatement")}},{key:"parseThrowStatement",value:function(t){return this.next(),this.hasPrecedingLineBreak()&&this.raise(Z.NewlineAfterThrow,{at:this.state.lastTokEndLoc}),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")}},{key:"parseCatchClauseParam",value:function(){var t=this.parseBindingAtom(),e="Identifier"===t.type;return this.scope.enter(e?ve:0),this.checkLVal(t,{in:{type:"CatchClause"},binding:Be,allowingSloppyLetBinding:!0}),t}},{key:"parseTryStatement",value:function(t){var e=this;if(this.next(),t.block=this.parseBlock(),t.handler=null,this.match(62)){var i=this.startNode();this.next(),this.match(10)?(this.expect(10),i.param=this.parseCatchClauseParam(),this.expect(11)):(i.param=null,this.scope.enter(de)),i.body=this.withSmartMixTopicForbiddingContext((function(){return e.parseBlock(!1,!1)})),this.scope.exit(),t.handler=this.finishNode(i,"CatchClause")}return t.finalizer=this.eat(67)?this.parseBlock():null,t.handler||t.finalizer||this.raise(Z.NoCatchOrFinally,{at:t}),this.finishNode(t,"TryStatement")}},{key:"parseVarStatement",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.next(),this.parseVar(t,!1,e,i),this.semicolon(),this.finishNode(t,"VariableDeclaration")}},{key:"parseWhileStatement",value:function(t){var e=this;return this.next(),t.test=this.parseHeaderExpression(),this.state.labels.push(es),t.body=this.withSmartMixTopicForbiddingContext((function(){return e.parseStatement()})),this.state.labels.pop(),this.finishNode(t,"WhileStatement")}},{key:"parseWithStatement",value:function(t){var e=this;return this.state.strict&&this.raise(Z.StrictWith,{at:this.state.startLoc}),this.next(),t.object=this.parseHeaderExpression(),t.body=this.withSmartMixTopicForbiddingContext((function(){return e.parseStatement()})),this.finishNode(t,"WithStatement")}},{key:"parseEmptyStatement",value:function(t){return this.next(),this.finishNode(t,"EmptyStatement")}},{key:"parseLabeledStatement",value:function(t,e,i,r){var s,n=c(this.state.labels);try{for(n.s();!(s=n.n()).done;){var a=s.value;a.name===e&&this.raise(Z.LabelRedeclaration,{at:i,labelName:e})}}catch(u){n.e(u)}finally{n.f()}for(var o=Lt(this.state.type)?"loop":this.match(71)?"switch":null,l=this.state.labels.length-1;l>=0;l--){var h=this.state.labels[l];if(h.statementStart!==t.start)break;h.statementStart=this.state.start,h.kind=o}return this.state.labels.push({name:e,kind:o,statementStart:this.state.start}),t.body=r&ss.AllowLabeledFunction?this.parseStatementOrFunctionDeclaration(!1):this.parseStatement(),this.state.labels.pop(),t.label=i,this.finishNode(t,"LabeledStatement")}},{key:"parseExpressionStatement",value:function(t,e,i){return t.expression=e,this.semicolon(),this.finishNode(t,"ExpressionStatement")}},{key:"parseBlock",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2?arguments[2]:void 0,r=this.startNode();return t&&this.state.strictErrors.clear(),this.expect(5),e&&this.scope.enter(de),this.parseBlockBody(r,t,!1,8,i),e&&this.scope.exit(),this.finishNode(r,"BlockStatement")}},{key:"isValidDirective",value:function(t){return"ExpressionStatement"===t.type&&"StringLiteral"===t.expression.type&&!t.expression.extra.parenthesized}},{key:"parseBlockBody",value:function(t,e,i,r,s){var n=t.body=[],a=t.directives=[];this.parseBlockOrModuleBlockBody(n,e?a:void 0,i,r,s)}},{key:"parseBlockOrModuleBlockBody",value:function(t,e,i,r,s){var n=this.state.strict,a=!1,o=!1;while(!this.match(r)){var l=i?this.parseModuleItem():this.parseStatementListItem();if(e&&!o){if(this.isValidDirective(l)){var c=this.stmtToDirective(l);e.push(c),a||"use strict"!==c.value.value||(a=!0,this.setStrict(!0));continue}o=!0,this.state.strictErrors.clear()}t.push(l)}s&&s.call(this,a),n||this.setStrict(!1),this.next()}},{key:"parseFor",value:function(t,e){var i=this;return t.init=e,this.semicolon(!1),t.test=this.match(13)?null:this.parseExpression(),this.semicolon(!1),t.update=this.match(11)?null:this.parseExpression(),this.expect(11),t.body=this.withSmartMixTopicForbiddingContext((function(){return i.parseStatement()})),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,"ForStatement")}},{key:"parseForIn",value:function(t,e,i){var r=this,s=this.match(58);return this.next(),s?null!==i&&this.unexpected(i):t.await=null!==i,"VariableDeclaration"!==e.type||null==e.declarations[0].init||s&&!this.state.strict&&"var"===e.kind&&"Identifier"===e.declarations[0].id.type||this.raise(Z.ForInOfLoopInitializer,{at:e,type:s?"ForInStatement":"ForOfStatement"}),"AssignmentPattern"===e.type&&this.raise(Z.InvalidLhs,{at:e,ancestor:{type:"ForStatement"}}),t.left=e,t.right=s?this.parseExpression():this.parseMaybeAssignAllowIn(),this.expect(11),t.body=this.withSmartMixTopicForbiddingContext((function(){return r.parseStatement()})),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,s?"ForInStatement":"ForOfStatement")}},{key:"parseVar",value:function(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=t.declarations=[];for(t.kind=i;;){var n=this.startNode();if(this.parseVarId(n,i),n.init=this.eat(29)?e?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn():null,null!==n.init||r||("Identifier"===n.id.type||e&&(this.match(58)||this.isContextual(101))?"const"!==i||this.match(58)||this.isContextual(101)||this.raise(Z.DeclarationMissingInitializer,{at:this.state.lastTokEndLoc,kind:"const"}):this.raise(Z.DeclarationMissingInitializer,{at:this.state.lastTokEndLoc,kind:"destructuring"})),s.push(this.finishNode(n,"VariableDeclarator")),!this.eat(12))break}return t}},{key:"parseVarId",value:function(t,e){var i=this.parseBindingAtom();this.checkLVal(i,{in:{type:"VariableDeclarator"},binding:"var"===e?je:Be}),t.id=i}},{key:"parseAsyncFunctionExpression",value:function(t){return this.parseFunction(t,rs.Async)}},{key:"parseFunction",value:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rs.Expression,r=i&rs.HangingDeclaration,s=!!(i&rs.Declaration),n=s&&!(i&rs.NullableId),a=!!(i&rs.Async);this.initFunction(t,a),this.match(55)&&(r&&this.raise(Z.GeneratorInSingleStatementContext,{at:this.state.startLoc}),this.next(),t.generator=!0),s&&(t.id=this.parseFunctionId(n));var o=this.state.maybeInArrowParameters;return this.state.maybeInArrowParameters=!1,this.scope.enter(me),this.prodParam.enter(rr(a,t.generator)),s||(t.id=this.parseFunctionId()),this.parseFunctionParams(t,!1),this.withSmartMixTopicForbiddingContext((function(){e.parseFunctionBodyAndFinish(t,s?"FunctionDeclaration":"FunctionExpression")})),this.prodParam.exit(),this.scope.exit(),s&&!r&&this.registerFunctionStatementId(t),this.state.maybeInArrowParameters=o,t}},{key:"parseFunctionId",value:function(t){return t||At(this.state.type)?this.parseIdentifier():null}},{key:"parseFunctionParams",value:function(t,e){this.expect(10),this.expressionScope.enter(Wi()),t.params=this.parseBindingList(11,41,!1,e),this.expressionScope.exit()}},{key:"registerFunctionStatementId",value:function(t){t.id&&this.scope.declareName(t.id.name,this.state.strict||t.generator||t.async?this.scope.treatFunctionsAsVar?je:Be:Re,t.id.loc.start)}},{key:"parseClass",value:function(t,e,i){this.next();var r=this.state.strict;return this.state.strict=!0,this.parseClassId(t,e,i),this.parseClassSuper(t),t.body=this.parseClassBody(!!t.superClass,r),this.finishNode(t,e?"ClassDeclaration":"ClassExpression")}},{key:"isClassProperty",value:function(){return this.match(29)||this.match(13)||this.match(8)}},{key:"isClassMethod",value:function(){return this.match(10)}},{key:"isNonstaticConstructor",value:function(t){return!t.computed&&!t.static&&("constructor"===t.key.name||"constructor"===t.key.value)}},{key:"parseClassBody",value:function(t,e){var i=this;this.classScope.enter();var r={hadConstructor:!1,hadSuperClass:t},s=[],n=this.startNode();if(n.body=[],this.expect(5),this.withSmartMixTopicForbiddingContext((function(){while(!i.match(8))if(i.eat(13)){if(s.length>0)throw i.raise(Z.DecoratorSemicolon,{at:i.state.lastTokEndLoc})}else if(i.match(26))s.push(i.parseDecorator());else{var t=i.startNode();s.length&&(t.decorators=s,i.resetStartLocationFromNode(t,s[0]),s=[]),i.parseClassMember(n,t,r),"constructor"===t.kind&&t.decorators&&t.decorators.length>0&&i.raise(Z.DecoratorConstructor,{at:t})}})),this.state.strict=e,this.next(),s.length)throw this.raise(Z.TrailingDecorator,{at:this.state.startLoc});return this.classScope.exit(),this.finishNode(n,"ClassBody")}},{key:"parseClassMemberFromModifier",value:function(t,e){var i=this.parseIdentifier(!0);if(this.isClassMethod()){var r=e;return r.kind="method",r.computed=!1,r.key=i,r.static=!1,this.pushClassMethod(t,r,!1,!1,!1,!1),!0}if(this.isClassProperty()){var s=e;return s.computed=!1,s.key=i,s.static=!1,t.body.push(this.parseClassProperty(s)),!0}return this.resetPreviousNodeTrailingComments(i),!1}},{key:"parseClassMember",value:function(t,e,i){var r=this.isContextual(104);if(r){if(this.parseClassMemberFromModifier(t,e))return;if(this.eat(5))return void this.parseClassStaticBlock(t,e)}this.parseClassMemberWithIsStatic(t,e,i,r)}},{key:"parseClassMemberWithIsStatic",value:function(t,e,i,r){var s=e,n=e,a=e,o=e,l=e,c=s,h=s;if(e.static=r,this.parsePropertyNamePrefixOperator(e),this.eat(55)){c.kind="method";var u=this.match(136);return this.parseClassElementName(c),u?void this.pushClassPrivateMethod(t,n,!0,!1):(this.isNonstaticConstructor(s)&&this.raise(Z.ConstructorIsGenerator,{at:s.key}),void this.pushClassMethod(t,s,!0,!1,!1,!1))}var p=At(this.state.type)&&!this.state.containsEsc,d=this.match(136),f=this.parseClassElementName(e),m=this.state.startLoc;if(this.parsePostMemberNameModifiers(h),this.isClassMethod()){if(c.kind="method",d)return void this.pushClassPrivateMethod(t,n,!1,!1);var y=this.isNonstaticConstructor(s),v=!1;y&&(s.kind="constructor",i.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(Z.DuplicateConstructor,{at:f}),y&&this.hasPlugin("typescript")&&e.override&&this.raise(Z.OverrideOnConstructor,{at:f}),i.hadConstructor=!0,v=i.hadSuperClass),this.pushClassMethod(t,s,!1,!1,y,v)}else if(this.isClassProperty())d?this.pushClassPrivateProperty(t,o):this.pushClassProperty(t,a);else if(p&&"async"===f.name&&!this.isLineTerminator()){this.resetPreviousNodeTrailingComments(f);var g=this.eat(55);h.optional&&this.unexpected(m),c.kind="method";var x=this.match(136);this.parseClassElementName(c),this.parsePostMemberNameModifiers(h),x?this.pushClassPrivateMethod(t,n,g,!0):(this.isNonstaticConstructor(s)&&this.raise(Z.ConstructorIsAsync,{at:s.key}),this.pushClassMethod(t,s,g,!0,!1,!1))}else if(!p||"get"!==f.name&&"set"!==f.name||this.match(55)&&this.isLineTerminator())if(p&&"accessor"===f.name&&!this.isLineTerminator()){this.expectPlugin("decoratorAutoAccessors"),this.resetPreviousNodeTrailingComments(f);var b=this.match(136);this.parseClassElementName(a),this.pushClassAccessorProperty(t,l,b)}else this.isLineTerminator()?d?this.pushClassPrivateProperty(t,o):this.pushClassProperty(t,a):this.unexpected();else{this.resetPreviousNodeTrailingComments(f),c.kind=f.name;var P=this.match(136);this.parseClassElementName(s),P?this.pushClassPrivateMethod(t,n,!1,!1):(this.isNonstaticConstructor(s)&&this.raise(Z.ConstructorIsAccessor,{at:s.key}),this.pushClassMethod(t,s,!1,!1,!1,!1)),this.checkGetterSetterParams(s)}}},{key:"parseClassElementName",value:function(t){var e=this.state,i=e.type,r=e.value;if(130!==i&&131!==i||!t.static||"prototype"!==r||this.raise(Z.StaticPrototype,{at:this.state.startLoc}),136===i){"constructor"===r&&this.raise(Z.ConstructorClassPrivateField,{at:this.state.startLoc});var s=this.parsePrivateName();return t.key=s,s}return this.parsePropertyName(t)}},{key:"parseClassStaticBlock",value:function(t,e){var i;this.scope.enter(be|Pe|ge);var r=this.state.labels;this.state.labels=[],this.prodParam.enter($i);var s=e.body=[];this.parseBlockOrModuleBlockBody(s,void 0,!1,8),this.prodParam.exit(),this.scope.exit(),this.state.labels=r,t.body.push(this.finishNode(e,"StaticBlock")),null!=(i=e.decorators)&&i.length&&this.raise(Z.DecoratorStaticBlock,{at:e})}},{key:"pushClassProperty",value:function(t,e){e.computed||"constructor"!==e.key.name&&"constructor"!==e.key.value||this.raise(Z.ConstructorClassField,{at:e.key}),t.body.push(this.parseClassProperty(e))}},{key:"pushClassPrivateProperty",value:function(t,e){var i=this.parseClassPrivateProperty(e);t.body.push(i),this.classScope.declarePrivateName(this.getPrivateNameSV(i.key),si,i.key.loc.start)}},{key:"pushClassAccessorProperty",value:function(t,e,i){if(!i&&!e.computed){var r=e.key;"constructor"!==r.name&&"constructor"!==r.value||this.raise(Z.ConstructorClassField,{at:r})}var s=this.parseClassAccessorProperty(e);t.body.push(s),i&&this.classScope.declarePrivateName(this.getPrivateNameSV(s.key),si,s.key.loc.start)}},{key:"pushClassMethod",value:function(t,e,i,r,s,n){t.body.push(this.parseMethod(e,i,r,s,n,"ClassMethod",!0))}},{key:"pushClassPrivateMethod",value:function(t,e,i,r){var s=this.parseMethod(e,i,r,!1,!1,"ClassPrivateMethod",!0);t.body.push(s);var n="get"===s.kind?s.static?ti:ii:"set"===s.kind?s.static?ei:ri:si;this.declareClassPrivateMethodInScope(s,n)}},{key:"declareClassPrivateMethodInScope",value:function(t,e){this.classScope.declarePrivateName(this.getPrivateNameSV(t.key),e,t.key.loc.start)}},{key:"parsePostMemberNameModifiers",value:function(t){}},{key:"parseClassPrivateProperty",value:function(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassPrivateProperty")}},{key:"parseClassProperty",value:function(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassProperty")}},{key:"parseClassAccessorProperty",value:function(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassAccessorProperty")}},{key:"parseInitializer",value:function(t){this.scope.enter(be|ge),this.expressionScope.enter(Gi()),this.prodParam.enter($i),t.value=this.eat(29)?this.parseMaybeAssignAllowIn():null,this.expressionScope.exit(),this.prodParam.exit(),this.scope.exit()}},{key:"parseClassId",value:function(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:_e;if(At(this.state.type))t.id=this.parseIdentifier(),e&&this.declareNameFromIdentifier(t.id,r);else{if(!i&&e)throw this.raise(Z.MissingClassName,{at:this.state.startLoc});t.id=null}}},{key:"parseClassSuper",value:function(t){t.superClass=this.eat(81)?this.parseExprSubscripts():null}},{key:"parseExport",value:function(t,e){var i=this.maybeParseExportDefaultSpecifier(t),r=!i||this.eat(12),s=r&&this.eatExportStar(t),n=s&&this.maybeParseExportNamespaceSpecifier(t),a=r&&(!n||this.eat(12)),o=i||s;if(s&&!n){if(i&&this.unexpected(),e)throw this.raise(Z.UnsupportedDecoratorExport,{at:t});return this.parseExportFrom(t,!0),this.finishNode(t,"ExportAllDeclaration")}var l,c=this.maybeParseExportNamedSpecifiers(t);if(i&&r&&!s&&!c||n&&a&&!c)throw this.unexpected(null,5);if(o||c){if(l=!1,e)throw this.raise(Z.UnsupportedDecoratorExport,{at:t});this.parseExportFrom(t,o)}else l=this.maybeParseExportDeclaration(t);if(o||c||l){var h,u=t;if(this.checkExport(u,!0,!1,!!u.source),"ClassDeclaration"===(null==(h=u.declaration)?void 0:h.type))this.maybeTakeDecorators(e,u.declaration,u);else if(e)throw this.raise(Z.UnsupportedDecoratorExport,{at:t});return this.finishNode(u,"ExportNamedDeclaration")}if(this.eat(65)){var p=t,d=this.parseExportDefaultExpression();if(p.declaration=d,"ClassDeclaration"===d.type)this.maybeTakeDecorators(e,d,p);else if(e)throw this.raise(Z.UnsupportedDecoratorExport,{at:t});return this.checkExport(p,!0,!0),this.finishNode(p,"ExportDefaultDeclaration")}throw this.unexpected(null,5)}},{key:"eatExportStar",value:function(t){return this.eat(55)}},{key:"maybeParseExportDefaultSpecifier",value:function(t){if(this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom");var e=this.startNode();return e.exported=this.parseIdentifier(!0),t.specifiers=[this.finishNode(e,"ExportDefaultSpecifier")],!0}return!1}},{key:"maybeParseExportNamespaceSpecifier",value:function(t){if(this.isContextual(93)){t.specifiers||(t.specifiers=[]);var e=this.startNodeAt(this.state.lastTokStartLoc);return this.next(),e.exported=this.parseModuleExportName(),t.specifiers.push(this.finishNode(e,"ExportNamespaceSpecifier")),!0}return!1}},{key:"maybeParseExportNamedSpecifiers",value:function(t){if(this.match(5)){var e;t.specifiers||(t.specifiers=[]);var i="type"===t.exportKind;return(e=t.specifiers).push.apply(e,h(this.parseExportSpecifiers(i))),t.source=null,t.declaration=null,this.hasPlugin("importAssertions")&&(t.assertions=[]),!0}return!1}},{key:"maybeParseExportDeclaration",value:function(t){return!!this.shouldParseExportDeclaration()&&(t.specifiers=[],t.source=null,this.hasPlugin("importAssertions")&&(t.assertions=[]),t.declaration=this.parseExportDeclaration(t),!0)}},{key:"isAsyncFunction",value:function(){if(!this.isContextual(95))return!1;var t=this.nextTokenStart();return!mi.test(this.input.slice(this.state.pos,t))&&this.isUnparsedContextual(t,"function")}},{key:"parseExportDefaultExpression",value:function(){var t=this.startNode();if(this.match(68))return this.next(),this.parseFunction(t,rs.Declaration|rs.NullableId);if(this.isAsyncFunction())return this.next(),this.next(),this.parseFunction(t,rs.Declaration|rs.NullableId|rs.Async);if(this.match(80))return this.parseClass(t,!0,!0);if(this.match(26))return this.hasPlugin("decorators")&&this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(Z.DecoratorBeforeExport,{at:this.state.startLoc}),this.parseClass(this.maybeTakeDecorators(this.parseDecorators(!1),this.startNode()),!0,!0);if(this.match(75)||this.match(74)||this.isLet())throw this.raise(Z.UnsupportedDefaultExport,{at:this.state.startLoc});var e=this.parseMaybeAssignAllowIn();return this.semicolon(),e}},{key:"parseExportDeclaration",value:function(t){if(this.match(80)){var e=this.parseClass(this.startNode(),!0,!1);return e}return this.parseStatementListItem()}},{key:"isExportDefaultSpecifier",value:function(){var t=this.state.type;if(At(t)){if(95===t&&!this.state.containsEsc||99===t)return!1;if((128===t||127===t)&&!this.state.containsEsc){var e=this.lookahead(),i=e.type;if(At(i)&&97!==i||5===i)return this.expectOnePlugin(["flow","typescript"]),!1}}else if(!this.match(65))return!1;var r=this.nextTokenStart(),s=this.isUnparsedContextual(r,"from");if(44===this.input.charCodeAt(r)||At(this.state.type)&&s)return!0;if(this.match(65)&&s){var n=this.input.charCodeAt(this.nextTokenStartSince(r+4));return 34===n||39===n}return!1}},{key:"parseExportFrom",value:function(t,e){if(this.eatContextual(97)){t.source=this.parseImportSource(),this.checkExport(t);var i=this.maybeParseImportAssertions();i&&(t.assertions=i,this.checkJSONModuleImport(t))}else e&&this.unexpected();this.semicolon()}},{key:"shouldParseExportDeclaration",value:function(){var t=this.state.type;if(26===t&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))){if(this.getPluginOption("decorators","decoratorsBeforeExport"))throw this.raise(Z.DecoratorBeforeExport,{at:this.state.startLoc});return!0}return 74===t||75===t||68===t||80===t||this.isLet()||this.isAsyncFunction()}},{key:"checkExport",value:function(t,e,i,r){if(e)if(i){if(this.checkDuplicateExports(t,"default"),this.hasPlugin("exportDefaultFrom")){var s,n=t.declaration;"Identifier"!==n.type||"from"!==n.name||n.end-n.start!==4||null!=(s=n.extra)&&s.parenthesized||this.raise(Z.ExportDefaultFromAsIdentifier,{at:n})}}else if(t.specifiers&&t.specifiers.length){var a,o=c(t.specifiers);try{for(o.s();!(a=o.n()).done;){var l=a.value,h=l.exported,u="Identifier"===h.type?h.name:h.value;if(this.checkDuplicateExports(l,u),!r&&l.local){var p=l,d=p.local;"Identifier"!==d.type?this.raise(Z.ExportBindingIsString,{at:l,localName:d.value,exportName:u}):(this.checkReservedWord(d.name,d.loc.start,!0,!1),this.scope.checkLocalExport(d))}}}catch(g){o.e(g)}finally{o.f()}}else if(t.declaration)if("FunctionDeclaration"===t.declaration.type||"ClassDeclaration"===t.declaration.type){var f=t.declaration.id;if(!f)throw new Error("Assertion failure");this.checkDuplicateExports(t,f.name)}else if("VariableDeclaration"===t.declaration.type){var m,y=c(t.declaration.declarations);try{for(y.s();!(m=y.n()).done;){var v=m.value;this.checkDeclaration(v.id)}}catch(g){y.e(g)}finally{y.f()}}}},{key:"checkDeclaration",value:function(t){if("Identifier"===t.type)this.checkDuplicateExports(t,t.name);else if("ObjectPattern"===t.type){var e,i=c(t.properties);try{for(i.s();!(e=i.n()).done;){var r=e.value;this.checkDeclaration(r)}}catch(o){i.e(o)}finally{i.f()}}else if("ArrayPattern"===t.type){var s,n=c(t.elements);try{for(n.s();!(s=n.n()).done;){var a=s.value;a&&this.checkDeclaration(a)}}catch(o){n.e(o)}finally{n.f()}}else"ObjectProperty"===t.type?this.checkDeclaration(t.value):"RestElement"===t.type?this.checkDeclaration(t.argument):"AssignmentPattern"===t.type&&this.checkDeclaration(t.left)}},{key:"checkDuplicateExports",value:function(t,e){this.exportedIdentifiers.has(e)&&("default"===e?this.raise(Z.DuplicateDefaultExport,{at:t}):this.raise(Z.DuplicateExport,{at:t,exportName:e})),this.exportedIdentifiers.add(e)}},{key:"parseExportSpecifiers",value:function(t){var e=[],i=!0;this.expect(5);while(!this.eat(8)){if(i)i=!1;else if(this.expect(12),this.eat(8))break;var r=this.isContextual(128),s=this.match(131),n=this.startNode();n.local=this.parseModuleExportName(),e.push(this.parseExportSpecifier(n,s,t,r))}return e}},{key:"parseExportSpecifier",value:function(t,e,i,r){return this.eatContextual(93)?t.exported=this.parseModuleExportName():e?t.exported=hr(t.local):t.exported||(t.exported=cr(t.local)),this.finishNode(t,"ExportSpecifier")}},{key:"parseModuleExportName",value:function(){if(this.match(131)){var t=this.parseStringLiteral(this.state.value),e=t.value.match(ns);return e&&this.raise(Z.ModuleExportNameHasLoneSurrogate,{at:t,surrogateCharCode:e[0].charCodeAt(0)}),t}return this.parseIdentifier(!0)}},{key:"isJSONModuleImport",value:function(t){return null!=t.assertions&&t.assertions.some((function(t){var e=t.key,i=t.value;return"json"===i.value&&("Identifier"===e.type?"type"===e.name:"type"===e.value)}))}},{key:"checkImportReflection",value:function(t){var e;t.module&&(1===t.specifiers.length&&"ImportDefaultSpecifier"===t.specifiers[0].type||this.raise(Z.ImportReflectionNotBinding,{at:t.specifiers[0].loc.start}),(null==(e=t.assertions)?void 0:e.length)>0&&this.raise(Z.ImportReflectionHasAssertion,{at:t.specifiers[0].loc.start}))}},{key:"checkJSONModuleImport",value:function(t){if(this.isJSONModuleImport(t)&&"ExportAllDeclaration"!==t.type){var e=t.specifiers;if(null!=e){var i=e.find((function(t){var e;if("ExportSpecifier"===t.type?e=t.local:"ImportSpecifier"===t.type&&(e=t.imported),void 0!==e)return"Identifier"===e.type?"default"!==e.name:"default"!==e.value}));void 0!==i&&this.raise(Z.ImportJSONBindingNotDefault,{at:i.loc.start})}}}},{key:"parseMaybeImportReflection",value:function(t){var e=!1;if(this.isContextual(125)){var i=this.lookahead(),r=i.type;if(At(r))if(97!==r)e=!0;else{var s=this.input.charCodeAt(this.nextTokenStartSince(i.end));102===s&&(e=!0)}else 12!==r&&(e=!0)}e?(this.expectPlugin("importReflection"),this.next(),t.module=!0):this.hasPlugin("importReflection")&&(t.module=!1)}},{key:"parseImport",value:function(t){if(t.specifiers=[],!this.match(131)){this.parseMaybeImportReflection(t);var e=this.maybeParseDefaultImportSpecifier(t),i=!e||this.eat(12),r=i&&this.maybeParseStarImportSpecifier(t);i&&!r&&this.parseNamedImportSpecifiers(t),this.expectContextual(97)}t.source=this.parseImportSource();var s=this.maybeParseImportAssertions();if(s)t.assertions=s;else{var n=this.maybeParseModuleAttributes();n&&(t.attributes=n)}return this.checkImportReflection(t),this.checkJSONModuleImport(t),this.semicolon(),this.finishNode(t,"ImportDeclaration")}},{key:"parseImportSource",value:function(){return this.match(131)||this.unexpected(),this.parseExprAtom()}},{key:"shouldParseDefaultImport",value:function(t){return At(this.state.type)}},{key:"parseImportSpecifierLocal",value:function(t,e,i){e.local=this.parseIdentifier(),t.specifiers.push(this.finishImportSpecifier(e,i))}},{key:"finishImportSpecifier",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Be;return this.checkLVal(t.local,{in:t,binding:i}),this.finishNode(t,e)}},{key:"parseAssertEntries",value:function(){var t=[],e=new Set;do{if(this.match(8))break;var i=this.startNode(),r=this.state.value;if(e.has(r)&&this.raise(Z.ModuleAttributesWithDuplicateKeys,{at:this.state.startLoc,key:r}),e.add(r),this.match(131)?i.key=this.parseStringLiteral(r):i.key=this.parseIdentifier(!0),this.expect(14),!this.match(131))throw this.raise(Z.ModuleAttributeInvalidValue,{at:this.state.startLoc});i.value=this.parseStringLiteral(this.state.value),t.push(this.finishNode(i,"ImportAttribute"))}while(this.eat(12));return t}},{key:"maybeParseModuleAttributes",value:function(){if(!this.match(76)||this.hasPrecedingLineBreak())return this.hasPlugin("moduleAttributes")?[]:null;this.expectPlugin("moduleAttributes"),this.next();var t=[],e=new Set;do{var i=this.startNode();if(i.key=this.parseIdentifier(!0),"type"!==i.key.name&&this.raise(Z.ModuleAttributeDifferentFromType,{at:i.key}),e.has(i.key.name)&&this.raise(Z.ModuleAttributesWithDuplicateKeys,{at:i.key,key:i.key.name}),e.add(i.key.name),this.expect(14),!this.match(131))throw this.raise(Z.ModuleAttributeInvalidValue,{at:this.state.startLoc});i.value=this.parseStringLiteral(this.state.value),this.finishNode(i,"ImportAttribute"),t.push(i)}while(this.eat(12));return t}},{key:"maybeParseImportAssertions",value:function(){if(!this.isContextual(94)||this.hasPrecedingLineBreak())return this.hasPlugin("importAssertions")?[]:null;this.expectPlugin("importAssertions"),this.next(),this.eat(5);var t=this.parseAssertEntries();return this.eat(8),t}},{key:"maybeParseDefaultImportSpecifier",value:function(t){return!!this.shouldParseDefaultImport(t)&&(this.parseImportSpecifierLocal(t,this.startNode(),"ImportDefaultSpecifier"),!0)}},{key:"maybeParseStarImportSpecifier",value:function(t){if(this.match(55)){var e=this.startNode();return this.next(),this.expectContextual(93),this.parseImportSpecifierLocal(t,e,"ImportNamespaceSpecifier"),!0}return!1}},{key:"parseNamedImportSpecifiers",value:function(t){var e=!0;this.expect(5);while(!this.eat(8)){if(e)e=!1;else{if(this.eat(14))throw this.raise(Z.DestructureNamedImport,{at:this.state.startLoc});if(this.expect(12),this.eat(8))break}var i=this.startNode(),r=this.match(131),s=this.isContextual(128);i.imported=this.parseModuleExportName();var n=this.parseImportSpecifier(i,r,"type"===t.importKind||"typeof"===t.importKind,s,void 0);t.specifiers.push(n)}}},{key:"parseImportSpecifier",value:function(t,e,i,r,s){if(this.eatContextual(93))t.local=this.parseIdentifier();else{var n=t.imported;if(e)throw this.raise(Z.ImportBindingIsString,{at:t,importName:n.value});this.checkReservedWord(n.name,t.loc.start,!0,!0),t.local||(t.local=cr(n))}return this.finishImportSpecifier(t,"ImportSpecifier",s)}},{key:"isThisParam",value:function(t){return"Identifier"===t.type&&"this"===t.name}}]),i}(ts),cs=function(t){y(i,t);var e=g(i);function i(t,r){var s;return L(this,i),t=Gr(t),s=e.call(this,t,r),s.options=t,s.initializeScopes(),s.plugins=hs(s.options.plugins),s.filename=t.sourceFilename,s}return D(i,[{key:"getScopeHandler",value:function(){return ai}},{key:"parse",value:function(){this.enterInitialScopes();var t=this.startNode(),e=this.startNode();return this.nextToken(),t.errors=null,this.parseTopLevel(t,e),t.errors=this.state.errors,t}}]),i}(ls);function hs(t){var e,i=new Map,r=c(t);try{for(r.s();!(e=r.n()).done;){var s=e.value,n=Array.isArray(s)?s:[s,{}],a=w(n,2),o=a[0],l=a[1];i.has(o)||i.set(o,l||{})}}catch(h){r.e(h)}finally{r.f()}return i}function us(t,e){var i;if("unambiguous"!==(null==(i=e)?void 0:i.sourceType))return ms(e,t).parse();e=Object.assign({},e);try{e.sourceType="module";var r=ms(e,t),s=r.parse();if(r.sawUnambiguousESM)return s;if(r.ambiguousScriptDifferentAst)try{return e.sourceType="script",ms(e,t).parse()}catch(n){}else s.program.sourceType="script";return s}catch(a){try{return e.sourceType="script",ms(e,t).parse()}catch(o){}throw a}}function ps(t,e){var i=ms(e,t);return i.options.strictMode&&(i.state.strict=!0),i.getExpression()}function ds(t){for(var e={},i=0,r=Object.keys(t);i<r.length;i++){var s=r[i];e[s]=Kt(t[s])}return e}var fs=ds(Et);function ms(t,e){var i=cs;return null!=t&&t.plugins&&(Xr(t.plugins),i=vs(t.plugins)),new i(t,e)}var ys={};function vs(t){var e=Jr.filter((function(e){return zr(t,e)})),i=e.join("/"),r=ys[i];if(!r){r=cs;var s,n=c(e);try{for(n.s();!(s=n.n()).done;){var a=s.value;r=Wr[a](r)}}catch(o){n.e(o)}finally{n.f()}ys[i]=r}return r}e.parse=us,e.parseExpression=ps,e.tokTypes=fs},"28a0":function(t,e){"function"===typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t}},3022:function(t,e,i){(function(t){var r=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),i={},r=0;r<e.length;r++)i[e[r]]=Object.getOwnPropertyDescriptor(t,e[r]);return i},s=/%[sdj%]/g;e.format=function(t){if(!k(t)){for(var e=[],i=0;i<arguments.length;i++)e.push(o(arguments[i]));return e.join(" ")}i=1;for(var r=arguments,n=r.length,a=String(t).replace(s,(function(t){if("%%"===t)return"%";if(i>=n)return t;switch(t){case"%s":return String(r[i++]);case"%d":return Number(r[i++]);case"%j":try{return JSON.stringify(r[i++])}catch(e){return"[Circular]"}default:return t}})),l=r[i];i<n;l=r[++i])x(l)||!A(l)?a+=" "+l:a+=" "+o(l);return a},e.deprecate=function(i,r){if("undefined"!==typeof t&&!0===t.noDeprecation)return i;if("undefined"===typeof t)return function(){return e.deprecate(i,r).apply(this,arguments)};var s=!1;function n(){if(!s){if(t.throwDeprecation)throw new Error(r);t.traceDeprecation?console.trace(r):console.error(r),s=!0}return i.apply(this,arguments)}return n};var n,a={};function o(t,i){var r={seen:[],stylize:c};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),g(i)?r.showHidden=i:i&&e._extend(r,i),w(r.showHidden)&&(r.showHidden=!1),w(r.depth)&&(r.depth=2),w(r.colors)&&(r.colors=!1),w(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=l),u(r,t,r.depth)}function l(t,e){var i=o.styles[e];return i?"["+o.colors[i][0]+"m"+t+"["+o.colors[i][1]+"m":t}function c(t,e){return t}function h(t){var e={};return t.forEach((function(t,i){e[t]=!0})),e}function u(t,i,r){if(t.customInspect&&i&&I(i.inspect)&&i.inspect!==e.inspect&&(!i.constructor||i.constructor.prototype!==i)){var s=i.inspect(r,t);return k(s)||(s=u(t,s,r)),s}var n=p(t,i);if(n)return n;var a=Object.keys(i),o=h(a);if(t.showHidden&&(a=Object.getOwnPropertyNames(i)),C(i)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return d(i);if(0===a.length){if(I(i)){var l=i.name?": "+i.name:"";return t.stylize("[Function"+l+"]","special")}if(E(i))return t.stylize(RegExp.prototype.toString.call(i),"regexp");if(S(i))return t.stylize(Date.prototype.toString.call(i),"date");if(C(i))return d(i)}var c,g="",x=!1,b=["{","}"];if(v(i)&&(x=!0,b=["[","]"]),I(i)){var P=i.name?": "+i.name:"";g=" [Function"+P+"]"}return E(i)&&(g=" "+RegExp.prototype.toString.call(i)),S(i)&&(g=" "+Date.prototype.toUTCString.call(i)),C(i)&&(g=" "+d(i)),0!==a.length||x&&0!=i.length?r<0?E(i)?t.stylize(RegExp.prototype.toString.call(i),"regexp"):t.stylize("[Object]","special"):(t.seen.push(i),c=x?f(t,i,r,o,a):a.map((function(e){return m(t,i,r,o,e,x)})),t.seen.pop(),y(c,g,b)):b[0]+g+b[1]}function p(t,e){if(w(e))return t.stylize("undefined","undefined");if(k(e)){var i="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(i,"string")}return P(e)?t.stylize(""+e,"number"):g(e)?t.stylize(""+e,"boolean"):x(e)?t.stylize("null","null"):void 0}function d(t){return"["+Error.prototype.toString.call(t)+"]"}function f(t,e,i,r,s){for(var n=[],a=0,o=e.length;a<o;++a)F(e,String(a))?n.push(m(t,e,i,r,String(a),!0)):n.push("");return s.forEach((function(s){s.match(/^\d+$/)||n.push(m(t,e,i,r,s,!0))})),n}function m(t,e,i,r,s,n){var a,o,l;if(l=Object.getOwnPropertyDescriptor(e,s)||{value:e[s]},l.get?o=l.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):l.set&&(o=t.stylize("[Setter]","special")),F(r,s)||(a="["+s+"]"),o||(t.seen.indexOf(l.value)<0?(o=x(i)?u(t,l.value,null):u(t,l.value,i-1),o.indexOf("\n")>-1&&(o=n?o.split("\n").map((function(t){return"  "+t})).join("\n").substr(2):"\n"+o.split("\n").map((function(t){return"   "+t})).join("\n"))):o=t.stylize("[Circular]","special")),w(a)){if(n&&s.match(/^\d+$/))return o;a=JSON.stringify(""+s),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=t.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=t.stylize(a,"string"))}return a+": "+o}function y(t,e,i){var r=t.reduce((function(t,e){return e.indexOf("\n")>=0&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return r>60?i[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+i[1]:i[0]+e+" "+t.join(", ")+" "+i[1]}function v(t){return Array.isArray(t)}function g(t){return"boolean"===typeof t}function x(t){return null===t}function b(t){return null==t}function P(t){return"number"===typeof t}function k(t){return"string"===typeof t}function T(t){return"symbol"===typeof t}function w(t){return void 0===t}function E(t){return A(t)&&"[object RegExp]"===D(t)}function A(t){return"object"===typeof t&&null!==t}function S(t){return A(t)&&"[object Date]"===D(t)}function C(t){return A(t)&&("[object Error]"===D(t)||t instanceof Error)}function I(t){return"function"===typeof t}function N(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t}function D(t){return Object.prototype.toString.call(t)}function O(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(i){if(w(n)&&(n=Object({VUE_APP_BASE_API:"http://127.0.0.1:8080",NODE_ENV:"production",BASE_URL:"/"}).NODE_DEBUG||""),i=i.toUpperCase(),!a[i])if(new RegExp("\\b"+i+"\\b","i").test(n)){var r=t.pid;a[i]=function(){var t=e.format.apply(e,arguments);console.error("%s %d: %s",i,r,t)}}else a[i]=function(){};return a[i]},e.inspect=o,o.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},o.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.isArray=v,e.isBoolean=g,e.isNull=x,e.isNullOrUndefined=b,e.isNumber=P,e.isString=k,e.isSymbol=T,e.isUndefined=w,e.isRegExp=E,e.isObject=A,e.isDate=S,e.isError=C,e.isFunction=I,e.isPrimitive=N,e.isBuffer=i("d60a");var M=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function L(){var t=new Date,e=[O(t.getHours()),O(t.getMinutes()),O(t.getSeconds())].join(":");return[t.getDate(),M[t.getMonth()],e].join(" ")}function F(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",L(),e.format.apply(e,arguments))},e.inherits=i("28a0"),e._extend=function(t,e){if(!e||!A(e))return t;var i=Object.keys(e),r=i.length;while(r--)t[i[r]]=e[i[r]];return t};var _="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function B(t,e){if(!t){var i=new Error("Promise was rejected with a falsy value");i.reason=t,t=i}return e(t)}function j(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');function i(){for(var i=[],r=0;r<arguments.length;r++)i.push(arguments[r]);var s=i.pop();if("function"!==typeof s)throw new TypeError("The last argument must be of type Function");var n=this,a=function(){return s.apply(n,arguments)};e.apply(this,i).then((function(e){t.nextTick(a,null,e)}),(function(e){t.nextTick(B,e,a)}))}return Object.setPrototypeOf(i,Object.getPrototypeOf(e)),Object.defineProperties(i,r(e)),i}e.promisify=function(t){if("function"!==typeof t)throw new TypeError('The "original" argument must be of type Function');if(_&&t[_]){var e=t[_];if("function"!==typeof e)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,_,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,i,r=new Promise((function(t,r){e=t,i=r})),s=[],n=0;n<arguments.length;n++)s.push(arguments[n]);s.push((function(t,r){t?i(t):e(r)}));try{t.apply(this,s)}catch(a){i(a)}return r}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),_&&Object.defineProperty(e,_,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,r(t))},e.promisify.custom=_,e.callbackify=j}).call(this,i("4362"))},"7a1a":function(t,e,i){(function(t,i){i(e)})(0,(function(t){"use strict";function e(t,e,i,r){var s,n=!1,a=0;function o(){s&&clearTimeout(s)}function l(){o(),n=!0}function c(){for(var l=arguments.length,c=new Array(l),h=0;h<l;h++)c[h]=arguments[h];var u=this,p=Date.now()-a;function d(){a=Date.now(),i.apply(u,c)}function f(){s=void 0}n||(r&&!s&&d(),o(),void 0===r&&p>t?d():!0!==e&&(s=setTimeout(r?f:d,void 0===r?t-p:t)))}return"boolean"!==typeof e&&(r=i,i=e,e=void 0),c.cancel=l,c}function i(t,i,r){return void 0===r?e(t,i,!1):e(t,r,!1!==i)}t.debounce=i,t.throttle=e,Object.defineProperty(t,"__esModule",{value:!0})}))},aa47:function(t,e,i){"use strict";
/**!
 * Sortable 1.10.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function r(t){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function s(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function n(){return n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},n.apply(this,arguments)}function a(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},r=Object.keys(i);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter((function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable})))),r.forEach((function(e){s(t,e,i[e])}))}return t}function o(t,e){if(null==t)return{};var i,r,s={},n=Object.keys(t);for(r=0;r<n.length;r++)i=n[r],e.indexOf(i)>=0||(s[i]=t[i]);return s}function l(t,e){if(null==t)return{};var i,r,s=o(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(r=0;r<n.length;r++)i=n[r],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(s[i]=t[i])}return s}function c(t){return h(t)||u(t)||p()}function h(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}function u(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance")}i.r(e),i.d(e,"MultiDrag",(function(){return je})),i.d(e,"Sortable",(function(){return Qt})),i.d(e,"Swap",(function(){return Ae}));var d="1.10.2";function f(t){if("undefined"!==typeof window&&window.navigator)return!!navigator.userAgent.match(t)}var m=f(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),y=f(/Edge/i),v=f(/firefox/i),g=f(/safari/i)&&!f(/chrome/i)&&!f(/android/i),x=f(/iP(ad|od|hone)/i),b=f(/chrome/i)&&f(/android/i),P={capture:!1,passive:!1};function k(t,e,i){t.addEventListener(e,i,!m&&P)}function T(t,e,i){t.removeEventListener(e,i,!m&&P)}function w(t,e){if(e){if(">"===e[0]&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(i){return!1}return!1}}function E(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function A(t,e,i,r){if(t){i=i||document;do{if(null!=e&&(">"===e[0]?t.parentNode===i&&w(t,e):w(t,e))||r&&t===i)return t;if(t===i)break}while(t=E(t))}return null}var S,C=/\s+/g;function I(t,e,i){if(t&&e)if(t.classList)t.classList[i?"add":"remove"](e);else{var r=(" "+t.className+" ").replace(C," ").replace(" "+e+" "," ");t.className=(r+(i?" "+e:"")).replace(C," ")}}function N(t,e,i){var r=t&&t.style;if(r){if(void 0===i)return document.defaultView&&document.defaultView.getComputedStyle?i=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(i=t.currentStyle),void 0===e?i:i[e];e in r||-1!==e.indexOf("webkit")||(e="-webkit-"+e),r[e]=i+("string"===typeof i?"":"px")}}function D(t,e){var i="";if("string"===typeof t)i=t;else do{var r=N(t,"transform");r&&"none"!==r&&(i=r+" "+i)}while(!e&&(t=t.parentNode));var s=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return s&&new s(i)}function O(t,e,i){if(t){var r=t.getElementsByTagName(e),s=0,n=r.length;if(i)for(;s<n;s++)i(r[s],s);return r}return[]}function M(){var t=document.scrollingElement;return t||document.documentElement}function L(t,e,i,r,s){if(t.getBoundingClientRect||t===window){var n,a,o,l,c,h,u;if(t!==window&&t!==M()?(n=t.getBoundingClientRect(),a=n.top,o=n.left,l=n.bottom,c=n.right,h=n.height,u=n.width):(a=0,o=0,l=window.innerHeight,c=window.innerWidth,h=window.innerHeight,u=window.innerWidth),(e||i)&&t!==window&&(s=s||t.parentNode,!m))do{if(s&&s.getBoundingClientRect&&("none"!==N(s,"transform")||i&&"static"!==N(s,"position"))){var p=s.getBoundingClientRect();a-=p.top+parseInt(N(s,"border-top-width")),o-=p.left+parseInt(N(s,"border-left-width")),l=a+n.height,c=o+n.width;break}}while(s=s.parentNode);if(r&&t!==window){var d=D(s||t),f=d&&d.a,y=d&&d.d;d&&(a/=y,o/=f,u/=f,h/=y,l=a+h,c=o+u)}return{top:a,left:o,bottom:l,right:c,width:u,height:h}}}function F(t,e,i){var r=z(t,!0),s=L(t)[e];while(r){var n=L(r)[i],a=void 0;if(a="top"===i||"left"===i?s>=n:s<=n,!a)return r;if(r===M())break;r=z(r,!1)}return!1}function _(t,e,i){var r=0,s=0,n=t.children;while(s<n.length){if("none"!==n[s].style.display&&n[s]!==Qt.ghost&&n[s]!==Qt.dragged&&A(n[s],i.draggable,t,!1)){if(r===e)return n[s];r++}s++}return null}function B(t,e){var i=t.lastElementChild;while(i&&(i===Qt.ghost||"none"===N(i,"display")||e&&!w(i,e)))i=i.previousElementSibling;return i||null}function j(t,e){var i=0;if(!t||!t.parentNode)return-1;while(t=t.previousElementSibling)"TEMPLATE"===t.nodeName.toUpperCase()||t===Qt.clone||e&&!w(t,e)||i++;return i}function R(t){var e=0,i=0,r=M();if(t)do{var s=D(t),n=s.a,a=s.d;e+=t.scrollLeft*n,i+=t.scrollTop*a}while(t!==r&&(t=t.parentNode));return[e,i]}function U(t,e){for(var i in t)if(t.hasOwnProperty(i))for(var r in e)if(e.hasOwnProperty(r)&&e[r]===t[i][r])return Number(i);return-1}function z(t,e){if(!t||!t.getBoundingClientRect)return M();var i=t,r=!1;do{if(i.clientWidth<i.scrollWidth||i.clientHeight<i.scrollHeight){var s=N(i);if(i.clientWidth<i.scrollWidth&&("auto"==s.overflowX||"scroll"==s.overflowX)||i.clientHeight<i.scrollHeight&&("auto"==s.overflowY||"scroll"==s.overflowY)){if(!i.getBoundingClientRect||i===document.body)return M();if(r||e)return i;r=!0}}}while(i=i.parentNode);return M()}function H(t,e){if(t&&e)for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function V(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function q(t,e){return function(){if(!S){var i=arguments,r=this;1===i.length?t.call(r,i[0]):t.apply(r,i),S=setTimeout((function(){S=void 0}),e)}}}function K(){clearTimeout(S),S=void 0}function X(t,e,i){t.scrollLeft+=e,t.scrollTop+=i}function W(t){var e=window.Polymer,i=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):i?i(t).clone(!0)[0]:t.cloneNode(!0)}function J(t,e){N(t,"position","absolute"),N(t,"top",e.top),N(t,"left",e.left),N(t,"width",e.width),N(t,"height",e.height)}function Y(t){N(t,"position",""),N(t,"top",""),N(t,"left",""),N(t,"width",""),N(t,"height","")}var G="Sortable"+(new Date).getTime();function $(){var t,e=[];return{captureAnimationState:function(){if(e=[],this.options.animation){var t=[].slice.call(this.el.children);t.forEach((function(t){if("none"!==N(t,"display")&&t!==Qt.ghost){e.push({target:t,rect:L(t)});var i=a({},e[e.length-1].rect);if(t.thisAnimationDuration){var r=D(t,!0);r&&(i.top-=r.f,i.left-=r.e)}t.fromRect=i}}))}},addAnimationState:function(t){e.push(t)},removeAnimationState:function(t){e.splice(U(e,{target:t}),1)},animateAll:function(i){var r=this;if(!this.options.animation)return clearTimeout(t),void("function"===typeof i&&i());var s=!1,n=0;e.forEach((function(t){var e=0,i=t.target,a=i.fromRect,o=L(i),l=i.prevFromRect,c=i.prevToRect,h=t.rect,u=D(i,!0);u&&(o.top-=u.f,o.left-=u.e),i.toRect=o,i.thisAnimationDuration&&V(l,o)&&!V(a,o)&&(h.top-o.top)/(h.left-o.left)===(a.top-o.top)/(a.left-o.left)&&(e=Z(h,l,c,r.options)),V(o,a)||(i.prevFromRect=a,i.prevToRect=o,e||(e=r.options.animation),r.animate(i,h,o,e)),e&&(s=!0,n=Math.max(n,e),clearTimeout(i.animationResetTimer),i.animationResetTimer=setTimeout((function(){i.animationTime=0,i.prevFromRect=null,i.fromRect=null,i.prevToRect=null,i.thisAnimationDuration=null}),e),i.thisAnimationDuration=e)})),clearTimeout(t),s?t=setTimeout((function(){"function"===typeof i&&i()}),n):"function"===typeof i&&i(),e=[]},animate:function(t,e,i,r){if(r){N(t,"transition",""),N(t,"transform","");var s=D(this.el),n=s&&s.a,a=s&&s.d,o=(e.left-i.left)/(n||1),l=(e.top-i.top)/(a||1);t.animatingX=!!o,t.animatingY=!!l,N(t,"transform","translate3d("+o+"px,"+l+"px,0)"),Q(t),N(t,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),N(t,"transform","translate3d(0,0,0)"),"number"===typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout((function(){N(t,"transition",""),N(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1}),r)}}}}function Q(t){return t.offsetWidth}function Z(t,e,i,r){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-i.top,2)+Math.pow(e.left-i.left,2))*r.animation}var tt=[],et={initializeByDefault:!0},it={mount:function(t){for(var e in et)et.hasOwnProperty(e)&&!(e in t)&&(t[e]=et[e]);tt.push(t)},pluginEvent:function(t,e,i){var r=this;this.eventCanceled=!1,i.cancel=function(){r.eventCanceled=!0};var s=t+"Global";tt.forEach((function(r){e[r.pluginName]&&(e[r.pluginName][s]&&e[r.pluginName][s](a({sortable:e},i)),e.options[r.pluginName]&&e[r.pluginName][t]&&e[r.pluginName][t](a({sortable:e},i)))}))},initializePlugins:function(t,e,i,r){for(var s in tt.forEach((function(r){var s=r.pluginName;if(t.options[s]||r.initializeByDefault){var a=new r(t,e,t.options);a.sortable=t,a.options=t.options,t[s]=a,n(i,a.defaults)}})),t.options)if(t.options.hasOwnProperty(s)){var a=this.modifyOption(t,s,t.options[s]);"undefined"!==typeof a&&(t.options[s]=a)}},getEventProperties:function(t,e){var i={};return tt.forEach((function(r){"function"===typeof r.eventProperties&&n(i,r.eventProperties.call(e[r.pluginName],t))})),i},modifyOption:function(t,e,i){var r;return tt.forEach((function(s){t[s.pluginName]&&s.optionListeners&&"function"===typeof s.optionListeners[e]&&(r=s.optionListeners[e].call(t[s.pluginName],i))})),r}};function rt(t){var e=t.sortable,i=t.rootEl,r=t.name,s=t.targetEl,n=t.cloneEl,o=t.toEl,l=t.fromEl,c=t.oldIndex,h=t.newIndex,u=t.oldDraggableIndex,p=t.newDraggableIndex,d=t.originalEvent,f=t.putSortable,v=t.extraEventProperties;if(e=e||i&&i[G],e){var g,x=e.options,b="on"+r.charAt(0).toUpperCase()+r.substr(1);!window.CustomEvent||m||y?(g=document.createEvent("Event"),g.initEvent(r,!0,!0)):g=new CustomEvent(r,{bubbles:!0,cancelable:!0}),g.to=o||i,g.from=l||i,g.item=s||i,g.clone=n,g.oldIndex=c,g.newIndex=h,g.oldDraggableIndex=u,g.newDraggableIndex=p,g.originalEvent=d,g.pullMode=f?f.lastPutMode:void 0;var P=a({},v,it.getEventProperties(r,e));for(var k in P)g[k]=P[k];i&&i.dispatchEvent(g),x[b]&&x[b].call(e,g)}}var st=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i.evt,s=l(i,["evt"]);it.pluginEvent.bind(Qt)(t,e,a({dragEl:at,parentEl:ot,ghostEl:lt,rootEl:ct,nextEl:ht,lastDownEl:ut,cloneEl:pt,cloneHidden:dt,dragStarted:At,putSortable:xt,activeSortable:Qt.active,originalEvent:r,oldIndex:ft,oldDraggableIndex:yt,newIndex:mt,newDraggableIndex:vt,hideGhostForTarget:Jt,unhideGhostForTarget:Yt,cloneNowHidden:function(){dt=!0},cloneNowShown:function(){dt=!1},dispatchSortableEvent:function(t){nt({sortable:e,name:t,originalEvent:r})}},s))};function nt(t){rt(a({putSortable:xt,cloneEl:pt,targetEl:at,rootEl:ct,oldIndex:ft,oldDraggableIndex:yt,newIndex:mt,newDraggableIndex:vt},t))}var at,ot,lt,ct,ht,ut,pt,dt,ft,mt,yt,vt,gt,xt,bt,Pt,kt,Tt,wt,Et,At,St,Ct,It,Nt,Dt=!1,Ot=!1,Mt=[],Lt=!1,Ft=!1,_t=[],Bt=!1,jt=[],Rt="undefined"!==typeof document,Ut=x,zt=y||m?"cssFloat":"float",Ht=Rt&&!b&&!x&&"draggable"in document.createElement("div"),Vt=function(){if(Rt){if(m)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}}(),qt=function(t,e){var i=N(t),r=parseInt(i.width)-parseInt(i.paddingLeft)-parseInt(i.paddingRight)-parseInt(i.borderLeftWidth)-parseInt(i.borderRightWidth),s=_(t,0,e),n=_(t,1,e),a=s&&N(s),o=n&&N(n),l=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+L(s).width,c=o&&parseInt(o.marginLeft)+parseInt(o.marginRight)+L(n).width;if("flex"===i.display)return"column"===i.flexDirection||"column-reverse"===i.flexDirection?"vertical":"horizontal";if("grid"===i.display)return i.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(s&&a["float"]&&"none"!==a["float"]){var h="left"===a["float"]?"left":"right";return!n||"both"!==o.clear&&o.clear!==h?"horizontal":"vertical"}return s&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||l>=r&&"none"===i[zt]||n&&"none"===i[zt]&&l+c>r)?"vertical":"horizontal"},Kt=function(t,e,i){var r=i?t.left:t.top,s=i?t.right:t.bottom,n=i?t.width:t.height,a=i?e.left:e.top,o=i?e.right:e.bottom,l=i?e.width:e.height;return r===a||s===o||r+n/2===a+l/2},Xt=function(t,e){var i;return Mt.some((function(r){if(!B(r)){var s=L(r),n=r[G].options.emptyInsertThreshold,a=t>=s.left-n&&t<=s.right+n,o=e>=s.top-n&&e<=s.bottom+n;return n&&a&&o?i=r:void 0}})),i},Wt=function(t){function e(t,i){return function(r,s,n,a){var o=r.options.group.name&&s.options.group.name&&r.options.group.name===s.options.group.name;if(null==t&&(i||o))return!0;if(null==t||!1===t)return!1;if(i&&"clone"===t)return t;if("function"===typeof t)return e(t(r,s,n,a),i)(r,s,n,a);var l=(i?r:s).options.group.name;return!0===t||"string"===typeof t&&t===l||t.join&&t.indexOf(l)>-1}}var i={},s=t.group;s&&"object"==r(s)||(s={name:s}),i.name=s.name,i.checkPull=e(s.pull,!0),i.checkPut=e(s.put),i.revertClone=s.revertClone,t.group=i},Jt=function(){!Vt&&lt&&N(lt,"display","none")},Yt=function(){!Vt&&lt&&N(lt,"display","")};Rt&&document.addEventListener("click",(function(t){if(Ot)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Ot=!1,!1}),!0);var Gt=function(t){if(at){t=t.touches?t.touches[0]:t;var e=Xt(t.clientX,t.clientY);if(e){var i={};for(var r in t)t.hasOwnProperty(r)&&(i[r]=t[r]);i.target=i.rootEl=e,i.preventDefault=void 0,i.stopPropagation=void 0,e[G]._onDragOver(i)}}},$t=function(t){at&&at.parentNode[G]._isOutsideThisEl(t.target)};function Qt(t,e){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=n({},e),t[G]=this;var i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return qt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Qt.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var r in it.initializePlugins(this,t,i),i)!(r in e)&&(e[r]=i[r]);for(var s in Wt(e),this)"_"===s.charAt(0)&&"function"===typeof this[s]&&(this[s]=this[s].bind(this));this.nativeDraggable=!e.forceFallback&&Ht,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?k(t,"pointerdown",this._onTapStart):(k(t,"mousedown",this._onTapStart),k(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(k(t,"dragover",this),k(t,"dragenter",this)),Mt.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),n(this,$())}function Zt(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function te(t,e,i,r,s,n,a,o){var l,c,h=t[G],u=h.options.onMove;return!window.CustomEvent||m||y?(l=document.createEvent("Event"),l.initEvent("move",!0,!0)):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=e,l.from=t,l.dragged=i,l.draggedRect=r,l.related=s||e,l.relatedRect=n||L(e),l.willInsertAfter=o,l.originalEvent=a,t.dispatchEvent(l),u&&(c=u.call(h,l,a)),c}function ee(t){t.draggable=!1}function ie(){Bt=!1}function re(t,e,i){var r=L(B(i.el,i.options.draggable)),s=10;return e?t.clientX>r.right+s||t.clientX<=r.right&&t.clientY>r.bottom&&t.clientX>=r.left:t.clientX>r.right&&t.clientY>r.top||t.clientX<=r.right&&t.clientY>r.bottom+s}function se(t,e,i,r,s,n,a,o){var l=r?t.clientY:t.clientX,c=r?i.height:i.width,h=r?i.top:i.left,u=r?i.bottom:i.right,p=!1;if(!a)if(o&&It<c*s){if(!Lt&&(1===Ct?l>h+c*n/2:l<u-c*n/2)&&(Lt=!0),Lt)p=!0;else if(1===Ct?l<h+It:l>u-It)return-Ct}else if(l>h+c*(1-s)/2&&l<u-c*(1-s)/2)return ne(e);return p=p||a,p&&(l<h+c*n/2||l>u-c*n/2)?l>h+c/2?1:-1:0}function ne(t){return j(at)<j(t)?1:-1}function ae(t){var e=t.tagName+t.className+t.src+t.href+t.textContent,i=e.length,r=0;while(i--)r+=e.charCodeAt(i);return r.toString(36)}function oe(t){jt.length=0;var e=t.getElementsByTagName("input"),i=e.length;while(i--){var r=e[i];r.checked&&jt.push(r)}}function le(t){return setTimeout(t,0)}function ce(t){return clearTimeout(t)}Qt.prototype={constructor:Qt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(St=null)},_getDirection:function(t,e){return"function"===typeof this.options.direction?this.options.direction.call(this,t,e,at):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,i=this.el,r=this.options,s=r.preventOnFilter,n=t.type,a=t.touches&&t.touches[0]||t.pointerType&&"touch"===t.pointerType&&t,o=(a||t).target,l=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||o,c=r.filter;if(oe(i),!at&&!(/mousedown|pointerdown/.test(n)&&0!==t.button||r.disabled)&&!l.isContentEditable&&(o=A(o,r.draggable,i,!1),(!o||!o.animated)&&ut!==o)){if(ft=j(o),yt=j(o,r.draggable),"function"===typeof c){if(c.call(this,t,o,this))return nt({sortable:e,rootEl:l,name:"filter",targetEl:o,toEl:i,fromEl:i}),st("filter",e,{evt:t}),void(s&&t.cancelable&&t.preventDefault())}else if(c&&(c=c.split(",").some((function(r){if(r=A(l,r.trim(),i,!1),r)return nt({sortable:e,rootEl:r,name:"filter",targetEl:o,fromEl:i,toEl:i}),st("filter",e,{evt:t}),!0})),c))return void(s&&t.cancelable&&t.preventDefault());r.handle&&!A(l,r.handle,i,!1)||this._prepareDragStart(t,a,o)}}},_prepareDragStart:function(t,e,i){var r,s=this,n=s.el,a=s.options,o=n.ownerDocument;if(i&&!at&&i.parentNode===n){var l=L(i);if(ct=n,at=i,ot=at.parentNode,ht=at.nextSibling,ut=i,gt=a.group,Qt.dragged=at,bt={target:at,clientX:(e||t).clientX,clientY:(e||t).clientY},wt=bt.clientX-l.left,Et=bt.clientY-l.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,at.style["will-change"]="all",r=function(){st("delayEnded",s,{evt:t}),Qt.eventCanceled?s._onDrop():(s._disableDelayedDragEvents(),!v&&s.nativeDraggable&&(at.draggable=!0),s._triggerDragStart(t,e),nt({sortable:s,name:"choose",originalEvent:t}),I(at,a.chosenClass,!0))},a.ignore.split(",").forEach((function(t){O(at,t.trim(),ee)})),k(o,"dragover",Gt),k(o,"mousemove",Gt),k(o,"touchmove",Gt),k(o,"mouseup",s._onDrop),k(o,"touchend",s._onDrop),k(o,"touchcancel",s._onDrop),v&&this.nativeDraggable&&(this.options.touchStartThreshold=4,at.draggable=!0),st("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!e||this.nativeDraggable&&(y||m))r();else{if(Qt.eventCanceled)return void this._onDrop();k(o,"mouseup",s._disableDelayedDrag),k(o,"touchend",s._disableDelayedDrag),k(o,"touchcancel",s._disableDelayedDrag),k(o,"mousemove",s._delayedDragTouchMoveHandler),k(o,"touchmove",s._delayedDragTouchMoveHandler),a.supportPointer&&k(o,"pointermove",s._delayedDragTouchMoveHandler),s._dragStartTimer=setTimeout(r,a.delay)}}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){at&&ee(at),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;T(t,"mouseup",this._disableDelayedDrag),T(t,"touchend",this._disableDelayedDrag),T(t,"touchcancel",this._disableDelayedDrag),T(t,"mousemove",this._delayedDragTouchMoveHandler),T(t,"touchmove",this._delayedDragTouchMoveHandler),T(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||"touch"==t.pointerType&&t,!this.nativeDraggable||e?this.options.supportPointer?k(document,"pointermove",this._onTouchMove):k(document,e?"touchmove":"mousemove",this._onTouchMove):(k(at,"dragend",this),k(ct,"dragstart",this._onDragStart));try{document.selection?le((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(i){}},_dragStarted:function(t,e){if(Dt=!1,ct&&at){st("dragStarted",this,{evt:e}),this.nativeDraggable&&k(document,"dragover",$t);var i=this.options;!t&&I(at,i.dragClass,!1),I(at,i.ghostClass,!0),Qt.active=this,t&&this._appendGhost(),nt({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(Pt){this._lastX=Pt.clientX,this._lastY=Pt.clientY,Jt();var t=document.elementFromPoint(Pt.clientX,Pt.clientY),e=t;while(t&&t.shadowRoot){if(t=t.shadowRoot.elementFromPoint(Pt.clientX,Pt.clientY),t===e)break;e=t}if(at.parentNode[G]._isOutsideThisEl(t),e)do{if(e[G]){var i=void 0;if(i=e[G]._onDragOver({clientX:Pt.clientX,clientY:Pt.clientY,target:t,rootEl:e}),i&&!this.options.dragoverBubble)break}t=e}while(e=e.parentNode);Yt()}},_onTouchMove:function(t){if(bt){var e=this.options,i=e.fallbackTolerance,r=e.fallbackOffset,s=t.touches?t.touches[0]:t,n=lt&&D(lt,!0),a=lt&&n&&n.a,o=lt&&n&&n.d,l=Ut&&Nt&&R(Nt),c=(s.clientX-bt.clientX+r.x)/(a||1)+(l?l[0]-_t[0]:0)/(a||1),h=(s.clientY-bt.clientY+r.y)/(o||1)+(l?l[1]-_t[1]:0)/(o||1);if(!Qt.active&&!Dt){if(i&&Math.max(Math.abs(s.clientX-this._lastX),Math.abs(s.clientY-this._lastY))<i)return;this._onDragStart(t,!0)}if(lt){n?(n.e+=c-(kt||0),n.f+=h-(Tt||0)):n={a:1,b:0,c:0,d:1,e:c,f:h};var u="matrix(".concat(n.a,",").concat(n.b,",").concat(n.c,",").concat(n.d,",").concat(n.e,",").concat(n.f,")");N(lt,"webkitTransform",u),N(lt,"mozTransform",u),N(lt,"msTransform",u),N(lt,"transform",u),kt=c,Tt=h,Pt=s}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!lt){var t=this.options.fallbackOnBody?document.body:ct,e=L(at,!0,Ut,!0,t),i=this.options;if(Ut){Nt=t;while("static"===N(Nt,"position")&&"none"===N(Nt,"transform")&&Nt!==document)Nt=Nt.parentNode;Nt!==document.body&&Nt!==document.documentElement?(Nt===document&&(Nt=M()),e.top+=Nt.scrollTop,e.left+=Nt.scrollLeft):Nt=M(),_t=R(Nt)}lt=at.cloneNode(!0),I(lt,i.ghostClass,!1),I(lt,i.fallbackClass,!0),I(lt,i.dragClass,!0),N(lt,"transition",""),N(lt,"transform",""),N(lt,"box-sizing","border-box"),N(lt,"margin",0),N(lt,"top",e.top),N(lt,"left",e.left),N(lt,"width",e.width),N(lt,"height",e.height),N(lt,"opacity","0.8"),N(lt,"position",Ut?"absolute":"fixed"),N(lt,"zIndex","100000"),N(lt,"pointerEvents","none"),Qt.ghost=lt,t.appendChild(lt),N(lt,"transform-origin",wt/parseInt(lt.style.width)*100+"% "+Et/parseInt(lt.style.height)*100+"%")}},_onDragStart:function(t,e){var i=this,r=t.dataTransfer,s=i.options;st("dragStart",this,{evt:t}),Qt.eventCanceled?this._onDrop():(st("setupClone",this),Qt.eventCanceled||(pt=W(at),pt.draggable=!1,pt.style["will-change"]="",this._hideClone(),I(pt,this.options.chosenClass,!1),Qt.clone=pt),i.cloneId=le((function(){st("clone",i),Qt.eventCanceled||(i.options.removeCloneOnHide||ct.insertBefore(pt,at),i._hideClone(),nt({sortable:i,name:"clone"}))})),!e&&I(at,s.dragClass,!0),e?(Ot=!0,i._loopId=setInterval(i._emulateDragOver,50)):(T(document,"mouseup",i._onDrop),T(document,"touchend",i._onDrop),T(document,"touchcancel",i._onDrop),r&&(r.effectAllowed="move",s.setData&&s.setData.call(i,r,at)),k(document,"drop",i),N(at,"transform","translateZ(0)")),Dt=!0,i._dragStartId=le(i._dragStarted.bind(i,e,t)),k(document,"selectstart",i),At=!0,g&&N(document.body,"user-select","none"))},_onDragOver:function(t){var e,i,r,s,n=this.el,o=t.target,l=this.options,c=l.group,h=Qt.active,u=gt===c,p=l.sort,d=xt||h,f=this,m=!1;if(!Bt){if(void 0!==t.preventDefault&&t.cancelable&&t.preventDefault(),o=A(o,l.draggable,n,!0),O("dragOver"),Qt.eventCanceled)return m;if(at.contains(t.target)||o.animated&&o.animatingX&&o.animatingY||f._ignoreWhileAnimating===o)return _(!1);if(Ot=!1,h&&!l.disabled&&(u?p||(r=!ct.contains(at)):xt===this||(this.lastPutMode=gt.checkPull(this,h,at,t))&&c.checkPut(this,h,at,t))){if(s="vertical"===this._getDirection(t,o),e=L(at),O("dragOverValid"),Qt.eventCanceled)return m;if(r)return ot=ct,M(),this._hideClone(),O("revert"),Qt.eventCanceled||(ht?ct.insertBefore(at,ht):ct.appendChild(at)),_(!0);var y=B(n,l.draggable);if(!y||re(t,s,this)&&!y.animated){if(y===at)return _(!1);if(y&&n===t.target&&(o=y),o&&(i=L(o)),!1!==te(ct,n,at,e,o,i,t,!!o))return M(),n.appendChild(at),ot=n,R(),_(!0)}else if(o.parentNode===n){i=L(o);var v,g,x=0,b=at.parentNode!==n,P=!Kt(at.animated&&at.toRect||e,o.animated&&o.toRect||i,s),k=s?"top":"left",T=F(o,"top","top")||F(at,"top","top"),w=T?T.scrollTop:void 0;if(St!==o&&(v=i[k],Lt=!1,Ft=!P&&l.invertSwap||b),x=se(t,o,i,s,P?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,Ft,St===o),0!==x){var E=j(at);do{E-=x,g=ot.children[E]}while(g&&("none"===N(g,"display")||g===lt))}if(0===x||g===o)return _(!1);St=o,Ct=x;var S=o.nextElementSibling,C=!1;C=1===x;var D=te(ct,n,at,e,o,i,t,C);if(!1!==D)return 1!==D&&-1!==D||(C=1===D),Bt=!0,setTimeout(ie,30),M(),C&&!S?n.appendChild(at):o.parentNode.insertBefore(at,C?S:o),T&&X(T,0,w-T.scrollTop),ot=at.parentNode,void 0===v||Ft||(It=Math.abs(v-L(o)[k])),R(),_(!0)}if(n.contains(at))return _(!1)}return!1}function O(l,c){st(l,f,a({evt:t,isOwner:u,axis:s?"vertical":"horizontal",revert:r,dragRect:e,targetRect:i,canSort:p,fromSortable:d,target:o,completed:_,onMove:function(i,r){return te(ct,n,at,e,i,L(i),t,r)},changed:R},c))}function M(){O("dragOverAnimationCapture"),f.captureAnimationState(),f!==d&&d.captureAnimationState()}function _(e){return O("dragOverCompleted",{insertion:e}),e&&(u?h._hideClone():h._showClone(f),f!==d&&(I(at,xt?xt.options.ghostClass:h.options.ghostClass,!1),I(at,l.ghostClass,!0)),xt!==f&&f!==Qt.active?xt=f:f===Qt.active&&xt&&(xt=null),d===f&&(f._ignoreWhileAnimating=o),f.animateAll((function(){O("dragOverAnimationComplete"),f._ignoreWhileAnimating=null})),f!==d&&(d.animateAll(),d._ignoreWhileAnimating=null)),(o===at&&!at.animated||o===n&&!o.animated)&&(St=null),l.dragoverBubble||t.rootEl||o===document||(at.parentNode[G]._isOutsideThisEl(t.target),!e&&Gt(t)),!l.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),m=!0}function R(){mt=j(at),vt=j(at,l.draggable),nt({sortable:f,name:"change",toEl:n,newIndex:mt,newDraggableIndex:vt,originalEvent:t})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){T(document,"mousemove",this._onTouchMove),T(document,"touchmove",this._onTouchMove),T(document,"pointermove",this._onTouchMove),T(document,"dragover",Gt),T(document,"mousemove",Gt),T(document,"touchmove",Gt)},_offUpEvents:function(){var t=this.el.ownerDocument;T(t,"mouseup",this._onDrop),T(t,"touchend",this._onDrop),T(t,"pointerup",this._onDrop),T(t,"touchcancel",this._onDrop),T(document,"selectstart",this)},_onDrop:function(t){var e=this.el,i=this.options;mt=j(at),vt=j(at,i.draggable),st("drop",this,{evt:t}),ot=at&&at.parentNode,mt=j(at),vt=j(at,i.draggable),Qt.eventCanceled||(Dt=!1,Ft=!1,Lt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ce(this.cloneId),ce(this._dragStartId),this.nativeDraggable&&(T(document,"drop",this),T(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),g&&N(document.body,"user-select",""),N(at,"transform",""),t&&(At&&(t.cancelable&&t.preventDefault(),!i.dropBubble&&t.stopPropagation()),lt&&lt.parentNode&&lt.parentNode.removeChild(lt),(ct===ot||xt&&"clone"!==xt.lastPutMode)&&pt&&pt.parentNode&&pt.parentNode.removeChild(pt),at&&(this.nativeDraggable&&T(at,"dragend",this),ee(at),at.style["will-change"]="",At&&!Dt&&I(at,xt?xt.options.ghostClass:this.options.ghostClass,!1),I(at,this.options.chosenClass,!1),nt({sortable:this,name:"unchoose",toEl:ot,newIndex:null,newDraggableIndex:null,originalEvent:t}),ct!==ot?(mt>=0&&(nt({rootEl:ot,name:"add",toEl:ot,fromEl:ct,originalEvent:t}),nt({sortable:this,name:"remove",toEl:ot,originalEvent:t}),nt({rootEl:ot,name:"sort",toEl:ot,fromEl:ct,originalEvent:t}),nt({sortable:this,name:"sort",toEl:ot,originalEvent:t})),xt&&xt.save()):mt!==ft&&mt>=0&&(nt({sortable:this,name:"update",toEl:ot,originalEvent:t}),nt({sortable:this,name:"sort",toEl:ot,originalEvent:t})),Qt.active&&(null!=mt&&-1!==mt||(mt=ft,vt=yt),nt({sortable:this,name:"end",toEl:ot,originalEvent:t}),this.save())))),this._nulling()},_nulling:function(){st("nulling",this),ct=at=ot=lt=ht=pt=ut=dt=bt=Pt=At=mt=vt=ft=yt=St=Ct=xt=gt=Qt.dragged=Qt.ghost=Qt.clone=Qt.active=null,jt.forEach((function(t){t.checked=!0})),jt.length=kt=Tt=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":at&&(this._onDragOver(t),Zt(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t,e=[],i=this.el.children,r=0,s=i.length,n=this.options;r<s;r++)t=i[r],A(t,n.draggable,this.el,!1)&&e.push(t.getAttribute(n.dataIdAttr)||ae(t));return e},sort:function(t){var e={},i=this.el;this.toArray().forEach((function(t,r){var s=i.children[r];A(s,this.options.draggable,i,!1)&&(e[t]=s)}),this),t.forEach((function(t){e[t]&&(i.removeChild(e[t]),i.appendChild(e[t]))}))},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return A(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var i=this.options;if(void 0===e)return i[t];var r=it.modifyOption(this,t,e);i[t]="undefined"!==typeof r?r:e,"group"===t&&Wt(i)},destroy:function(){st("destroy",this);var t=this.el;t[G]=null,T(t,"mousedown",this._onTapStart),T(t,"touchstart",this._onTapStart),T(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(T(t,"dragover",this),T(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),(function(t){t.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),Mt.splice(Mt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!dt){if(st("hideClone",this),Qt.eventCanceled)return;N(pt,"display","none"),this.options.removeCloneOnHide&&pt.parentNode&&pt.parentNode.removeChild(pt),dt=!0}},_showClone:function(t){if("clone"===t.lastPutMode){if(dt){if(st("showClone",this),Qt.eventCanceled)return;ct.contains(at)&&!this.options.group.revertClone?ct.insertBefore(pt,at):ht?ct.insertBefore(pt,ht):ct.appendChild(pt),this.options.group.revertClone&&this.animate(at,pt),N(pt,"display",""),dt=!1}}else this._hideClone()}},Rt&&k(document,"touchmove",(function(t){(Qt.active||Dt)&&t.cancelable&&t.preventDefault()})),Qt.utils={on:k,off:T,css:N,find:O,is:function(t,e){return!!A(t,e,t,!1)},extend:H,throttle:q,closest:A,toggleClass:I,clone:W,index:j,nextTick:le,cancelNextTick:ce,detectDirection:qt,getChild:_},Qt.get=function(t){return t[G]},Qt.mount=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];e[0].constructor===Array&&(e=e[0]),e.forEach((function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(Qt.utils=a({},Qt.utils,t.utils)),it.mount(t)}))},Qt.create=function(t,e){return new Qt(t,e)},Qt.version=d;var he,ue,pe,de,fe,me,ye=[],ve=!1;function ge(){function t(){for(var t in this.defaults={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"===typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?k(document,"dragover",this._handleAutoScroll):this.options.supportPointer?k(document,"pointermove",this._handleFallbackAutoScroll):e.touches?k(document,"touchmove",this._handleFallbackAutoScroll):k(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?T(document,"dragover",this._handleAutoScroll):(T(document,"pointermove",this._handleFallbackAutoScroll),T(document,"touchmove",this._handleFallbackAutoScroll),T(document,"mousemove",this._handleFallbackAutoScroll)),be(),xe(),K()},nulling:function(){fe=ue=he=ve=me=pe=de=null,ye.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var i=this,r=(t.touches?t.touches[0]:t).clientX,s=(t.touches?t.touches[0]:t).clientY,n=document.elementFromPoint(r,s);if(fe=t,e||y||m||g){ke(t,this.options,n,e);var a=z(n,!0);!ve||me&&r===pe&&s===de||(me&&be(),me=setInterval((function(){var n=z(document.elementFromPoint(r,s),!0);n!==a&&(a=n,xe()),ke(t,i.options,n,e)}),10),pe=r,de=s)}else{if(!this.options.bubbleScroll||z(n,!0)===M())return void xe();ke(t,this.options,z(n,!1),!1)}}},n(t,{pluginName:"scroll",initializeByDefault:!0})}function xe(){ye.forEach((function(t){clearInterval(t.pid)})),ye=[]}function be(){clearInterval(me)}var Pe,ke=q((function(t,e,i,r){if(e.scroll){var s,n=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,o=e.scrollSensitivity,l=e.scrollSpeed,c=M(),h=!1;ue!==i&&(ue=i,xe(),he=e.scroll,s=e.scrollFn,!0===he&&(he=z(i,!0)));var u=0,p=he;do{var d=p,f=L(d),m=f.top,y=f.bottom,v=f.left,g=f.right,x=f.width,b=f.height,P=void 0,k=void 0,T=d.scrollWidth,w=d.scrollHeight,E=N(d),A=d.scrollLeft,S=d.scrollTop;d===c?(P=x<T&&("auto"===E.overflowX||"scroll"===E.overflowX||"visible"===E.overflowX),k=b<w&&("auto"===E.overflowY||"scroll"===E.overflowY||"visible"===E.overflowY)):(P=x<T&&("auto"===E.overflowX||"scroll"===E.overflowX),k=b<w&&("auto"===E.overflowY||"scroll"===E.overflowY));var C=P&&(Math.abs(g-n)<=o&&A+x<T)-(Math.abs(v-n)<=o&&!!A),I=k&&(Math.abs(y-a)<=o&&S+b<w)-(Math.abs(m-a)<=o&&!!S);if(!ye[u])for(var D=0;D<=u;D++)ye[D]||(ye[D]={});ye[u].vx==C&&ye[u].vy==I&&ye[u].el===d||(ye[u].el=d,ye[u].vx=C,ye[u].vy=I,clearInterval(ye[u].pid),0==C&&0==I||(h=!0,ye[u].pid=setInterval(function(){r&&0===this.layer&&Qt.active._onTouchMove(fe);var e=ye[this.layer].vy?ye[this.layer].vy*l:0,i=ye[this.layer].vx?ye[this.layer].vx*l:0;"function"===typeof s&&"continue"!==s.call(Qt.dragged.parentNode[G],i,e,t,fe,ye[this.layer].el)||X(ye[this.layer].el,i,e)}.bind({layer:u}),24))),u++}while(e.bubbleScroll&&p!==c&&(p=z(p,!1)));ve=h}}),30),Te=function(t){var e=t.originalEvent,i=t.putSortable,r=t.dragEl,s=t.activeSortable,n=t.dispatchSortableEvent,a=t.hideGhostForTarget,o=t.unhideGhostForTarget;if(e){var l=i||s;a();var c=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,h=document.elementFromPoint(c.clientX,c.clientY);o(),l&&!l.el.contains(h)&&(n("spill"),this.onSpill({dragEl:r,putSortable:i}))}};function we(){}function Ee(){}function Ae(){function t(){this.defaults={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){var e=t.dragEl;Pe=e},dragOverValid:function(t){var e=t.completed,i=t.target,r=t.onMove,s=t.activeSortable,n=t.changed,a=t.cancel;if(s.options.swap){var o=this.sortable.el,l=this.options;if(i&&i!==o){var c=Pe;!1!==r(i)?(I(i,l.swapClass,!0),Pe=i):Pe=null,c&&c!==Pe&&I(c,l.swapClass,!1)}n(),e(!0),a()}},drop:function(t){var e=t.activeSortable,i=t.putSortable,r=t.dragEl,s=i||this.sortable,n=this.options;Pe&&I(Pe,n.swapClass,!1),Pe&&(n.swap||i&&i.options.swap)&&r!==Pe&&(s.captureAnimationState(),s!==e&&e.captureAnimationState(),Se(r,Pe),s.animateAll(),s!==e&&e.animateAll())},nulling:function(){Pe=null}},n(t,{pluginName:"swap",eventProperties:function(){return{swapItem:Pe}}})}function Se(t,e){var i,r,s=t.parentNode,n=e.parentNode;s&&n&&!s.isEqualNode(e)&&!n.isEqualNode(t)&&(i=j(t),r=j(e),s.isEqualNode(n)&&i<r&&r++,s.insertBefore(e,s.children[i]),n.insertBefore(t,n.children[r]))}we.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,i=t.putSortable;this.sortable.captureAnimationState(),i&&i.captureAnimationState();var r=_(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(e,r):this.sortable.el.appendChild(e),this.sortable.animateAll(),i&&i.animateAll()},drop:Te},n(we,{pluginName:"revertOnSpill"}),Ee.prototype={onSpill:function(t){var e=t.dragEl,i=t.putSortable,r=i||this.sortable;r.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),r.animateAll()},drop:Te},n(Ee,{pluginName:"removeOnSpill"});var Ce,Ie,Ne,De,Oe,Me=[],Le=[],Fe=!1,_e=!1,Be=!1;function je(){function t(t){for(var e in this)"_"===e.charAt(0)&&"function"===typeof this[e]&&(this[e]=this[e].bind(this));t.options.supportPointer?k(document,"pointerup",this._deselectMultiDrag):(k(document,"mouseup",this._deselectMultiDrag),k(document,"touchend",this._deselectMultiDrag)),k(document,"keydown",this._checkKeyDown),k(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(e,i){var r="";Me.length&&Ie===t?Me.forEach((function(t,e){r+=(e?", ":"")+t.textContent})):r=i.textContent,e.setData("Text",r)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var e=t.dragEl;Ne=e},delayEnded:function(){this.isMultiDrag=~Me.indexOf(Ne)},setupClone:function(t){var e=t.sortable,i=t.cancel;if(this.isMultiDrag){for(var r=0;r<Me.length;r++)Le.push(W(Me[r])),Le[r].sortableIndex=Me[r].sortableIndex,Le[r].draggable=!1,Le[r].style["will-change"]="",I(Le[r],this.options.selectedClass,!1),Me[r]===Ne&&I(Le[r],this.options.chosenClass,!1);e._hideClone(),i()}},clone:function(t){var e=t.sortable,i=t.rootEl,r=t.dispatchSortableEvent,s=t.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||Me.length&&Ie===e&&(Ue(!0,i),r("clone"),s()))},showClone:function(t){var e=t.cloneNowShown,i=t.rootEl,r=t.cancel;this.isMultiDrag&&(Ue(!1,i),Le.forEach((function(t){N(t,"display","")})),e(),Oe=!1,r())},hideClone:function(t){var e=this,i=(t.sortable,t.cloneNowHidden),r=t.cancel;this.isMultiDrag&&(Le.forEach((function(t){N(t,"display","none"),e.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)})),i(),Oe=!0,r())},dragStartGlobal:function(t){t.sortable;!this.isMultiDrag&&Ie&&Ie.multiDrag._deselectMultiDrag(),Me.forEach((function(t){t.sortableIndex=j(t)})),Me=Me.sort((function(t,e){return t.sortableIndex-e.sortableIndex})),Be=!0},dragStarted:function(t){var e=this,i=t.sortable;if(this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){Me.forEach((function(t){t!==Ne&&N(t,"position","absolute")}));var r=L(Ne,!1,!0,!0);Me.forEach((function(t){t!==Ne&&J(t,r)})),_e=!0,Fe=!0}i.animateAll((function(){_e=!1,Fe=!1,e.options.animation&&Me.forEach((function(t){Y(t)})),e.options.sort&&ze()}))}},dragOver:function(t){var e=t.target,i=t.completed,r=t.cancel;_e&&~Me.indexOf(e)&&(i(!1),r())},revert:function(t){var e=t.fromSortable,i=t.rootEl,r=t.sortable,s=t.dragRect;Me.length>1&&(Me.forEach((function(t){r.addAnimationState({target:t,rect:_e?L(t):s}),Y(t),t.fromRect=s,e.removeAnimationState(t)})),_e=!1,Re(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(t){var e=t.sortable,i=t.isOwner,r=t.insertion,s=t.activeSortable,n=t.parentEl,a=t.putSortable,o=this.options;if(r){if(i&&s._hideClone(),Fe=!1,o.animation&&Me.length>1&&(_e||!i&&!s.options.sort&&!a)){var l=L(Ne,!1,!0,!0);Me.forEach((function(t){t!==Ne&&(J(t,l),n.appendChild(t))})),_e=!0}if(!i)if(_e||ze(),Me.length>1){var c=Oe;s._showClone(e),s.options.animation&&!Oe&&c&&Le.forEach((function(t){s.addAnimationState({target:t,rect:De}),t.fromRect=De,t.thisAnimationDuration=null}))}else s._showClone(e)}},dragOverAnimationCapture:function(t){var e=t.dragRect,i=t.isOwner,r=t.activeSortable;if(Me.forEach((function(t){t.thisAnimationDuration=null})),r.options.animation&&!i&&r.multiDrag.isMultiDrag){De=n({},e);var s=D(Ne,!0);De.top-=s.f,De.left-=s.e}},dragOverAnimationComplete:function(){_e&&(_e=!1,ze())},drop:function(t){var e=t.originalEvent,i=t.rootEl,r=t.parentEl,s=t.sortable,n=t.dispatchSortableEvent,a=t.oldIndex,o=t.putSortable,l=o||this.sortable;if(e){var c=this.options,h=r.children;if(!Be)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),I(Ne,c.selectedClass,!~Me.indexOf(Ne)),~Me.indexOf(Ne))Me.splice(Me.indexOf(Ne),1),Ce=null,rt({sortable:s,rootEl:i,name:"deselect",targetEl:Ne,originalEvt:e});else{if(Me.push(Ne),rt({sortable:s,rootEl:i,name:"select",targetEl:Ne,originalEvt:e}),e.shiftKey&&Ce&&s.el.contains(Ce)){var u,p,d=j(Ce),f=j(Ne);if(~d&&~f&&d!==f)for(f>d?(p=d,u=f):(p=f,u=d+1);p<u;p++)~Me.indexOf(h[p])||(I(h[p],c.selectedClass,!0),Me.push(h[p]),rt({sortable:s,rootEl:i,name:"select",targetEl:h[p],originalEvt:e}))}else Ce=Ne;Ie=l}if(Be&&this.isMultiDrag){if((r[G].options.sort||r!==i)&&Me.length>1){var m=L(Ne),y=j(Ne,":not(."+this.options.selectedClass+")");if(!Fe&&c.animation&&(Ne.thisAnimationDuration=null),l.captureAnimationState(),!Fe&&(c.animation&&(Ne.fromRect=m,Me.forEach((function(t){if(t.thisAnimationDuration=null,t!==Ne){var e=_e?L(t):m;t.fromRect=e,l.addAnimationState({target:t,rect:e})}}))),ze(),Me.forEach((function(t){h[y]?r.insertBefore(t,h[y]):r.appendChild(t),y++})),a===j(Ne))){var v=!1;Me.forEach((function(t){t.sortableIndex===j(t)||(v=!0)})),v&&n("update")}Me.forEach((function(t){Y(t)})),l.animateAll()}Ie=l}(i===r||o&&"clone"!==o.lastPutMode)&&Le.forEach((function(t){t.parentNode&&t.parentNode.removeChild(t)}))}},nullingGlobal:function(){this.isMultiDrag=Be=!1,Le.length=0},destroyGlobal:function(){this._deselectMultiDrag(),T(document,"pointerup",this._deselectMultiDrag),T(document,"mouseup",this._deselectMultiDrag),T(document,"touchend",this._deselectMultiDrag),T(document,"keydown",this._checkKeyDown),T(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(("undefined"===typeof Be||!Be)&&Ie===this.sortable&&(!t||!A(t.target,this.options.draggable,this.sortable.el,!1))&&(!t||0===t.button))while(Me.length){var e=Me[0];I(e,this.options.selectedClass,!1),Me.shift(),rt({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvt:t})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},n(t,{pluginName:"multiDrag",utils:{select:function(t){var e=t.parentNode[G];e&&e.options.multiDrag&&!~Me.indexOf(t)&&(Ie&&Ie!==e&&(Ie.multiDrag._deselectMultiDrag(),Ie=e),I(t,e.options.selectedClass,!0),Me.push(t))},deselect:function(t){var e=t.parentNode[G],i=Me.indexOf(t);e&&e.options.multiDrag&&~i&&(I(t,e.options.selectedClass,!1),Me.splice(i,1))}},eventProperties:function(){var t=this,e=[],i=[];return Me.forEach((function(r){var s;e.push({multiDragElement:r,index:r.sortableIndex}),s=_e&&r!==Ne?-1:_e?j(r,":not(."+t.options.selectedClass+")"):j(r),i.push({multiDragElement:r,index:s})})),{items:c(Me),clones:[].concat(Le),oldIndicies:e,newIndicies:i}},optionListeners:{multiDragKey:function(t){return t=t.toLowerCase(),"ctrl"===t?t="Control":t.length>1&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}function Re(t,e){Me.forEach((function(i,r){var s=e.children[i.sortableIndex+(t?Number(r):0)];s?e.insertBefore(i,s):e.appendChild(i)}))}function Ue(t,e){Le.forEach((function(i,r){var s=e.children[i.sortableIndex+(t?Number(r):0)];s?e.insertBefore(i,s):e.appendChild(i)}))}function ze(){Me.forEach((function(t){t!==Ne&&t.parentNode&&t.parentNode.removeChild(t)}))}Qt.mount(new ge),Qt.mount(Ee,we),e["default"]=Qt},b311:function(t,e,i){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
(function(e,i){t.exports=i()})(0,(function(){return function(){var t={686:function(t,e,i){"use strict";i.d(e,{default:function(){return O}});var r=i(279),s=i.n(r),n=i(370),a=i.n(n),o=i(817),l=i.n(o);function c(t){try{return document.execCommand(t)}catch(e){return!1}}var h=function(t){var e=l()(t);return c("cut"),e},u=h;function p(t){var e="rtl"===document.documentElement.getAttribute("dir"),i=document.createElement("textarea");i.style.fontSize="12pt",i.style.border="0",i.style.padding="0",i.style.margin="0",i.style.position="absolute",i.style[e?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return i.style.top="".concat(r,"px"),i.setAttribute("readonly",""),i.value=t,i}var d=function(t,e){var i=p(t);e.container.appendChild(i);var r=l()(i);return c("copy"),i.remove(),r},f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},i="";return"string"===typeof t?i=d(t,e):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===t||void 0===t?void 0:t.type)?i=d(t.value,e):(i=l()(t),c("copy")),i},m=f;function y(t){return y="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}var v=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.action,i=void 0===e?"copy":e,r=t.container,s=t.target,n=t.text;if("copy"!==i&&"cut"!==i)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==s){if(!s||"object"!==y(s)||1!==s.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===i&&s.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===i&&(s.hasAttribute("readonly")||s.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return n?m(n,{container:r}):s?"cut"===i?u(s):m(s,{container:r}):void 0},g=v;function x(t){return x="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function P(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function k(t,e,i){return e&&P(t.prototype,e),i&&P(t,i),t}function T(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&w(t,e)}function w(t,e){return w=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},w(t,e)}function E(t){var e=C();return function(){var i,r=I(t);if(e){var s=I(this).constructor;i=Reflect.construct(r,arguments,s)}else i=r.apply(this,arguments);return A(this,i)}}function A(t,e){return!e||"object"!==x(e)&&"function"!==typeof e?S(t):e}function S(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function C(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function I(t){return I=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},I(t)}function N(t,e){var i="data-clipboard-".concat(t);if(e.hasAttribute(i))return e.getAttribute(i)}var D=function(t){T(i,t);var e=E(i);function i(t,r){var s;return b(this,i),s=e.call(this),s.resolveOptions(r),s.listenClick(t),s}return k(i,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===x(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=a()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget,i=this.action(e)||"copy",r=g({action:i,container:this.container,target:this.target(e),text:this.text(e)});this.emit(r?"success":"error",{action:i,text:r,trigger:e,clearSelection:function(){e&&e.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(t){return N("action",t)}},{key:"defaultTarget",value:function(t){var e=N("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return N("text",t)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return m(t,e)}},{key:"cut",value:function(t){return u(t)}},{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,i=!!document.queryCommandSupported;return e.forEach((function(t){i=i&&!!document.queryCommandSupported(t)})),i}}]),i}(s()),O=D},828:function(t){var e=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var i=Element.prototype;i.matches=i.matchesSelector||i.mozMatchesSelector||i.msMatchesSelector||i.oMatchesSelector||i.webkitMatchesSelector}function r(t,i){while(t&&t.nodeType!==e){if("function"===typeof t.matches&&t.matches(i))return t;t=t.parentNode}}t.exports=r},438:function(t,e,i){var r=i(828);function s(t,e,i,r,s){var n=a.apply(this,arguments);return t.addEventListener(i,n,s),{destroy:function(){t.removeEventListener(i,n,s)}}}function n(t,e,i,r,n){return"function"===typeof t.addEventListener?s.apply(null,arguments):"function"===typeof i?s.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return s(t,e,i,r,n)})))}function a(t,e,i,s){return function(i){i.delegateTarget=r(i.target,e),i.delegateTarget&&s.call(t,i)}}t.exports=n},879:function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var i=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===i||"[object HTMLCollection]"===i)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},370:function(t,e,i){var r=i(879),s=i(438);function n(t,e,i){if(!t&&!e&&!i)throw new Error("Missing required arguments");if(!r.string(e))throw new TypeError("Second argument must be a String");if(!r.fn(i))throw new TypeError("Third argument must be a Function");if(r.node(t))return a(t,e,i);if(r.nodeList(t))return o(t,e,i);if(r.string(t))return l(t,e,i);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(t,e,i){return t.addEventListener(e,i),{destroy:function(){t.removeEventListener(e,i)}}}function o(t,e,i){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,i)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,i)}))}}}function l(t,e,i){return s(document.body,t,e,i)}t.exports=n},817:function(t){function e(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var i=t.hasAttribute("readonly");i||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),i||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var r=window.getSelection(),s=document.createRange();s.selectNodeContents(t),r.removeAllRanges(),r.addRange(s),e=r.toString()}return e}t.exports=e},279:function(t){function e(){}e.prototype={on:function(t,e,i){var r=this.e||(this.e={});return(r[t]||(r[t]=[])).push({fn:e,ctx:i}),this},once:function(t,e,i){var r=this;function s(){r.off(t,s),e.apply(i,arguments)}return s._=e,this.on(t,s,i)},emit:function(t){var e=[].slice.call(arguments,1),i=((this.e||(this.e={}))[t]||[]).slice(),r=0,s=i.length;for(r;r<s;r++)i[r].fn.apply(i[r].ctx,e);return this},off:function(t,e){var i=this.e||(this.e={}),r=i[t],s=[];if(r&&e)for(var n=0,a=r.length;n<a;n++)r[n].fn!==e&&r[n].fn._!==e&&s.push(r[n]);return s.length?i[t]=s:delete i[t],this}},t.exports=e,t.exports.TinyEmitter=e}},e={};function i(r){if(e[r])return e[r].exports;var s=e[r]={exports:{}};return t[r](s,s.exports,i),s.exports}return function(){i.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return i.d(e,{a:e}),e}}(),function(){i.d=function(t,e){for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}}(),function(){i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),i(686)}().default}))},b76a:function(t,e,i){(function(e,r){t.exports=r(i("aa47"))})("undefined"!==typeof self&&self,(function(t){return function(t){var e={};function i(r){if(e[r])return e[r].exports;var s=e[r]={i:r,l:!1,exports:{}};return t[r].call(s.exports,s,s.exports,i),s.l=!0,s.exports}return i.m=t,i.c=e,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var s in t)i.d(r,s,function(e){return t[e]}.bind(null,s));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s="fb15")}({"01f9":function(t,e,i){"use strict";var r=i("2d00"),s=i("5ca1"),n=i("2aba"),a=i("32e9"),o=i("84f2"),l=i("41a0"),c=i("7f20"),h=i("38fd"),u=i("2b4c")("iterator"),p=!([].keys&&"next"in[].keys()),d="@@iterator",f="keys",m="values",y=function(){return this};t.exports=function(t,e,i,v,g,x,b){l(i,e,v);var P,k,T,w=function(t){if(!p&&t in C)return C[t];switch(t){case f:return function(){return new i(this,t)};case m:return function(){return new i(this,t)}}return function(){return new i(this,t)}},E=e+" Iterator",A=g==m,S=!1,C=t.prototype,I=C[u]||C[d]||g&&C[g],N=I||w(g),D=g?A?w("entries"):N:void 0,O="Array"==e&&C.entries||I;if(O&&(T=h(O.call(new t)),T!==Object.prototype&&T.next&&(c(T,E,!0),r||"function"==typeof T[u]||a(T,u,y))),A&&I&&I.name!==m&&(S=!0,N=function(){return I.call(this)}),r&&!b||!p&&!S&&C[u]||a(C,u,N),o[e]=N,o[E]=y,g)if(P={values:A?N:w(m),keys:x?N:w(f),entries:D},b)for(k in P)k in C||n(C,k,P[k]);else s(s.P+s.F*(p||S),e,P);return P}},"02f4":function(t,e,i){var r=i("4588"),s=i("be13");t.exports=function(t){return function(e,i){var n,a,o=String(s(e)),l=r(i),c=o.length;return l<0||l>=c?t?"":void 0:(n=o.charCodeAt(l),n<55296||n>56319||l+1===c||(a=o.charCodeAt(l+1))<56320||a>57343?t?o.charAt(l):n:t?o.slice(l,l+2):a-56320+(n-55296<<10)+65536)}}},"0390":function(t,e,i){"use strict";var r=i("02f4")(!0);t.exports=function(t,e,i){return e+(i?r(t,e).length:1)}},"0bfb":function(t,e,i){"use strict";var r=i("cb7c");t.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"0d58":function(t,e,i){var r=i("ce10"),s=i("e11e");t.exports=Object.keys||function(t){return r(t,s)}},1495:function(t,e,i){var r=i("86cc"),s=i("cb7c"),n=i("0d58");t.exports=i("9e1e")?Object.defineProperties:function(t,e){s(t);var i,a=n(e),o=a.length,l=0;while(o>l)r.f(t,i=a[l++],e[i]);return t}},"214f":function(t,e,i){"use strict";i("b0c5");var r=i("2aba"),s=i("32e9"),n=i("79e5"),a=i("be13"),o=i("2b4c"),l=i("520a"),c=o("species"),h=!n((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),u=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2===i.length&&"a"===i[0]&&"b"===i[1]}();t.exports=function(t,e,i){var p=o(t),d=!n((function(){var e={};return e[p]=function(){return 7},7!=""[t](e)})),f=d?!n((function(){var e=!1,i=/a/;return i.exec=function(){return e=!0,null},"split"===t&&(i.constructor={},i.constructor[c]=function(){return i}),i[p](""),!e})):void 0;if(!d||!f||"replace"===t&&!h||"split"===t&&!u){var m=/./[p],y=i(a,p,""[t],(function(t,e,i,r,s){return e.exec===l?d&&!s?{done:!0,value:m.call(e,i,r)}:{done:!0,value:t.call(i,e,r)}:{done:!1}})),v=y[0],g=y[1];r(String.prototype,t,v),s(RegExp.prototype,p,2==e?function(t,e){return g.call(t,this,e)}:function(t){return g.call(t,this)})}}},"230e":function(t,e,i){var r=i("d3f4"),s=i("7726").document,n=r(s)&&r(s.createElement);t.exports=function(t){return n?s.createElement(t):{}}},"23c6":function(t,e,i){var r=i("2d95"),s=i("2b4c")("toStringTag"),n="Arguments"==r(function(){return arguments}()),a=function(t,e){try{return t[e]}catch(i){}};t.exports=function(t){var e,i,o;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=a(e=Object(t),s))?i:n?r(e):"Object"==(o=r(e))&&"function"==typeof e.callee?"Arguments":o}},2621:function(t,e){e.f=Object.getOwnPropertySymbols},"2aba":function(t,e,i){var r=i("7726"),s=i("32e9"),n=i("69a8"),a=i("ca5a")("src"),o=i("fa5b"),l="toString",c=(""+o).split(l);i("8378").inspectSource=function(t){return o.call(t)},(t.exports=function(t,e,i,o){var l="function"==typeof i;l&&(n(i,"name")||s(i,"name",e)),t[e]!==i&&(l&&(n(i,a)||s(i,a,t[e]?""+t[e]:c.join(String(e)))),t===r?t[e]=i:o?t[e]?t[e]=i:s(t,e,i):(delete t[e],s(t,e,i)))})(Function.prototype,l,(function(){return"function"==typeof this&&this[a]||o.call(this)}))},"2aeb":function(t,e,i){var r=i("cb7c"),s=i("1495"),n=i("e11e"),a=i("613b")("IE_PROTO"),o=function(){},l="prototype",c=function(){var t,e=i("230e")("iframe"),r=n.length,s="<",a=">";e.style.display="none",i("fab2").appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write(s+"script"+a+"document.F=Object"+s+"/script"+a),t.close(),c=t.F;while(r--)delete c[l][n[r]];return c()};t.exports=Object.create||function(t,e){var i;return null!==t?(o[l]=r(t),i=new o,o[l]=null,i[a]=t):i=c(),void 0===e?i:s(i,e)}},"2b4c":function(t,e,i){var r=i("5537")("wks"),s=i("ca5a"),n=i("7726").Symbol,a="function"==typeof n,o=t.exports=function(t){return r[t]||(r[t]=a&&n[t]||(a?n:s)("Symbol."+t))};o.store=r},"2d00":function(t,e){t.exports=!1},"2d95":function(t,e){var i={}.toString;t.exports=function(t){return i.call(t).slice(8,-1)}},"2fdb":function(t,e,i){"use strict";var r=i("5ca1"),s=i("d2c8"),n="includes";r(r.P+r.F*i("5147")(n),"String",{includes:function(t){return!!~s(this,t,n).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"32e9":function(t,e,i){var r=i("86cc"),s=i("4630");t.exports=i("9e1e")?function(t,e,i){return r.f(t,e,s(1,i))}:function(t,e,i){return t[e]=i,t}},"38fd":function(t,e,i){var r=i("69a8"),s=i("4bf8"),n=i("613b")("IE_PROTO"),a=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=s(t),r(t,n)?t[n]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?a:null}},"41a0":function(t,e,i){"use strict";var r=i("2aeb"),s=i("4630"),n=i("7f20"),a={};i("32e9")(a,i("2b4c")("iterator"),(function(){return this})),t.exports=function(t,e,i){t.prototype=r(a,{next:s(1,i)}),n(t,e+" Iterator")}},"456d":function(t,e,i){var r=i("4bf8"),s=i("0d58");i("5eda")("keys",(function(){return function(t){return s(r(t))}}))},4588:function(t,e){var i=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:i)(t)}},4630:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"4bf8":function(t,e,i){var r=i("be13");t.exports=function(t){return Object(r(t))}},5147:function(t,e,i){var r=i("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[r]=!1,!"/./"[t](e)}catch(s){}}return!0}},"520a":function(t,e,i){"use strict";var r=i("0bfb"),s=RegExp.prototype.exec,n=String.prototype.replace,a=s,o="lastIndex",l=function(){var t=/a/,e=/b*/g;return s.call(t,"a"),s.call(e,"a"),0!==t[o]||0!==e[o]}(),c=void 0!==/()??/.exec("")[1],h=l||c;h&&(a=function(t){var e,i,a,h,u=this;return c&&(i=new RegExp("^"+u.source+"$(?!\\s)",r.call(u))),l&&(e=u[o]),a=s.call(u,t),l&&a&&(u[o]=u.global?a.index+a[0].length:e),c&&a&&a.length>1&&n.call(a[0],i,(function(){for(h=1;h<arguments.length-2;h++)void 0===arguments[h]&&(a[h]=void 0)})),a}),t.exports=a},"52a7":function(t,e){e.f={}.propertyIsEnumerable},5537:function(t,e,i){var r=i("8378"),s=i("7726"),n="__core-js_shared__",a=s[n]||(s[n]={});(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:i("2d00")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"5ca1":function(t,e,i){var r=i("7726"),s=i("8378"),n=i("32e9"),a=i("2aba"),o=i("9b43"),l="prototype",c=function(t,e,i){var h,u,p,d,f=t&c.F,m=t&c.G,y=t&c.S,v=t&c.P,g=t&c.B,x=m?r:y?r[e]||(r[e]={}):(r[e]||{})[l],b=m?s:s[e]||(s[e]={}),P=b[l]||(b[l]={});for(h in m&&(i=e),i)u=!f&&x&&void 0!==x[h],p=(u?x:i)[h],d=g&&u?o(p,r):v&&"function"==typeof p?o(Function.call,p):p,x&&a(x,h,p,t&c.U),b[h]!=p&&n(b,h,d),v&&P[h]!=p&&(P[h]=p)};r.core=s,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,t.exports=c},"5eda":function(t,e,i){var r=i("5ca1"),s=i("8378"),n=i("79e5");t.exports=function(t,e){var i=(s.Object||{})[t]||Object[t],a={};a[t]=e(i),r(r.S+r.F*n((function(){i(1)})),"Object",a)}},"5f1b":function(t,e,i){"use strict";var r=i("23c6"),s=RegExp.prototype.exec;t.exports=function(t,e){var i=t.exec;if("function"===typeof i){var n=i.call(t,e);if("object"!==typeof n)throw new TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==r(t))throw new TypeError("RegExp#exec called on incompatible receiver");return s.call(t,e)}},"613b":function(t,e,i){var r=i("5537")("keys"),s=i("ca5a");t.exports=function(t){return r[t]||(r[t]=s(t))}},"626a":function(t,e,i){var r=i("2d95");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},6762:function(t,e,i){"use strict";var r=i("5ca1"),s=i("c366")(!0);r(r.P,"Array",{includes:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0)}}),i("9c6c")("includes")},6821:function(t,e,i){var r=i("626a"),s=i("be13");t.exports=function(t){return r(s(t))}},"69a8":function(t,e){var i={}.hasOwnProperty;t.exports=function(t,e){return i.call(t,e)}},"6a99":function(t,e,i){var r=i("d3f4");t.exports=function(t,e){if(!r(t))return t;var i,s;if(e&&"function"==typeof(i=t.toString)&&!r(s=i.call(t)))return s;if("function"==typeof(i=t.valueOf)&&!r(s=i.call(t)))return s;if(!e&&"function"==typeof(i=t.toString)&&!r(s=i.call(t)))return s;throw TypeError("Can't convert object to primitive value")}},7333:function(t,e,i){"use strict";var r=i("0d58"),s=i("2621"),n=i("52a7"),a=i("4bf8"),o=i("626a"),l=Object.assign;t.exports=!l||i("79e5")((function(){var t={},e={},i=Symbol(),r="abcdefghijklmnopqrst";return t[i]=7,r.split("").forEach((function(t){e[t]=t})),7!=l({},t)[i]||Object.keys(l({},e)).join("")!=r}))?function(t,e){var i=a(t),l=arguments.length,c=1,h=s.f,u=n.f;while(l>c){var p,d=o(arguments[c++]),f=h?r(d).concat(h(d)):r(d),m=f.length,y=0;while(m>y)u.call(d,p=f[y++])&&(i[p]=d[p])}return i}:l},7726:function(t,e){var i=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=i)},"77f1":function(t,e,i){var r=i("4588"),s=Math.max,n=Math.min;t.exports=function(t,e){return t=r(t),t<0?s(t+e,0):n(t,e)}},"79e5":function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"7f20":function(t,e,i){var r=i("86cc").f,s=i("69a8"),n=i("2b4c")("toStringTag");t.exports=function(t,e,i){t&&!s(t=i?t:t.prototype,n)&&r(t,n,{configurable:!0,value:e})}},8378:function(t,e){var i=t.exports={version:"2.6.5"};"number"==typeof __e&&(__e=i)},"84f2":function(t,e){t.exports={}},"86cc":function(t,e,i){var r=i("cb7c"),s=i("c69a"),n=i("6a99"),a=Object.defineProperty;e.f=i("9e1e")?Object.defineProperty:function(t,e,i){if(r(t),e=n(e,!0),r(i),s)try{return a(t,e,i)}catch(o){}if("get"in i||"set"in i)throw TypeError("Accessors not supported!");return"value"in i&&(t[e]=i.value),t}},"9b43":function(t,e,i){var r=i("d8e8");t.exports=function(t,e,i){if(r(t),void 0===e)return t;switch(i){case 1:return function(i){return t.call(e,i)};case 2:return function(i,r){return t.call(e,i,r)};case 3:return function(i,r,s){return t.call(e,i,r,s)}}return function(){return t.apply(e,arguments)}}},"9c6c":function(t,e,i){var r=i("2b4c")("unscopables"),s=Array.prototype;void 0==s[r]&&i("32e9")(s,r,{}),t.exports=function(t){s[r][t]=!0}},"9def":function(t,e,i){var r=i("4588"),s=Math.min;t.exports=function(t){return t>0?s(r(t),9007199254740991):0}},"9e1e":function(t,e,i){t.exports=!i("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},a352:function(e,i){e.exports=t},a481:function(t,e,i){"use strict";var r=i("cb7c"),s=i("4bf8"),n=i("9def"),a=i("4588"),o=i("0390"),l=i("5f1b"),c=Math.max,h=Math.min,u=Math.floor,p=/\$([$&`']|\d\d?|<[^>]*>)/g,d=/\$([$&`']|\d\d?)/g,f=function(t){return void 0===t?t:String(t)};i("214f")("replace",2,(function(t,e,i,m){return[function(r,s){var n=t(this),a=void 0==r?void 0:r[e];return void 0!==a?a.call(r,n,s):i.call(String(n),r,s)},function(t,e){var s=m(i,t,this,e);if(s.done)return s.value;var u=r(t),p=String(this),d="function"===typeof e;d||(e=String(e));var v=u.global;if(v){var g=u.unicode;u.lastIndex=0}var x=[];while(1){var b=l(u,p);if(null===b)break;if(x.push(b),!v)break;var P=String(b[0]);""===P&&(u.lastIndex=o(p,n(u.lastIndex),g))}for(var k="",T=0,w=0;w<x.length;w++){b=x[w];for(var E=String(b[0]),A=c(h(a(b.index),p.length),0),S=[],C=1;C<b.length;C++)S.push(f(b[C]));var I=b.groups;if(d){var N=[E].concat(S,A,p);void 0!==I&&N.push(I);var D=String(e.apply(void 0,N))}else D=y(E,p,A,S,I,e);A>=T&&(k+=p.slice(T,A)+D,T=A+E.length)}return k+p.slice(T)}];function y(t,e,r,n,a,o){var l=r+t.length,c=n.length,h=d;return void 0!==a&&(a=s(a),h=p),i.call(o,h,(function(i,s){var o;switch(s.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,r);case"'":return e.slice(l);case"<":o=a[s.slice(1,-1)];break;default:var h=+s;if(0===h)return i;if(h>c){var p=u(h/10);return 0===p?i:p<=c?void 0===n[p-1]?s.charAt(1):n[p-1]+s.charAt(1):i}o=n[h-1]}return void 0===o?"":o}))}}))},aae3:function(t,e,i){var r=i("d3f4"),s=i("2d95"),n=i("2b4c")("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[n])?!!e:"RegExp"==s(t))}},ac6a:function(t,e,i){for(var r=i("cadf"),s=i("0d58"),n=i("2aba"),a=i("7726"),o=i("32e9"),l=i("84f2"),c=i("2b4c"),h=c("iterator"),u=c("toStringTag"),p=l.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},f=s(d),m=0;m<f.length;m++){var y,v=f[m],g=d[v],x=a[v],b=x&&x.prototype;if(b&&(b[h]||o(b,h,p),b[u]||o(b,u,v),l[v]=p,g))for(y in r)b[y]||n(b,y,r[y],!0)}},b0c5:function(t,e,i){"use strict";var r=i("520a");i("5ca1")({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},be13:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},c366:function(t,e,i){var r=i("6821"),s=i("9def"),n=i("77f1");t.exports=function(t){return function(e,i,a){var o,l=r(e),c=s(l.length),h=n(a,c);if(t&&i!=i){while(c>h)if(o=l[h++],o!=o)return!0}else for(;c>h;h++)if((t||h in l)&&l[h]===i)return t||h||0;return!t&&-1}}},c649:function(t,e,i){"use strict";(function(t){i.d(e,"c",(function(){return c})),i.d(e,"a",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"d",(function(){return l}));i("a481");function r(){return"undefined"!==typeof window?window.console:t.console}var s=r();function n(t){var e=Object.create(null);return function(i){var r=e[i];return r||(e[i]=t(i))}}var a=/-(\w)/g,o=n((function(t){return t.replace(a,(function(t,e){return e?e.toUpperCase():""}))}));function l(t){null!==t.parentElement&&t.parentElement.removeChild(t)}function c(t,e,i){var r=0===i?t.children[0]:t.children[i-1].nextSibling;t.insertBefore(e,r)}}).call(this,i("c8ba"))},c69a:function(t,e,i){t.exports=!i("9e1e")&&!i("79e5")((function(){return 7!=Object.defineProperty(i("230e")("div"),"a",{get:function(){return 7}}).a}))},c8ba:function(t,e){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(r){"object"===typeof window&&(i=window)}t.exports=i},ca5a:function(t,e){var i=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++i+r).toString(36))}},cadf:function(t,e,i){"use strict";var r=i("9c6c"),s=i("d53b"),n=i("84f2"),a=i("6821");t.exports=i("01f9")(Array,"Array",(function(t,e){this._t=a(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,i=this._i++;return!t||i>=t.length?(this._t=void 0,s(1)):s(0,"keys"==e?i:"values"==e?t[i]:[i,t[i]])}),"values"),n.Arguments=n.Array,r("keys"),r("values"),r("entries")},cb7c:function(t,e,i){var r=i("d3f4");t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},ce10:function(t,e,i){var r=i("69a8"),s=i("6821"),n=i("c366")(!1),a=i("613b")("IE_PROTO");t.exports=function(t,e){var i,o=s(t),l=0,c=[];for(i in o)i!=a&&r(o,i)&&c.push(i);while(e.length>l)r(o,i=e[l++])&&(~n(c,i)||c.push(i));return c}},d2c8:function(t,e,i){var r=i("aae3"),s=i("be13");t.exports=function(t,e,i){if(r(e))throw TypeError("String#"+i+" doesn't accept regex!");return String(s(t))}},d3f4:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},d53b:function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},d8e8:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},e11e:function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},f559:function(t,e,i){"use strict";var r=i("5ca1"),s=i("9def"),n=i("d2c8"),a="startsWith",o=""[a];r(r.P+r.F*i("5147")(a),"String",{startsWith:function(t){var e=n(this,t,a),i=s(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),r=String(t);return o?o.call(e,r,i):e.slice(i,i+r.length)===r}})},f6fd:function(t,e){(function(t){var e="currentScript",i=t.getElementsByTagName("script");e in t||Object.defineProperty(t,e,{get:function(){try{throw new Error}catch(r){var t,e=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(r.stack)||[!1])[1];for(t in i)if(i[t].src==e||"interactive"==i[t].readyState)return i[t];return null}}})})(document)},f751:function(t,e,i){var r=i("5ca1");r(r.S+r.F,"Object",{assign:i("7333")})},fa5b:function(t,e,i){t.exports=i("5537")("native-function-to-string",Function.toString)},fab2:function(t,e,i){var r=i("7726").document;t.exports=r&&r.documentElement},fb15:function(t,e,i){"use strict";var r;(i.r(e),"undefined"!==typeof window)&&(i("f6fd"),(r=window.document.currentScript)&&(r=r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(i.p=r[1]));i("f751"),i("f559"),i("ac6a"),i("cadf"),i("456d");function s(t){if(Array.isArray(t))return t}function n(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var i=[],r=!0,s=!1,n=void 0;try{for(var a,o=t[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)if(i.push(a.value),e&&i.length===e)break}catch(l){s=!0,n=l}finally{try{r||null==o["return"]||o["return"]()}finally{if(s)throw n}}return i}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=new Array(e);i<e;i++)r[i]=t[i];return r}function o(t,e){if(t){if("string"===typeof t)return a(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?a(t,e):void 0}}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){return s(t)||n(t,e)||o(t,e)||l()}i("6762"),i("2fdb");function h(t){if(Array.isArray(t))return a(t)}function u(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t){return h(t)||u(t)||o(t)||p()}var f=i("a352"),m=i.n(f),y=i("c649");function v(t,e,i){return void 0===i||(t=t||{},t[e]=i),t}function g(t,e){return t.map((function(t){return t.elm})).indexOf(e)}function x(t,e,i,r){if(!t)return[];var s=t.map((function(t){return t.elm})),n=e.length-r,a=d(e).map((function(t,e){return e>=n?s.length:s.indexOf(t)}));return i?a.filter((function(t){return-1!==t})):a}function b(t,e){var i=this;this.$nextTick((function(){return i.$emit(t.toLowerCase(),e)}))}function P(t){var e=this;return function(i){null!==e.realList&&e["onDrag"+t](i),b.call(e,t,i)}}function k(t){return["transition-group","TransitionGroup"].includes(t)}function T(t){if(!t||1!==t.length)return!1;var e=c(t,1),i=e[0].componentOptions;return!!i&&k(i.tag)}function w(t,e,i){return t[i]||(e[i]?e[i]():void 0)}function E(t,e,i){var r=0,s=0,n=w(e,i,"header");n&&(r=n.length,t=t?[].concat(d(n),d(t)):d(n));var a=w(e,i,"footer");return a&&(s=a.length,t=t?[].concat(d(t),d(a)):d(a)),{children:t,headerOffset:r,footerOffset:s}}function A(t,e){var i=null,r=function(t,e){i=v(i,t,e)},s=Object.keys(t).filter((function(t){return"id"===t||t.startsWith("data-")})).reduce((function(e,i){return e[i]=t[i],e}),{});if(r("attrs",s),!e)return i;var n=e.on,a=e.props,o=e.attrs;return r("on",n),r("props",a),Object.assign(i.attrs,o),i}var S=["Start","Add","Remove","Update","End"],C=["Choose","Unchoose","Sort","Filter","Clone"],I=["Move"].concat(S,C).map((function(t){return"on"+t})),N=null,D={options:Object,list:{type:Array,required:!1,default:null},value:{type:Array,required:!1,default:null},noTransitionOnDrag:{type:Boolean,default:!1},clone:{type:Function,default:function(t){return t}},element:{type:String,default:"div"},tag:{type:String,default:null},move:{type:Function,default:null},componentData:{type:Object,required:!1,default:null}},O={name:"draggable",inheritAttrs:!1,props:D,data:function(){return{transitionMode:!1,noneFunctionalComponentMode:!1}},render:function(t){var e=this.$slots.default;this.transitionMode=T(e);var i=E(e,this.$slots,this.$scopedSlots),r=i.children,s=i.headerOffset,n=i.footerOffset;this.headerOffset=s,this.footerOffset=n;var a=A(this.$attrs,this.componentData);return t(this.getTag(),a,r)},created:function(){null!==this.list&&null!==this.value&&y["b"].error("Value and list props are mutually exclusive! Please set one or another."),"div"!==this.element&&y["b"].warn("Element props is deprecated please use tag props instead. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#element-props"),void 0!==this.options&&y["b"].warn("Options props is deprecated, add sortable options directly as vue.draggable item, or use v-bind. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#options-props")},mounted:function(){var t=this;if(this.noneFunctionalComponentMode=this.getTag().toLowerCase()!==this.$el.nodeName.toLowerCase()&&!this.getIsFunctional(),this.noneFunctionalComponentMode&&this.transitionMode)throw new Error("Transition-group inside component is not supported. Please alter tag value or remove transition-group. Current tag value: ".concat(this.getTag()));var e={};S.forEach((function(i){e["on"+i]=P.call(t,i)})),C.forEach((function(i){e["on"+i]=b.bind(t,i)}));var i=Object.keys(this.$attrs).reduce((function(e,i){return e[Object(y["a"])(i)]=t.$attrs[i],e}),{}),r=Object.assign({},this.options,i,e,{onMove:function(e,i){return t.onDragMove(e,i)}});!("draggable"in r)&&(r.draggable=">*"),this._sortable=new m.a(this.rootContainer,r),this.computeIndexes()},beforeDestroy:function(){void 0!==this._sortable&&this._sortable.destroy()},computed:{rootContainer:function(){return this.transitionMode?this.$el.children[0]:this.$el},realList:function(){return this.list?this.list:this.value}},watch:{options:{handler:function(t){this.updateOptions(t)},deep:!0},$attrs:{handler:function(t){this.updateOptions(t)},deep:!0},realList:function(){this.computeIndexes()}},methods:{getIsFunctional:function(){var t=this._vnode.fnOptions;return t&&t.functional},getTag:function(){return this.tag||this.element},updateOptions:function(t){for(var e in t){var i=Object(y["a"])(e);-1===I.indexOf(i)&&this._sortable.option(i,t[e])}},getChildrenNodes:function(){if(this.noneFunctionalComponentMode)return this.$children[0].$slots.default;var t=this.$slots.default;return this.transitionMode?t[0].child.$slots.default:t},computeIndexes:function(){var t=this;this.$nextTick((function(){t.visibleIndexes=x(t.getChildrenNodes(),t.rootContainer.children,t.transitionMode,t.footerOffset)}))},getUnderlyingVm:function(t){var e=g(this.getChildrenNodes()||[],t);if(-1===e)return null;var i=this.realList[e];return{index:e,element:i}},getUnderlyingPotencialDraggableComponent:function(t){var e=t.__vue__;return e&&e.$options&&k(e.$options._componentTag)?e.$parent:!("realList"in e)&&1===e.$children.length&&"realList"in e.$children[0]?e.$children[0]:e},emitChanges:function(t){var e=this;this.$nextTick((function(){e.$emit("change",t)}))},alterList:function(t){if(this.list)t(this.list);else{var e=d(this.value);t(e),this.$emit("input",e)}},spliceList:function(){var t=arguments,e=function(e){return e.splice.apply(e,d(t))};this.alterList(e)},updatePosition:function(t,e){var i=function(i){return i.splice(e,0,i.splice(t,1)[0])};this.alterList(i)},getRelatedContextFromMoveEvent:function(t){var e=t.to,i=t.related,r=this.getUnderlyingPotencialDraggableComponent(e);if(!r)return{component:r};var s=r.realList,n={list:s,component:r};if(e!==i&&s&&r.getUnderlyingVm){var a=r.getUnderlyingVm(i);if(a)return Object.assign(a,n)}return n},getVmIndex:function(t){var e=this.visibleIndexes,i=e.length;return t>i-1?i:e[t]},getComponent:function(){return this.$slots.default[0].componentInstance},resetTransitionData:function(t){if(this.noTransitionOnDrag&&this.transitionMode){var e=this.getChildrenNodes();e[t].data=null;var i=this.getComponent();i.children=[],i.kept=void 0}},onDragStart:function(t){this.context=this.getUnderlyingVm(t.item),t.item._underlying_vm_=this.clone(this.context.element),N=t.item},onDragAdd:function(t){var e=t.item._underlying_vm_;if(void 0!==e){Object(y["d"])(t.item);var i=this.getVmIndex(t.newIndex);this.spliceList(i,0,e),this.computeIndexes();var r={element:e,newIndex:i};this.emitChanges({added:r})}},onDragRemove:function(t){if(Object(y["c"])(this.rootContainer,t.item,t.oldIndex),"clone"!==t.pullMode){var e=this.context.index;this.spliceList(e,1);var i={element:this.context.element,oldIndex:e};this.resetTransitionData(e),this.emitChanges({removed:i})}else Object(y["d"])(t.clone)},onDragUpdate:function(t){Object(y["d"])(t.item),Object(y["c"])(t.from,t.item,t.oldIndex);var e=this.context.index,i=this.getVmIndex(t.newIndex);this.updatePosition(e,i);var r={element:this.context.element,oldIndex:e,newIndex:i};this.emitChanges({moved:r})},updateProperty:function(t,e){t.hasOwnProperty(e)&&(t[e]+=this.headerOffset)},computeFutureIndex:function(t,e){if(!t.element)return 0;var i=d(e.to.children).filter((function(t){return"none"!==t.style["display"]})),r=i.indexOf(e.related),s=t.component.getVmIndex(r),n=-1!==i.indexOf(N);return n||!e.willInsertAfter?s:s+1},onDragMove:function(t,e){var i=this.move;if(!i||!this.realList)return!0;var r=this.getRelatedContextFromMoveEvent(t),s=this.context,n=this.computeFutureIndex(r,t);Object.assign(s,{futureIndex:n});var a=Object.assign({},t,{relatedContext:r,draggedContext:s});return i(a,e)},onDragEnd:function(){this.computeIndexes(),N=null}}};"undefined"!==typeof window&&"Vue"in window&&window.Vue.component("draggable",O);var M=O;e["default"]=M}})["default"]}))},d60a:function(t,e){t.exports=function(t){return t&&"object"===typeof t&&"function"===typeof t.copy&&"function"===typeof t.fill&&"function"===typeof t.readUInt8}}}]);