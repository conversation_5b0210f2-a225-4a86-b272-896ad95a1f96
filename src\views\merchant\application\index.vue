<template>
  <div class="divBox">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <div class="container">
          <el-form size="small" label-width="100px" :inline="true">
            <el-form-item label="选择时间：" style="width: 100%">
              <el-radio-group v-model="tableFrom.dateLimit" size="small" @change="selectChange(tableFrom.dateLimit)">
                <el-radio-button v-for="(itemn, indexn) in fromList.fromTxt" :key="indexn" :label="itemn.val">{{
                  itemn.text
                }}</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-model="timeVal"
                type="daterange"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="onchangeTime"
              />
            </el-form-item>
            <el-form-item label="审核状态：">
              <el-radio-group
                v-model="tableFrom.auditStatus"
                size="small"
                @change="statusChange(tableFrom.auditStatus)"
              >
                <el-radio-button v-for="(itemn, indexn) in statusList.fromTxt" :key="indexn" :label="itemn.val">{{
                  itemn.text
                }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="商户分类：">
              <el-select
                v-model="tableFrom.categoryId"
                clearable
                placeholder="请选择"
                class="selWidth"
                @change="getList(1)"
              >
                <el-option v-for="item in merchantClassify" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="店铺类型：">
              <el-select
                v-model="tableFrom.typeId"
                clearable
                placeholder="请选择"
                class="selWidth"
                @change="getList(1)"
              >
                <el-option v-for="item in merchantType" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="关键字：">
              <el-input
                v-model="tableFrom.keywords"
                @keyup.enter.native="getList(1)"
                placeholder="请输入商户名称/关键字"
                class="selWidth"
              >
                <el-button slot="append" icon="el-icon-search" class="el-button-solt" @click="getList(1)" />
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        style="width: 100%"
        size="small"
        highlight-current-row
        class="switchTable"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-form-item label="商户姓名：">
                <span>{{ props.row.realName }}</span>
              </el-form-item>
              <el-form-item label="商户类别：">
                <span>{{ props.row.isSelf ? '自营' : '非自营' }}</span>
              </el-form-item>
              <el-form-item label="备注：">
                <span>{{ props.row.remark }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" min-width="60" />
        <el-table-column prop="name" label="商户名称" min-width="150" />
        <el-table-column prop="email" label="商户邮箱" min-width="150" />
        <el-table-column prop="phone" label="联系方式" min-width="130" />
        <el-table-column prop="createTime" label="申请时间" min-width="150" />
        <el-table-column label="状态" min-width="150">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.auditStatus == 2" type="success">通过</el-tag>
            <el-tag v-if="scope.row.auditStatus == 1" type="info">未处理</el-tag>
            <el-tag v-if="scope.row.auditStatus == 3" type="warning">未通过</el-tag>
            <div v-if="scope.row.auditStatus == 3">原因：{{ scope.row.denialReason }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="110" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.auditStatus == 1"
              type="text"
              size="small"
              @click="onchangeIsShow(scope.row, 'isSHOW')"
              v-hasPermi="['platform:merchant:apply:audit']"
              >审核</el-button
            >
            <el-button type="text" size="small" @click="onchangeIsShow(scope.row)">详情</el-button>
            <el-button
              type="text"
              size="small"
              @click="onEdit(scope.row)"
              v-hasPermi="['platform:merchant:apply:remark']"
              >备注</el-button
            >
            <!--<el-button-->
            <!--type="text"-->
            <!--size="small"-->
            <!--@click="handleDelete(scope.row.mer_intention_id)"-->
            <!--&gt;删除</el-button-->
            <!--&gt;-->
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
    <audit-from ref="auditFroms" :merData="merData" :isSHOW="isSHOW" @subSuccess="subSuccess"></audit-from>
  </div>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { merApplyListApi, merApplyRemarkApi } from '@/api/merchant';
import { mapGetters } from 'vuex';
import auditFrom from './audit';
export default {
  name: 'MerchantApplication',
  components: { auditFrom },
  data() {
    return {
      props: {
        emitPath: false,
      },
      fromList: this.$constants.fromList,
      statusList: this.$constants.statusList, //筛选状态列表
      isChecked: false,
      listLoading: true,
      tableData: {
        data: [],
        total: 0,
      },
      tableFrom: {
        page: 1,
        limit: 20,
        dateLimit: '',
        auditStatus: '',
        keywords: '',
        categoryId: '',
        typeId: '',
      },
      mer_id: this.$route.query.id ? this.$route.query.id : '',
      autoUpdate: true,
      timeVal: [],
      merData: {},
      isSHOW: '',
    };
  },
  computed: {
    ...mapGetters(['merchantClassify', 'merchantType']),
  },
  watch: {
    mer_id(newName, oldName) {
      this.getList('');
    },
  },
  mounted() {
    this.getList('');
  },
  methods: {
    subSuccess() {
      this.getList('');
    },
    // 选择时间
    selectChange(tab) {
      this.tableFrom.dateLimit = tab;
      this.timeVal = [];
      this.tableFrom.page = 1;
      this.getList('');
    },
    statusChange(tab) {
      this.tableFrom.auditStatus = tab;
      this.tableFrom.page = 1;
      this.getList('');
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.tableFrom.dateLimit = this.timeVal ? this.timeVal.join(',') : '';
      this.tableFrom.page = 1;
      this.getList('');
    },
    // 列表
    getList(num) {
      if (!this.merchantClassify.length) this.$store.dispatch('merchant/getMerchantClassify');
      if (!this.merchantType.length) this.$store.dispatch('merchant/getMerchantType');
      this.listLoading = true;
      this.tableFrom.page = num ? num : this.tableFrom.page;
      merApplyListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList('');
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList(1);
    },
    // 审核
    onchangeIsShow(row, type) {
      this.merData = row;
      this.isSHOW = type;
      this.$refs.auditFroms.dialogVisible = true;
    },
    // 添加

    // 备注
    onEdit(row) {
      this.$prompt('备注', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputErrorMessage: '请输入备注',
        inputType: 'textarea',
        inputValue: row.remark,
        inputPlaceholder: '请输入备注',
        inputValidator: (value) => {
          if (!value) {
            return '请输入备注';
          }
        },
      })
        .then(({ value }) => {
          merApplyRemarkApi({
            id: row.id,
            remark: value,
          }).then((res) => {
            this.$message({
              type: 'success',
              message: '提交成功',
            });
            this.getList('');
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入',
          });
        });
    },
    // 删除
    handleDelete(id) {
      this.$modalSure().then(() => {
        intentionDelte(id).then(({ message }) => {
          this.$message.success(message);
          this.getList(1);
        });
      }).catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
/*/deep/ table .el-image {*/
/*display: inline-block !important;*/
/*}*/
</style>
