(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-62558ad3"],{"4d00":function(t,e,a){"use strict";a("d73b")},"794c":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small",inline:"","label-width":"100px"}},[a("el-form-item",{staticClass:"width100",staticStyle:{display:"block"},attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,"dateLimit",e)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,i){return a("el-radio-button",{key:i,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(" "),a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"审核状态："}},[a("el-radio-group",{attrs:{type:"button"},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.auditStatus,callback:function(e){t.$set(t.tableFrom,"auditStatus",e)},expression:"tableFrom.auditStatus"}},[a("el-radio-button",{attrs:{label:""}},[t._v("全部 ")]),t._v(" "),a("el-radio-button",{attrs:{label:"0"}},[t._v("待审核")]),t._v(" "),a("el-radio-button",{attrs:{label:"1"}},[t._v("审核通过")]),t._v(" "),a("el-radio-button",{attrs:{label:"2"}},[t._v("审核失败")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"到账状态："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.accountStatus,callback:function(e){t.$set(t.tableFrom,"accountStatus",e)},expression:"tableFrom.accountStatus"}},t._l(t.arrivalStatusList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"结算类型："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.closingType,callback:function(e){t.$set(t.tableFrom,"closingType",e)},expression:"tableFrom.closingType"}},t._l(t.closingTypeList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),a("br"),t._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"关键字："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"微信号、支付宝账号、银行卡号、持卡人姓名",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getList(1)}},model:{value:t.keywords,callback:function(e){t.keywords=e},expression:"keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:user:closing:page:list"],expression:"['platform:finance:user:closing:page:list']"}],staticClass:"el-button-solt",attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1),t._v(" "),a("br")],1)],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",staticStyle:{width:"100%"},attrs:{"tooltip-effect":"dark",data:t.tableData.data}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),t._v(" "),a("el-table-column",{attrs:{prop:"closingNo",label:"结算单号","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"nickName",label:"用户昵称","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"closingPrice",label:"金额","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{label:"结算类型","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("closingTypeFilter")(e.row.closingType)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"审核状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(0==e.row.auditStatus?"待审核":1==e.row.auditStatus?"审核通过":"审核失败"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"到账状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(1==e.row.accountStatus?"已转账":"未转账"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"备注","min-width":"120","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.mark)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"申请时间","min-width":"150","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:user:closing:page:list"],expression:"['platform:finance:user:closing:page:list']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.closingDetail(e.row,1)}}},[t._v("详情")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:0===e.row.auditStatus,expression:"scope.row.auditStatus === 0"},{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:user:closing:audit"],expression:"['platform:finance:user:closing:audit']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.closingDetail(e.row,2)}}},[t._v("审核")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:1===e.row.auditStatus&&0===e.row.accountStatus,expression:"scope.row.auditStatus === 1 && scope.row.accountStatus === 0"},{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:user:closing:proof"],expression:"['platform:finance:user:closing:proof']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.closingDetail(e.row,3)}}},[t._v("结算凭证")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:user:closing:remark"],expression:"['platform:finance:user:closing:remark']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.onRemark(e.row)}}},[t._v("备注")])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("el-drawer",{attrs:{direction:"rtl",visible:t.dialogVisible,size:"700px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[a("div",{staticClass:"title",attrs:{slot:"title"},slot:"title"},[t._v("结算详情")]),t._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"box-container"},[a("div",{staticClass:"acea-row"},[a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("用户昵称：")]),t._v(t._s(t.closingData.nickName))]),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("结算金额：")]),t._v(t._s(t.closingData.closingPrice))]),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("用户余额：")]),t._v(t._s(t.closingData.balance))]),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("结算类型：")]),t._v(t._s(t._f("closingTypeFilter")(t.closingData.closingType))+"\n        ")]),t._v(" "),"bank"===t.closingData.closingType?[a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("银行卡姓名 ：")]),t._v(t._s(t.closingData.cardholder))]),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("银行名称：")]),t._v(t._s(t.closingData.bankName))]),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("银行账号：")]),t._v(t._s(t.closingData.bankCardNo))])]:t._e(),t._v(" "),"wechat"===t.closingData.closingType?a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("微信号：")]),t._v(t._s(t.closingData.wechatNo)+"\n        ")]):t._e(),t._v(" "),"alipay"===t.closingData.closingType?a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("支付宝账号：")]),t._v(t._s(t.closingData.alipayAccount)+"\n        ")]):t._e(),t._v(" "),"bank"!==t.closingData.closingType?a("div",{staticClass:"list sp100 acea-row"},[a("label",{staticClass:"name"},[t._v("收款二维码：")]),t._v(" "),a("div",{staticClass:"demo-image__preview"},[a("el-image",{attrs:{src:t.closingData.paymentCode,"preview-src-list":[t.closingData.paymentCode]}})],1)]):t._e(),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("审核状态：")]),t._v(t._s(0==t.closingData.auditStatus?"待审核":1==t.closingData.auditStatus?"审核通过":"审核失败")+"\n        ")]),t._v(" "),1==t.closingData.auditStatus?a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("审核时间：")]),t._v(t._s(t._f("filterEmpty")(t.closingData.auditTime))+"\n        ")]):t._e(),t._v(" "),t.closingData.closingProof?a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("结算凭证：")]),t._v(" "),a("div",{staticClass:"acea-row"},t._l(JSON.parse(t.closingData.closingProof),(function(e,i){return a("div",{key:i,staticClass:"pictrue"},[a("img",{attrs:{src:e},on:{click:function(a){return t.getPicture(e)}}})])})),0)]):t._e(),t._v(" "),1==t.closingData.auditStatus&&t.closingData.closingTime?a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("结算时间：")]),t._v(t._s(t.closingData.closingTime)+"\n        ")]):t._e(),t._v(" "),2==t.closingData.auditStatus&&t.closingData.refusalReason?a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("审核未通过原因：")]),t._v(t._s(t.closingData.refusalReason)+"\n        ")]):t._e(),t._v(" "),a("div",{staticClass:"list sp100"},[a("label",{staticClass:"name"},[t._v("备注：")]),t._v(t._s(t._f("filterEmpty")(t.closingData.mark)))])],2),t._v(" "),1!==t.isShow?a("div",{staticClass:"from-foot-btn fix btn-shadow"},[a("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px"}},[2===t.isShow&&0==t.closingData.auditStatus?[a("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[a("el-radio-group",{model:{value:t.ruleForm.auditStatus,callback:function(e){t.$set(t.ruleForm,"auditStatus",e)},expression:"ruleForm.auditStatus"}},[a("el-radio",{attrs:{label:1}},[t._v("通过")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("拒绝")])],1)],1),t._v(" "),2===t.ruleForm.auditStatus?a("el-form-item",{attrs:{label:"原因",prop:"refusalReason"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入原因"},model:{value:t.ruleForm.refusalReason,callback:function(e){t.$set(t.ruleForm,"refusalReason",e)},expression:"ruleForm.refusalReason"}})],1):t._e()]:t._e(),t._v(" "),3===t.isShow&&1===t.closingData.auditStatus&&0===t.closingData.accountStatus?a("el-form-item",{attrs:{label:"转账凭证：",prop:"closingProof"}},[a("div",{staticClass:"acea-row"},[t.ruleForm.closingProof.length>0?a("div",{staticClass:"acea-row"},t._l(t.ruleForm.closingProof,(function(e,i){return a("div",{key:i,staticClass:"pictrue"},[a("img",{attrs:{src:e},on:{click:function(a){return t.getPicture(e)}}}),t._v(" "),a("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(i)}}})])})),0):t._e(),t._v(" "),a("el-upload",{directives:[{name:"show",rawName:"v-show",value:t.ruleForm.closingProof.length<6,expression:"ruleForm.closingProof.length < 6"}],staticClass:"upload-demo",attrs:{action:"","http-request":t.handleUploadForm,headers:t.myHeaders,"show-file-list":!1,multiple:""}},[a("div",{staticClass:"upLoadPicBox"},[a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-upload2"})])])])],1)]):t._e(),t._v(" "),a("el-form-item",[a("el-button",{on:{click:t.close}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("ruleForm")}}},[t._v(t._s(t.loadingBtn?"提交中 ...":"确 定"))])],1)],2)],1):t._e()])]),t._v(" "),t.pictureVisible?a("el-dialog",{attrs:{visible:t.pictureVisible,width:"700px"},on:{"update:visible":function(e){t.pictureVisible=e}}},[a("img",{staticClass:"pictures",attrs:{src:t.pictureUrl}})]):t._e()],1)},s=[],l=a("cd05"),n=a("6ed5"),o=a("5f87"),r=a("785a"),c={name:"transferAccount",data:function(){return{myHeaders:{"X-Token":Object(o["a"])()},isShow:0,loadingBtn:!1,rules:{auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],refusalReason:[{required:!0,message:"请填写拒绝原因",trigger:"blur"}],closingProof:[{required:!0,message:"请上传结算凭证",type:"array",trigger:"change"}]},tableData:{data:[],total:0},arrivalStatusList:[{label:"已到账",value:1},{label:"未到账",value:0}],closingTypeList:[{label:"银行卡",value:"bank"},{label:"微信",value:"wechat"},{label:"支付宝",value:"alipay"}],listLoading:!0,tableFrom:{dateLimit:"",page:1,limit:20,keywords:"",auditStatus:"",accountStatus:"",closingType:""},keywords:"",timeVal:[],fromList:this.$constants.fromList,loading:!1,dialogVisible:!1,pictureVisible:!1,closingData:{},baseInfoform:{amount:0,mark:"",transferType:""},merchantList:[],search:{limit:10,page:1,keywords:""},ruleForm:{refusalReason:"",auditStatus:1,id:"",closingProof:[]},localImg:""}},components:{merchantName:n["a"]},mounted:function(){this.getList(1)},methods:{onRemark:function(t){var e=this;this.$prompt("备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入备注",inputType:"textarea",inputValue:t.mark,inputPlaceholder:"请输入备注",inputValidator:function(t){if(!t)return"请输入备注"}}).then((function(a){var i=a.value;Object(l["p"])({closingNo:t.closingNo,remark:i}).then((function(t){e.$message({type:"success",message:"提交成功"}),e.getList("")}))})).catch((function(){e.$message({type:"info",message:"取消输入"})}))},onSubmit:function(t){var e=this;if(2===this.isShow)this.$refs[t].validate((function(t){if(!t)return!1;e.loadingBtn=!0;var a={closingNo:e.closingData.closingNo,refusalReason:e.ruleForm.refusalReason,auditStatus:e.ruleForm.auditStatus};Object(l["m"])(a).then((function(t){e.$message.success("操作成功"),e.dialogVisible=!1,e.getList(1),e.close(),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}));else{this.loadingBtn=!0;var a={closingNo:this.closingData.closingNo,closingProof:JSON.stringify(this.ruleForm.closingProof)};this.$refs[t].validate((function(t){t&&Object(l["o"])(a).then((function(t){e.$message.success("操作成功"),e.dialogVisible=!1,e.getList(1),e.close(),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}))}},handleUploadForm:function(t){var e=this,a=new FormData,i={model:"finance",pid:0};a.append("multipart",t.file);var s=this.$loading({lock:!0,text:"上传中，请稍候...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Object(r["d"])(a,i).then((function(t){s.close(),e.$message.success("上传成功"),e.ruleForm.closingProof.push(t.url)})).catch((function(t){s.close()}))},handleRemove:function(t){this.ruleForm.closingProof.splice(t,1)},close:function(){this.dialogVisible=!1,this.$refs["ruleForm"].resetFields(),this.ruleForm.closingProof=[],this.loadingBtn=!1},closingDetail:function(t,e){this.isShow=e,this.closingData=t,this.dialogVisible=!0},getPicture:function(t){this.pictureVisible=!0,this.pictureUrl=t},selectChange:function(t){this.tableFrom.dateLimit=t,this.timeVal=[],this.getList(1)},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.getList(1)},exportRecord:function(){var t=this;Object(l["transferRecordsExportApi"])(this.tableFrom).then((function(e){var a=t.$createElement;t.$msgbox({title:"提示",message:a("p",null,[a("span",null,'文件正在生成中，请稍后点击"'),a("span",{style:"color: teal"},"导出记录"),a("span",null,'"查看~ ')]),confirmButtonText:"我知道了"}).then((function(t){}))})).catch((function(e){t.$message.error(e.message)}))},getExportFileList:function(){this.$refs.exportList.exportFileList()},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,this.tableFrom.keywords=encodeURIComponent(this.keywords),Object(l["n"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList("")},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList("")},handleClose:function(){this.dialogLogistics=!1}}},u=c,m=(a("4d00"),a("2877")),d=Object(m["a"])(u,i,s,!1,null,"39922c25",null);e["default"]=d.exports},cd05:function(t,e,a){"use strict";a.d(e,"i",(function(){return s})),a.d(e,"h",(function(){return l})),a.d(e,"g",(function(){return n})),a.d(e,"k",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"d",(function(){return c})),a.d(e,"e",(function(){return u})),a.d(e,"p",(function(){return m})),a.d(e,"m",(function(){return d})),a.d(e,"n",(function(){return f})),a.d(e,"o",(function(){return p})),a.d(e,"f",(function(){return g})),a.d(e,"j",(function(){return v})),a.d(e,"b",(function(){return b})),a.d(e,"a",(function(){return h}));var i=a("b775");function s(t){return Object(i["a"])({url:"/admin/platform/finance/merchant/closing/list",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/admin/platform/finance/merchant/closing/remark",method:"post",data:t})}function n(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/detail/".concat(t),method:"get"})}function o(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/proof",method:"POST",data:t})}function r(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/audit",method:"POST",data:t})}function c(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/config",method:"get"})}function u(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/config/edit",method:"post",data:t})}function m(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/remark",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/audit",method:"POST",data:t})}function f(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/list",method:"get",params:t})}function p(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/proof",method:"POST",data:t})}function g(t){return Object(i["a"])({url:"admin/platform/finance/daily/statement/list",method:"get",params:t})}function v(t){return Object(i["a"])({url:"admin/platform/finance/month/statement/list",method:"get",params:t})}function b(t){return Object(i["a"])({url:"admin/platform/finance/funds/flow",method:"get",params:t})}function h(t){return i["a"].get("financial_record/export",t)}},d73b:function(t,e,a){}}]);