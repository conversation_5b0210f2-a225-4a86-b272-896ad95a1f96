(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b0ab5ee2"],{"29ef":function(e,t,i){},a4fe9:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:user:level:save"],expression:"['platform:system:user:level:save']"}],staticClass:"mr10",attrs:{type:"primary",size:"small"},on:{click:e.add}},[e._v("添加用户等级")])],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini"}},[i("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),e._v(" "),i("el-table-column",{attrs:{label:"等级图标","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{staticClass:"demo-image__preview"},[i("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.icon,"preview-src-list":[e.row.icon]}})],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"name",label:"等级名称","min-width":"100"}}),e._v(" "),i("el-table-column",{attrs:{prop:"experience",label:"经验","min-width":"100"}}),e._v(" "),i("el-table-column",{attrs:{label:"状态","min-width":"100",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["platform:system:user:level:use"])?[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭",disabled:""},nativeOn:{click:function(i){return e.onchangeIsShow(t.row)}},model:{value:t.row.isShow,callback:function(i){e.$set(t.row,"isShow",i)},expression:"scope.row.isShow"}})]:void 0}}],null,!0)}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:user:level:update"],expression:"['platform:system:user:level:update']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(i){return e.edit(t.row)}}},[e._v("编辑")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:user:level:delete"],expression:"['platform:system:user:level:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleDelete(t.row.id,t.$index)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),i("creat-level",{ref:"grades",attrs:{user:e.userInfo}})],1)},a=[],r=i("c24f"),n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.dialogVisible?i("el-dialog",{attrs:{title:"用户等级",visible:e.dialogVisible,width:"500px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"user",staticClass:"demo-ruleForm",attrs:{model:e.user,rules:e.rules,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"等级名称",prop:"name"}},[i("el-input",{attrs:{placeholder:"请输入等级名称"},model:{value:e.user.name,callback:function(t){e.$set(e.user,"name",t)},expression:"user.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"等级",prop:"grade"}},[i("el-input",{attrs:{placeholder:"请输入等级"},model:{value:e.user.grade,callback:function(t){e.$set(e.user,"grade",e._n(t))},expression:"user.grade"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"经验",prop:"experience"}},[i("el-input-number",{attrs:{placeholder:"请输入经验",min:0,"step-strictly":""},model:{value:e.user.experience,callback:function(t){e.$set(e.user,"experience",e._n(t))},expression:"user.experience"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"图标",prop:"icon"}},[i("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("1","icon")}}},[e.user.icon?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.user.icon}})]):e.formValidate.icon?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.formValidate.icon}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){return e.resetForm("user")}}},[e._v("取 消")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:user:level:save","platform:system:user:level:update"],expression:"['platform:system:user:level:save', 'platform:system:user:level:update']"}],attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formValidate")}}},[e._v("确 定")])],1)],1):e._e()},l=[],o=i("61f7"),c={name:"",grade:1,icon:"",image:"",id:null},u={name:"CreatGrade",props:{user:Object},data:function(){return{dialogVisible:!1,formValidate:Object.assign({},c),loading:!1,rules:{name:[{required:!0,message:"请输入等级名称",trigger:"blur"}],grade:[{required:!0,message:"请输入等级",trigger:"blur"},{type:"number",message:"等级必须为数字值"}],discount:[{message:"请输入折扣",trigger:"blur"}],experience:[{required:!0,message:"请输入经验",trigger:"blur"},{type:"number",message:"经验必须为数字值"}],icon:[{required:!0,message:"请上传图标",trigger:"change"}],image:[{required:!0,message:"请上传用户背景",trigger:"change"}]}}},methods:{modalPicTap:function(e,t){var i=this;this.$modalUpload((function(s){"1"===e&&"icon"===t?i.formValidate.icon=s[0].sattDir:i.formValidate.image=s[0].sattDir,this.$set(i.user,"icon",i.formValidate.icon),this.$set(i.user,"isShow",!1)}),e,"user")},info:function(e){var t=this;this.loading=!0,Object(r["h"])({id:e}).then((function(e){t.formValidate=e,t.loading=!1})).catch((function(){t.loading=!1}))},handleClose:function(){this.dialogVisible=!1},submitForm:Object(o["a"])((function(e){var t=this;this.$refs.user.validate((function(e){if(!e)return!1;t.loading=!0;var i={discount:t.user.discount,experience:t.user.experience,grade:t.user.grade,icon:t.user.icon,id:t.user.id,isShow:t.user.isShow,name:t.user.name};t.user.id?Object(r["k"])(i).then((function(e){t.$message.success("编辑成功"),t.loading=!1,t.handleClose(),t.formValidate=Object.assign({},c),t.$parent.getList()})).catch((function(){t.loading=!1})):Object(r["j"])(t.user).then((function(e){t.$message.success("添加成功"),t.loading=!1,t.handleClose(),t.formValidate=Object.assign({},c),t.$parent.getList()})).catch((function(){t.loading=!1,t.formValidate=Object.assign({},c)}))}))})),resetForm:function(e){this.dialogVisible=!1,this[e]={}}}},d=u,m=i("2877"),f=Object(m["a"])(d,n,l,!1,null,"6c40da05",null),h=f.exports,p=i("e350"),g={name:"Grade",filters:{typeFilter:function(e){var t={wechat:"微信用户",routine:"小程序你用户",h5:"H5用户"};return t[e]}},components:{creatLevel:h},data:function(){return{listLoading:!0,userInfo:{},tableData:{data:[],total:0}}},mounted:function(){this.getList()},methods:{checkPermi:p["a"],seachList:function(){this.getList()},add:function(){this.$refs.grades.dialogVisible=!0,this.userInfo={}},edit:function(e){this.userInfo=e,this.$refs.grades.dialogVisible=!0},getList:function(){var e=this;this.listLoading=!0,Object(r["i"])().then((function(t){e.tableData.data=t,e.listLoading=!1})).catch((function(){e.listLoading=!1}))},handleDelete:function(e,t){var i=this;this.$modalSure("删除吗？删除会导致对应用户等级数据清空，请谨慎操作！").then((function(){Object(r["g"])(e).then((function(){i.$message.success("删除成功"),i.tableData.data.splice(t,1)}))})).catch((function(){}))},onchangeIsShow:function(e){var t=this;0==e.isShow?(e.isShow=!e.isShow,Object(r["l"])({id:e.id,isShow:e.isShow}).then((function(){t.$message.success("修改成功"),t.getList()})).catch((function(){e.isShow=!e.isShow}))):this.$modalSure("该操作会导致对应用户等级隐藏，请谨慎操作").then((function(){e.isShow=!e.isShow,Object(r["l"])({id:e.id,isShow:e.isShow}).then((function(){t.$message.success("修改成功"),t.getList()})).catch((function(){e.isShow=!e.isShow}))})).catch((function(){}))}}},v=g,b=(i("e590"),Object(m["a"])(v,s,a,!1,null,"26ca0e95",null));t["default"]=b.exports},e590:function(e,t,i){"use strict";i("29ef")}}]);