(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c51a4"],{"3e51":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"components-container"},[a("el-form",{ref:"pram",attrs:{"label-width":"150px",model:t.pram}},[a("el-form-item",{attrs:{label:"标题",prop:"title",rules:[{required:!0,message:"请填写标题",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"标题",maxlength:"100"},model:{value:t.pram.title,callback:function(e){t.$set(t.pram,"title",e)},expression:"pram.title"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"作者",prop:"author",rules:[{required:!0,message:"请填作者",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"作者",maxlength:"20"},model:{value:t.pram.author,callback:function(e){t.$set(t.pram,"author",e)},expression:"pram.author"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"文章分类",rules:[{required:!0,message:"请选择分类",trigger:["blur","change"]}]}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.pram.cid,callback:function(e){t.$set(t.pram,"cid",e)},expression:"pram.cid"}},t._l(t.categoryTreeData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"图文封面",prop:"cover",rules:[{required:!0,message:"请上传图文封面",trigger:"change"}]}},[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.pram.cover?a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.pram.cover}})]):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),a("el-form-item",{attrs:{label:"文章简介",prop:"synopsis",rules:[{required:!0,message:"请填写文章简介",trigger:["blur","change"]}]}},[a("el-input",{attrs:{maxlength:"100",type:"textarea",rows:2,resize:"none",placeholder:"文章简介"},model:{value:t.pram.synopsis,callback:function(e){t.$set(t.pram,"synopsis",e)},expression:"pram.synopsis"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"文章内容",prop:"content",rules:[{required:!0,message:"请填写文章内容",trigger:["blur","change"]}]}},[a("Tinymce",{model:{value:t.pram.content,callback:function(e){t.$set(t.pram,"content",e)},expression:"pram.content"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{attrs:{min:0,max:10,label:"排序"},model:{value:t.pram.sort,callback:function(e){t.$set(t.pram,"sort",e)},expression:"pram.sort"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否Banner"}},[a("el-switch",{model:{value:t.pram.isBanner,callback:function(e){t.$set(t.pram,"isBanner",e)},expression:"pram.isBanner"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否热门"}},[a("el-switch",{model:{value:t.pram.isHot,callback:function(e){t.$set(t.pram,"isHot",e)},expression:"pram.isHot"}})],1),t._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:article:update","platform:article:save"],expression:"['platform:article:update', 'platform:article:save']"}],attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.handerSubmit("pram")}}},[t._v("保存")])],1)],1)],1)])],1)},s=[],i=a("8256"),n=a("2423"),l=(a("785a"),a("5f87")),o=a("61f7"),c={components:{Tinymce:i["a"]},data:function(){return{loading:!1,constants:this.$constants,categoryTreeData:[],categoryProps:{value:"id",label:"name",children:"child",expandTrigger:"hover",checkStrictly:!0,emitPath:!1},pram:{author:null,cid:null,content:"",cover:"",isBanner:!1,isHot:null,shareSynopsis:null,shareTitle:null,sort:0,synopsis:null,title:null,id:null},editData:{},myHeaders:{"X-Token":Object(l["a"])()},editorContentLaebl:""}},created:function(){this.tempRoute=Object.assign({},this.$route)},mounted:function(){localStorage.getItem("articleClass")?this.categoryTreeData=JSON.parse(localStorage.getItem("articleClass")):this.handlerGetCategoryTreeData(),this.$route.params.id&&(this.getInfo(),this.setTagsViewTitle())},methods:{getInfo:function(){var t=this;n["c"](this.$route.params.id).then((function(e){t.editData=e,t.hadlerInitEditData()}))},modalPicTap:function(t){var e=this;this.$modalUpload((function(t){e.pram.cover=t[0].sattDir}),t,"content")},hadlerInitEditData:function(){if(this.$route.params.id){var t=this.editData,e=t.author,a=t.cid,r=t.content,s=t.cover,i=t.isBanner,n=t.isHot,l=t.shareSynopsis,o=t.shareTitle,c=t.sort,p=t.synopsis,m=t.title,u=t.id;this.pram.author=e,this.pram.cid=Number.parseInt(a),this.pram.content=r,this.pram.cover=s,this.pram.isBanner=i,this.pram.isHot=n,this.pram.shareSynopsis=l,this.pram.shareTitle=o,this.pram.sort=c,this.pram.synopsis=p,this.pram.title=m,this.pram.id=u}},handlerGetCategoryTreeData:function(){var t=this;n["h"]().then((function(e){t.categoryTreeData=e;var a=e.filter((function(t){return t.status}));localStorage.setItem("articleClass",JSON.stringify(a))}))},handerSubmit:Object(o["a"])((function(t){var e=this;this.$refs[t].validate((function(t){t&&(e.$route.params.id?e.handlerUpdate():e.handlerSave())}))})),handlerUpdate:function(){var t=this;this.loading=!0,this.pram.shareTitle=this.pram.title,this.pram.shareSynopsis=this.pram.synopsis,n["e"](this.pram).then((function(e){t.$message.success("编辑文章成功"),t.loading=!1,t.$router.push({path:"/content/articleManager"})})).catch((function(){t.loading=!1}))},handlerSave:function(){var t=this;this.loading=!0,this.pram.shareTitle=this.pram.title,this.pram.shareSynopsis=this.pram.synopsis,n["a"](this.pram).then((function(e){t.$message.success("新增文章成功"),t.loading=!1,t.$router.push({path:"/content/articleManager"})})).catch((function(){t.loading=!1}))},setTagsViewTitle:function(){var t="编辑文章",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)}}},p=c,m=a("2877"),u=Object(m["a"])(p,r,s,!1,null,"3bfcfb68",null);e["default"]=u.exports}}]);