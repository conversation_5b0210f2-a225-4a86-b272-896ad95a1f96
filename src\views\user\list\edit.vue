<template>
  <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
    <el-form-item label="用户编号：">
      <el-input v-model="ruleForm.id" disabled class="selWidth"></el-input>
    </el-form-item>
    <el-form-item label="用户地址：">
      <el-input v-model="address" disabled class="selWidth"></el-input>
    </el-form-item>
    <el-form-item label="用户标签：">
      <el-select v-model="labelData" placeholder="请选择" class="selWidth" clearable filterable multiple>
        <el-option :value="item.id" v-for="(item, index) in labelLists" :key="index" :label="item.name"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="生日：">
      <el-date-picker
        v-model="ruleForm.birthday"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item label="用户备注：">
      <el-input v-model="ruleForm.mark" type="textarea" class="selWidth"></el-input>
    </el-form-item>
    <el-form-item label="推广员">
      <el-radio-group v-model="ruleForm.isPromoter">
        <el-radio :label="true">开启</el-radio>
        <el-radio :label="false">关闭</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="状态">
      <el-radio-group v-model="ruleForm.status">
        <el-radio :label="true">开启</el-radio>
        <el-radio :label="false">关闭</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm('ruleForm')" v-hasPermi="['platform:user:update']">提交</el-button>
      <el-button @click="resetForm('ruleForm')">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { groupListApi, levelListApi, tagListApi, userInfoApi, userUpdateApi } from '@/api/user';
import { Debounce } from '@/utils/validate';
const defaultObj = {
  // birthday: '',
  // cardId: '',
  id: null,
  mark: '',
  //  phone: '',
  // realName: '',
  addres: '',
  groupId: '',
  level: '',
  isPromoter: false,
  status: false,
};
export default {
  name: 'UserEdit',
  props: {
    userInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      ruleForm: Object.assign({}, defaultObj),
      groupData: [],
      labelData: [],
      rules: {},
      labelLists: [],
    };
  },

  computed: {
    address() {
      return this.ruleForm.province + this.ruleForm.city
    }
  },
  mounted() {
    if (this.userInfo) this.ruleForm = JSON.parse(JSON.stringify(this.userInfo));
    this.labelLists = JSON.parse(localStorage.getItem('tagAllList')) || [];
    this.labelData = this.userInfo.tagId ? this.userInfo.tagId.split(',').map(Number) : [];
  },
  methods: {
    submitForm: Debounce(function (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ruleForm.tagId = this.labelData.join(',');
          userUpdateApi({ id: this.ruleForm.id }, this.ruleForm).then(async (res) => {
            this.$message.success('编辑成功');
            this.$parent.$parent.visible = false;
            this.$parent.$parent.getList();
          });
        } else {
          return false;
        }
      });
    }),
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$emit('resetForm');
    },
  },
};
</script>

<style scoped>
.selWidth {
  width: 90%;
}
</style>
