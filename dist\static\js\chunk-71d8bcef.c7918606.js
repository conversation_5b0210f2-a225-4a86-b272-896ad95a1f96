(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-71d8bcef"],{"340e":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-tabs",{attrs:{type:"border-card"}},[r("el-tab-pane",{attrs:{label:"类目申请"}},[r("category-list")],1),t._v(" "),r("el-tab-pane",{attrs:{label:"品牌申请"}},[r("brand-list")],1)],1)],1)},n=[],i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-form",{attrs:{inline:""}},[r("el-form-item",{attrs:{label:"类目名称:"}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"类目名称",clearable:""},model:{value:t.tableFrom.catName,callback:function(e){t.$set(t.tableFrom,"catName",e)},expression:"tableFrom.catName"}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:pay:component:product:list"],expression:"['admin:pay:component:product:list']"}],attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.search()}},slot:"append"})],1)],1)],1)],1),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","row-key":"value","tree-props":{children:"children",hasChildren:"hasChildren"},"header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{prop:"",label:"类目","min-width":"130",align:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.productQualificationType>0||e.row.qualificationType>0?r("el-popover",{attrs:{placement:"top-start",title:e.row.thirdCatName,width:"200",trigger:"hover",content:t._f("filterEmpty")(e.row.qualification)}},[r("el-link",{attrs:{slot:"reference",type:"info",underline:!1},slot:"reference"},[t._v(t._s(e.row.thirdCatName))])],1):t._e(),t._v(" "),0===e.row.productQualificationType&&0===e.row.qualificationType?r("el-link",{attrs:{slot:"reference",type:"info",underline:!1},slot:"reference"},[t._v(t._s(e.row.thirdCatName))]):t._e(),t._v(" "),r("span",[t._v(" < "+t._s(e.row.secondCatName)+" < "+t._s(e.row.firstCatName)+" ")])]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"类目资质",prop:""},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.qualificationType?r("el-link",{attrs:{type:"success",underline:!1}},[t._v("不需要")]):t._e(),t._v(" "),1===e.row.qualificationType?r("el-link",{attrs:{type:"warning",underline:!1}},[t._v("选填")]):t._e(),t._v(" "),2===e.row.qualificationType?r("el-link",{attrs:{type:"danger",underline:!1}},[t._v("必须")]):t._e()]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"商品资质",prop:""},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.productQualificationType?r("el-link",{attrs:{type:"success",underline:!1}},[t._v("不需要")]):t._e(),t._v(" "),1===e.row.productQualificationType?r("el-link",{attrs:{type:"warning",underline:!1}},[t._v("选填")]):t._e(),t._v(" "),2===e.row.productQualificationType?r("el-link",{attrs:{type:"danger",underline:!1}},[t._v("必须")]):t._e()]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"状态"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.productQualificationType&&0===e.row.qualificationType?r("span",[t._v("-")]):0===e.row.status?r("span",[t._v(" 待提审 ")]):1===e.row.status?r("span",[t._v(" 微信审核中 ")]):2===e.row.status?r("span",[t._v(" 微信审核失败 ")]):3===e.row.status?r("span",[t._v(" 微信审核成功 ")]):t._e()]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"审核时间",prop:"auditTime"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(t._f("filterEmpty")(e.row.auditTime))+"\n        ")]}}])}),t._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[(e.row.productQualificationType>0||e.row.qualificationType>0)&&e.row.status<1?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:draft:review"],expression:"['merchant:pay:component:product:draft:review']"}],attrs:{size:"small",type:"text"},on:{click:function(r){return t.handleAudit(e.row,!0)}}},[t._v("上传资质")]):3===e.row.status&&(e.row.productQualificationType>0||e.row.qualificationType>0)?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:draft:review"],expression:"['merchant:pay:component:product:draft:review']"}],attrs:{size:"small",type:"text"},on:{click:function(r){return t.handleAudit(e.row.id,!1)}}},[t._v("查看资质")]):r("span",[t._v("-")])]}}])})],1),t._v(" "),r("div",{staticClass:"block mb20"},[r("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableData.data.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1),t._v(" "),r("el-dialog",{attrs:{title:t.forSubmitAuditCat.dialog.title,visible:t.forSubmitAuditCat.dialog.visible,"destroy-on-close":"","close-on-click-modal":!1},on:{close:function(e){t.forSubmitAuditCat.dialog.visible=!1}}},[r("submit-cat-audit",{attrs:{catTitle:t.forSubmitAuditCat.catTitle,params:t.forSubmitAuditCat.params},on:{closeDia:function(e){t.forSubmitAuditCat.dialog.visible=!1},auditSuccess:function(e){return t.forSubmitAuditCatSuccess()}}})],1)],1)],1)},o=[],l=r("8bbf"),u=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-form",{attrs:{"label-width":"200px"}},[r("el-form-item",{attrs:{label:"服务类目:"}},[t._v("\n      "+t._s(t.catTitle)+"\n    ")]),t._v(" "),r("el-form-item",{attrs:{label:"营业执照或组织机构证件:",prop:"license"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.catImages.license?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.catImages.license}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{label:"类目资质(最多10张):",prop:"certificate"}},[r("div",{staticClass:"acea-row"},[t._l(t.catImages.certificate,(function(e,a){return r("div",{key:a,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(r){return t.handleDragStart(r,e,"certificate")},dragover:function(r){return r.preventDefault(),t.handleDragOver(r,e,"certificate")},dragenter:function(r){return t.handleDragEnter(r,e,"certificate")},dragend:function(r){return t.handleDragEnd(r,e,"certificate")}}},[r("img",{attrs:{src:e}}),t._v(" "),r("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(a,"certificate")}}})])})),t._v(" "),t.catImages.certificate.length<10?r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2")}}},[r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2)]),t._v(" "),r("el-form-item",{attrs:{align:"right"}},[r("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.handleSubmitCatAudit()}}},[t._v("提交")]),t._v(" "),r("el-button",{nativeOn:{click:function(e){return t.$emit("closeDia")}}},[t._v("取消")])],1)],1)],1)},c=[];function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function d(t){return h(t)||f(t)||m(t)||p()}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"===typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function f(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function h(t){if(Array.isArray(t))return b(t)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(q){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var i=e&&e.prototype instanceof m?e:m,o=Object.create(i.prototype),l=new R(n||[]);return a(o,"_invoke",{value:k(t,r,l)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(q){return{type:"throw",arg:q}}}t.wrap=c;var p={};function m(){}function f(){}function h(){}var b={};u(b,i,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(D([])));y&&y!==e&&r.call(y,i)&&(b=y);var _=h.prototype=m.prototype=Object.create(b);function w(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function A(t,e){function n(a,i,o,l){var u=d(t[a],t,i);if("throw"!==u.type){var c=u.arg,p=c.value;return p&&"object"==s(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,o,l)}),(function(t){n("throw",t,o,l)})):e.resolve(p).then((function(t){c.value=t,o(c)}),(function(t){return n("throw",t,o,l)}))}l(u.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function k(t,e,r){var a="suspendedStart";return function(n,i){if("executing"===a)throw new Error("Generator is already running");if("completed"===a){if("throw"===n)throw i;return L()}for(r.method=n,r.arg=i;;){var o=r.delegate;if(o){var l=x(o,r);if(l){if(l===p)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===a)throw a="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a="executing";var u=d(t,e,r);if("normal"===u.type){if(a=r.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a="completed",r.method="throw",r.arg=u.arg)}}}function x(t,e){var r=e.method,a=t.iterator[r];if(void 0===a)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var n=d(a,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,p;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function R(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function D(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,n=function e(){for(;++a<t.length;)if(r.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:L}}function L(){return{value:void 0,done:!0}}return f.prototype=h,a(_,"constructor",{value:h,configurable:!0}),a(h,"constructor",{value:f,configurable:!0}),f.displayName=u(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,l,"GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},w(A.prototype),u(A.prototype,o,(function(){return this})),t.AsyncIterator=A,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new A(c(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},w(_),u(_,l,"Generator"),u(_,i,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},t.values=D,R.prototype={constructor:R,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(I),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(r,a){return o.type="throw",o.arg=t,e.next=r,a&&(e.method="next",e.arg=void 0),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],o=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),I(r),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;I(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:D(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),p}},t}function g(t,e,r,a,n,i,o){try{var l=t[i](o),u=l.value}catch(c){return void r(c)}l.done?e(u):Promise.resolve(u).then(a,n)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){g(i,a,n,o,l,"next",t)}function l(t){g(i,a,n,o,l,"throw",t)}o(void 0)}))}}var _={name:"submitCatAudit",props:{catTitle:{type:String,require:!0},params:{audit_req:{category_info:{certificate:[],level1:null,level2:null,level3:null},license:"",scene_group_list:[1]}}},data:function(){return{catImages:{license:null,certificate:[]}}},mounted:function(){},create:function(){},methods:{handleSubmitCatAudit:function(){var t=y(v().mark((function t(){var e,r,a=this;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.catImages.license||0!==this.catImages.certificate.length||this.$message.warning("正确填写类目审核表单"),t.next=3,this.getShopImgUpload([this.catImages.license],[]);case 3:return e=t.sent,t.next=6,this.getShopImgUpload(this.catImages.certificate,[]);case 6:r=t.sent,this.params.audit_req.license=e[0],this.params.audit_req.category_info.certificate=r,Object(l["c"])(this.params).then((function(t){a.$message.success("提交类目审核成功"),a.$emit("auditSuccess")})).catch((function(t){a.$message.error(t)}));case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),modalPicTap:function(t,e,r){var a=this;this.$modalUpload((function(r){"1"!==t||e||(a.catImages.license=r[0].sattDir),"2"!==t||e||r.map((function(t){a.catImages.certificate.push(t.sattDir)}))}),t,"payComponent")},getShopImgUpload:function(){var t=y(v().mark((function t(e,r){var a,n;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.t0=v().keys(e);case 1:if((t.t1=t.t0()).done){t.next=9;break}return a=t.t1.value,t.next=5,this.getImgData(e[a]);case 5:n=t.sent,r.push(n),t.next=1;break;case 9:return t.abrupt("return",r);case 10:case"end":return t.stop()}}),t,this)})));function e(e,r){return t.apply(this,arguments)}return e}(),getImgData:function(t){return new Promise((function(e,r){Object(l["i"])({imgUrl:t,respType:1,uploadType:1}).then((function(t){e(t.img_info.temp_img_url)}))}))},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e,r){var a;if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var n=[];"certificate"===r&&(n=d(this.catImages.certificate));var i=n.indexOf(this.dragging),o=n.indexOf(e);(a=n).splice.apply(a,[o,0].concat(d(n.splice(i,1)))),"certificate"===r&&(this.catImages.certificate=n)}},handleRemove:function(t,e){"certificate"===e&&this.catImages.certificate.splice(t,1)}}},w=_,A=r("2877"),k=Object(A["a"])(w,u,c,!1,null,"1314a3cc",null),x=k.exports,C={name:"categoryList",components:{SubmitCatAudit:x},data:function(){return{listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,catName:null},forSubmitAuditCat:{dialog:{title:"上传微信类目资质",visible:!1},catTitle:null,params:{audit_req:{category_info:{certificate:[],level1:null,level2:null,level3:null},license:"",scene_group_list:[1]}}}}},mounted:function(){this.search()},methods:{search:function(){this.tableFrom.page=1,this.tableFrom.limit=20,this.getList(this.tableFrom)},getList:function(t){var e=this;this.listLoading=!0,Object(l["d"])(t).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList(this.tableFrom)},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList(this.tableFrom)},handleAudit:function(t){this.forSubmitAuditCat.catTitle=t.firstCatName+" > "+t.secondCatName+" > "+t.thirdCatName,this.forSubmitAuditCat.params.audit_req.category_info.level1=t.firstCatId,this.forSubmitAuditCat.params.audit_req.category_info.level2=t.secondCatId,this.forSubmitAuditCat.params.audit_req.category_info.level3=t.thirdCatId,this.forSubmitAuditCat.dialog.title="上传类目资质",this.forSubmitAuditCat.dialog.visible=!0},forSubmitAuditCatSuccess:function(){this.forSubmitAuditCat.dialog.visible=!1,this.getList(this.tableFrom)}}},I=C,R=Object(A["a"])(I,i,o,!1,null,"60a99db4",null),D=R.exports,L=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("el-card",{staticClass:"box-card"},[r("el-form",{attrs:{inline:""}},[r("el-form-item",[r("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){t.submitBrandAudit.visible=!0}}},[t._v("添加品牌")])],1)],1),t._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","row-key":"value","tree-props":{children:"children",hasChildren:"hasChildren"},"header-cell-style":{fontWeight:"bold"}}},[r("el-table-column",{attrs:{prop:"id",label:"ID"}}),t._v(" "),r("el-table-column",{attrs:{prop:"brandId","min-width":"70px",label:"品牌ID"}}),t._v(" "),r("el-table-column",{attrs:{prop:"auditId","min-width":"200px",label:"审核单ID"}}),t._v(" "),r("el-table-column",{attrs:{prop:"license","min-width":"150px",label:"营业执照或组织机构代码证"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.license.split(","),(function(t,e){return r("img",{key:e,attrs:{src:t,height:"80px"}})}))}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"brandAuditType","min-width":"150px",label:"认证审核类型"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkType","min-width":"150px",label:"商标分类"}}),t._v(" "),r("el-table-column",{attrs:{prop:"brandManagementType","min-width":"150px",label:"品牌经营类型"}}),t._v(" "),r("el-table-column",{attrs:{prop:"commodityOriginType","min-width":"150px",label:"商品产地是否进口"}}),t._v(" "),r("el-table-column",{attrs:{prop:"brandWording","min-width":"150px",label:"商标/品牌词"}}),t._v(" "),r("el-table-column",{attrs:{prop:"saleAuthorization","min-width":"150px",label:"销售授权书"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkRegistrationCertificate","min-width":"150px",label:"商标注册证书"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkChangeCertificate","min-width":"150px",label:"商标变更证明"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkRegistrant","min-width":"150px",label:"商标注册人姓名"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkRegistrantNu","min-width":"150px",label:"商标注册号/申请号"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkAuthorizationPeriod","min-width":"150px",label:"商标有效期"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkRegistrationApplication","min-width":"150px",label:"商标注册申请受理通知书"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkApplicant","min-width":"150px",label:"商标申请人姓名"}}),t._v(" "),r("el-table-column",{attrs:{prop:"trademarkApplicationTime","min-width":"150px",label:"商标申请时间"}}),t._v(" "),r("el-table-column",{attrs:{prop:"importedGoodsForm",label:"报关单"}}),t._v(" "),r("el-table-column",{attrs:{prop:"status",fixed:"right",label:"审核状态"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.status?r("el-tag",{attrs:{type:"warning"}},[t._v("审核中")]):t._e(),t._v(" "),1===e.row.status?r("el-tag",{attrs:{type:"success"}},[t._v("审核成功")]):t._e(),t._v(" "),r("el-popover",{attrs:{placement:"top-start",title:"拒绝原因",width:"200",trigger:"hover",content:e.row.rejectReason}},[9===e.row.status?r("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("审核拒绝")]):t._e()],1)]}}])}),t._v(" "),r("el-table-column",{attrs:{prop:"createTime",fixed:"right",label:"创建时间"}}),t._v(" "),r("el-table-column",{attrs:{label:"操作",fixed:"right","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.status?r("el-button",{attrs:{type:"text"},nativeOn:{click:function(r){return t.getCatAndBrandResult(e.row.auditId)}}},[t._v("手动更新")]):r("span",[t._v(" - ")])]}}])})],1),t._v(" "),r("div",{staticClass:"block mb20"},[r("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableData.data.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),r("el-dialog",{attrs:{title:t.submitBrandAudit.title,visible:t.submitBrandAudit.visible,"destroy-on-close":"","close-on-click-modal":!1},on:{close:function(e){t.submitBrandAudit.visible=!1}}},[r("el-form",{ref:"brandSubmitFromRules",attrs:{model:t.submitBrandAudit,inline:"",rules:t.brandSubmitFromRules,"label-width":"200px",size:"mini"}},[r("el-form-item",{attrs:{label:"商标分类",prop:"pramData.auditReq.brandInfo.trademarkType"}},[r("el-select",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkType,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"trademarkType",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.trademarkType"}},t._l(45,(function(e){return r("el-option",{key:e,attrs:{value:e,label:"第 "+e+" 类"}},[t._v('第 "'+t._s(e)+'" 类')])})),1)],1),t._v(" "),r("el-form-item",{attrs:{label:"选择品牌经营类型"}},[r("el-select",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.brandManagementType,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"brandManagementType",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.brandManagementType"}},[r("el-option",{attrs:{value:"1",label:"自有品牌"}},[t._v("自有品牌")]),t._v(" "),r("el-option",{attrs:{value:"2",label:"代理品牌"}},[t._v("代理品牌")]),t._v(" "),r("el-option",{attrs:{value:"3",label:"无品牌"}},[t._v("无品牌")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"商品产地是否进口"}},[r("el-select",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.commodityOriginType,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"commodityOriginType",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.commodityOriginType"}},[r("el-option",{attrs:{value:"1",label:"是"}},[t._v("是")]),t._v(" "),r("el-option",{attrs:{value:"2",label:"否"}},[t._v("否")])],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"商标有效期"}},[r("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择 商标有效期"},model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkAuthorizationPeriod,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"trademarkAuthorizationPeriod",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.trademarkAuthorizationPeriod"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"商标/品牌词"}},[r("el-input",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.brandWording,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"brandWording",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.brandWording"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"商标申请时间"}},[r("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择 商标申请时间"},model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkApplicationTime,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"trademarkApplicationTime",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.trademarkApplicationTime"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"商标注册人姓名"}},[r("el-input",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrant,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"trademarkRegistrant",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrant"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"商标注册号/申请号"}},[r("el-input",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrantNu,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"trademarkRegistrantNu",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrantNu"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"认证审核类型"}},[r("el-select",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.brandAuditType,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"brandAuditType",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.brandAuditType"}},[r("el-option",{attrs:{value:"1",label:"国内品牌申请 -R 标"}}),t._v(" "),r("el-option",{attrs:{value:"2",label:"国内品牌申请 -TM 标"}}),t._v(" "),r("el-option",{attrs:{value:"3",label:"海外品牌申请 -R 标"}}),t._v(" "),r("el-option",{attrs:{value:"4",label:"海外品牌申请 -TM 标"}})],1)],1),t._v(" "),r("el-form-item",{attrs:{label:"商标申请人姓名"}},[r("el-input",{model:{value:t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkApplicant,callback:function(e){t.$set(t.submitBrandAudit.pramData.auditReq.brandInfo,"trademarkApplicant",e)},expression:"submitBrandAudit.pramData.auditReq.brandInfo.trademarkApplicant"}})],1),t._v(" "),r("el-form-item",{attrs:{label:"营业执照或组织机构代码证",prop:"pramData.auditReq.license"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1")}}},[t.submitBrandAudit.pramData.auditReq.license?r("div",{staticClass:"pictrue"},[r("img",{attrs:{src:t.submitBrandAudit.pramData.auditReq.license}})]):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{label:"销售授权书（如商持人为自然人，还需提供有其签名的身份证正反面扫描件)"}},[r("span",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2")}}},[t.submitBrandAudit.pramData.auditReq.brandInfo.saleAuthorizationArr.length>0?r("div",{staticClass:"pictrue"},t._l(t.submitBrandAudit.pramData.auditReq.brandInfo.saleAuthorizationArr,(function(t){return r("img",{key:t,attrs:{src:t}})})),0):t._e(),t._v(" "),t.submitBrandAudit.pramData.auditReq.brandInfo.saleAuthorizationArr.length<=10?r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})]):t._e()])]),t._v(" "),r("el-form-item",{attrs:{label:"商标注册证书"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("3")}}},[t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationCertificateArr.length>0?r("div",{staticClass:"pictrue"},t._l(t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationCertificateArr,(function(t){return r("img",{key:t,attrs:{src:t}})})),0):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{label:"商标变更证明"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("4")}}},[t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkChangeCertificateArr.length>0?r("div",{staticClass:"pictrue"},t._l(t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkChangeCertificateArr,(function(t){return r("img",{key:t,attrs:{src:t}})})),0):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{label:"商标注册申请受理通知书"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("5")}}},[t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationApplicationArr.length>0?r("div",{staticClass:"pictrue"},t._l(t.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationApplicationArr,(function(t){return r("img",{key:t,attrs:{src:t}})})),0):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{label:"中华人民共和国海关进口货物报关单"}},[r("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("6")}}},[t.submitBrandAudit.pramData.auditReq.brandInfo.importedGoodsFormArr.length>0?r("div",{staticClass:"pictrue"},t._l(t.submitBrandAudit.pramData.auditReq.brandInfo.importedGoodsFormArr,(function(t){return r("img",{key:t,attrs:{src:t}})})),0):r("div",{staticClass:"upLoad"},[r("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),t._v(" "),r("el-form-item",{attrs:{align:"right"}},[r("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.handleSubmitBrandAudit("brandSubmitFromRules")}}},[t._v("提交")]),t._v(" "),r("el-button",{nativeOn:{click:function(e){t.submitBrandAudit.visible=!1}}},[t._v("取消")])],1)],1)],1)],1)},q=[];function S(t){return S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},S(t)}function B(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */B=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(D){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var i=e&&e.prototype instanceof p?e:p,o=Object.create(i.prototype),l=new C(n||[]);return a(o,"_invoke",{value:w(t,r,l)}),o}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(D){return{type:"throw",arg:D}}}t.wrap=c;var d={};function p(){}function m(){}function f(){}var h={};u(h,i,(function(){return this}));var b=Object.getPrototypeOf,v=b&&b(b(I([])));v&&v!==e&&r.call(v,i)&&(h=v);var g=f.prototype=p.prototype=Object.create(h);function y(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function n(a,i,o,l){var u=s(t[a],t,i);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==S(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,o,l)}),(function(t){n("throw",t,o,l)})):e.resolve(d).then((function(t){c.value=t,o(c)}),(function(t){return n("throw",t,o,l)}))}l(u.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function w(t,e,r){var a="suspendedStart";return function(n,i){if("executing"===a)throw new Error("Generator is already running");if("completed"===a){if("throw"===n)throw i;return R()}for(r.method=n,r.arg=i;;){var o=r.delegate;if(o){var l=A(o,r);if(l){if(l===d)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===a)throw a="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a="executing";var u=s(t,e,r);if("normal"===u.type){if(a=r.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a="completed",r.method="throw",r.arg=u.arg)}}}function A(t,e){var r=e.method,a=t.iterator[r];if(void 0===a)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,A(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var n=s(a,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,d;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function I(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,n=function e(){for(;++a<t.length;)if(r.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:R}}function R(){return{value:void 0,done:!0}}return m.prototype=f,a(g,"constructor",{value:f,configurable:!0}),a(f,"constructor",{value:m,configurable:!0}),m.displayName=u(f,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,u(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},y(_.prototype),u(_.prototype,o,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new _(c(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},y(g),u(g,l,"Generator"),u(g,i,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},t.values=I,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function a(r,a){return o.type="throw",o.arg=t,e.next=r,a&&(e.method="next",e.arg=void 0),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],o=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),x(r),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;x(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:I(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),d}},t}function T(t,e,r,a,n,i,o){try{var l=t[i](o),u=l.value}catch(c){return void r(c)}l.done?e(u):Promise.resolve(u).then(a,n)}function O(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){T(i,a,n,o,l,"next",t)}function l(t){T(i,a,n,o,l,"throw",t)}o(void 0)}))}}var j={name:"brandList",data:function(){return{listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,brandWording:null},submitBrandAudit:{title:"自定义交易组件 - 品牌申请",visible:!1,pramData:{auditReq:{license:null,brandInfo:{brandAuditType:null,trademarkType:null,brandManagementType:null,commodityOriginType:null,brandWording:null,saleAuthorizationArr:[],saleAuthorization:null,trademarkRegistrationCertificateArr:[],trademarkRegistrationCertificate:null,trademarkChangeCertificateArr:[],trademarkChangeCertificate:null,trademarkRegistrant:null,trademarkRegistrantNu:null,trademarkAuthorizationPeriod:null,trademarkRegistrationApplicationArr:[],trademarkRegistrationApplication:null,trademarkApplicant:null,trademarkApplicationTime:null,importedGoodsFormArr:[],importedGoodsForm:null}}}},brandSubmitFromRules:{"pramData.auditReq.brandInfo.trademarkType":[{required:!0,message:"请选择 商标分类",trigger:"blur"}],"pramData.auditReq.license":[{required:!0,message:"营业执照或组织机构代码证 不能为空",trigger:"blur"}]}}},mounted:function(){this.handleSearch()},methods:{handleSearch:function(){this.tableFrom.page=1,this.tableFrom.limit=20,this.getList(this.tableFrom)},handleSubmitBrandAudit:function(t){var e=this;this.$refs[t].validate(function(){var t=O(B().mark((function t(r){var a;return B().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r){t.next=28;break}return t.next=3,e.getShopImgUpload([e.submitBrandAudit.pramData.auditReq.license],[]);case 3:return e.submitBrandAudit.pramData.auditReq.license=t.sent.join(","),t.next=6,e.getShopImgUpload(e.submitBrandAudit.pramData.auditReq.brandInfo.saleAuthorizationArr,[]);case 6:return e.submitBrandAudit.pramData.auditReq.brandInfo.saleAuthorization=t.sent,t.next=9,e.getShopImgUpload(e.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationCertificateArr,[]);case 9:return e.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationCertificate=t.sent,t.next=12,e.getShopImgUpload(e.submitBrandAudit.pramData.auditReq.brandInfo.trademarkChangeCertificateArr,[]);case 12:return e.submitBrandAudit.pramData.auditReq.brandInfo.trademarkChangeCertificate=t.sent,t.next=15,e.getShopImgUpload(e.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationApplicationArr,[]);case 15:return e.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationApplication=t.sent,t.next=18,e.getShopImgUpload(e.submitBrandAudit.pramData.auditReq.brandInfo.importedGoodsFormArr,[]);case 18:e.submitBrandAudit.pramData.auditReq.brandInfo.importedGoodsForm=t.sent,a=JSON.parse(JSON.stringify(e.submitBrandAudit.pramData)),delete a.auditReq.brandInfo.importedGoodsFormArr,delete a.auditReq.brandInfo.saleAuthorizationArr,delete a.auditReq.brandInfo.trademarkChangeCertificateArr,delete a.auditReq.brandInfo.trademarkRegistrationApplicationArr,delete a.auditReq.brandInfo.trademarkRegistrationCertificateArr,Object(l["j"])(a).then((function(t){e.getList(e.tableFrom)})).finally((function(){e.submitBrandAudit.visible=!1})),t.next=30;break;case 28:return console.log("error submit!!"),t.abrupt("return",!1);case 30:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getCatAndBrandResult:function(t){var e=this;Object(l["a"])({audit_id:t}).then((function(t){e.getList(e.tableFrom)})).catch((function(t){e.$message.error("手动同步审核结果失败:"+t)})).finally((function(){e.$message.info("手动同步审核结果完成")}))},getList:function(t){var e=this;this.listLoading=!0,Object(l["b"])(t).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){})).finally((function(){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList(this.tableFrom)},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList(this.tableFrom)},modalPicTap:function(t,e,r){var a=this;this.$modalUpload((function(r){"1"!==t||e||(a.submitBrandAudit.pramData.auditReq.license=r[0].sattDir),"2"!==t||e||r.map((function(t){a.submitBrandAudit.pramData.auditReq.brandInfo.saleAuthorizationArr.push(t.sattDir)})),"3"!==t||e||r.map((function(t){a.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationCertificateArr.push(t.sattDir)})),"4"!==t||e||r.map((function(t){a.submitBrandAudit.pramData.auditReq.brandInfo.trademarkChangeCertificateArr.push(t.sattDir)})),"5"!==t||e||r.map((function(t){a.submitBrandAudit.pramData.auditReq.brandInfo.trademarkRegistrationApplicationArr.push(t.sattDir)})),"6"!==t||e||r.map((function(t){a.submitBrandAudit.pramData.auditReq.brandInfo.importedGoodsFormArr.push(t.sattDir)}))}),t,"payComponent")},getShopImgUpload:function(){var t=O(B().mark((function t(e,r){var a,n;return B().wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.t0=B().keys(e);case 1:if((t.t1=t.t0()).done){t.next=9;break}return a=t.t1.value,t.next=5,this.getImgData(e[a]);case 5:n=t.sent,r.push(n),t.next=1;break;case 9:return t.abrupt("return",r);case 10:case"end":return t.stop()}}),t,this)})));function e(e,r){return t.apply(this,arguments)}return e}(),getImgData:function(t){return new Promise((function(e,r){Object(l["i"])({imgUrl:t,respType:1,uploadType:1}).then((function(t){e(t.img_info.temp_img_url)}))}))}}},E=j,P=Object(A["a"])(E,L,q,!1,null,"71a0c44c",null),F=P.exports,N={name:"weChatcategoryAndBrand",components:{BrandList:F,CategoryList:D},data:function(){return{}},mounted:function(){},methods:{}},z=N,$=(r("a8a0"),Object(A["a"])(z,a,n,!1,null,"c57e9dde",null));e["default"]=$.exports},"5d19":function(t,e,r){},"8bbf":function(t,e,r){"use strict";r.d(e,"h",(function(){return n})),r.d(e,"k",(function(){return i})),r.d(e,"f",(function(){return o})),r.d(e,"n",(function(){return l})),r.d(e,"d",(function(){return u})),r.d(e,"c",(function(){return c})),r.d(e,"o",(function(){return s})),r.d(e,"m",(function(){return d})),r.d(e,"l",(function(){return p})),r.d(e,"e",(function(){return m})),r.d(e,"g",(function(){return f})),r.d(e,"i",(function(){return h})),r.d(e,"b",(function(){return b})),r.d(e,"a",(function(){return v})),r.d(e,"j",(function(){return g}));var a=r("b775");function n(){return Object(a["a"])({url:"/admin/platform/pay/component/register/register/check",method:"get"})}function i(t){return Object(a["a"])({url:"/admin/platform/pay/component/register/apply",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/admin/platform/pay/component/draftproduct/draft/list",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/admin/platform/pay/component/product/list",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/admin/platform/pay/component/cat/get/list",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/category/audit",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/admin/pay/component/product/listing/".concat(t),method:"post"})}function d(t){return Object(a["a"])({url:"/admin/pay/component/product/delisting/".concat(t),method:"post"})}function p(t){return Object(a["a"])({url:"/admin/pay/component/product/delete/".concat(t),method:"post"})}function m(t){return Object(a["a"])({url:"/admin/platform/pay/component/draftproduct/draft/get/".concat(t),method:"get"})}function f(t){return Object(a["a"])({url:"/admin/platform/pay/component/draftproduct/review",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/img/upload",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/brand/list",method:"get",data:t})}function v(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/audit/result",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/admin/platform/pay/component/shop/brand/audit",method:"post",data:t})}},a8a0:function(t,e,r){"use strict";r("5d19")}}]);