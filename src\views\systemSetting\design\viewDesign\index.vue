<template>
  <div class="divBox" style="padding-top: 0">
    <el-card class="box-card" :body-style="{ paddingLeft: '0px', paddingBottom: '0px' }">
      <div class="flex">
        <div class="tab_view">
          <div
            class="cell_item"
            :class="{ tab_active: listActive == index }"
            v-for="(item, index) in tabList"
            :key="index"
            @click="ProductNavTab(index)"
          >
            {{ item }}
          </div>
        </div>
        <div class="leftModel">
          <div v-show="currentPage == 'home'" class="current_home">
            <div class="model_header">
              <!-- <img :src="logoUrl" alt="" /> -->
              <div class="header_search">
                <span class="iconfont iconios-search"></span>
              </div>
            </div>
            <!-- <div class="model_banner cur_pointer" @click="handleMessage('indexBanner')">
              <el-carousel indicator-position="none" height="139px">
                <el-carousel-item v-for="(item, index) in dataList[1]" :key="index">
                  <img
                    :src="item.pic"
                    alt=""
                    :class="{ select_ctive: shows == 1 }"
                    style="width: 100%; border-radius: 4px"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
            <div class="model_news cur_pointer" :class="{ select_ctive: shows == 2 }">
              <img src="@/assets/imgs/new_header1.png" alt="" style="width: 64px; height: 17px" />
              <span style="color: #ccc">|</span>
              <p>{{ newsInfo }}</p>
              <i class="el-icon-arrow-right"></i>
            </div> -->
            <br>
            <div
              class="model_nav cur_pointer"
              :class="{ select_ctive: shows == 3 }"
              @click="handleMessage('indexMenu')"
            >
              <div class="model_nav_item" v-for="(item, index) in dataList[0]" :key="index">
                <div>
                  <img :src="item.pic" alt="" />
                </div>
                <p>{{ item.name }}</p>
              </div>
            </div>
            <div class="moni_goods cur_pointer" :class="{ select_ctive: shows == 7 }">
              <img :src="mockGoodsImg" alt="" />
            </div>
          </div>
          <div v-show="currentPage == 'cate'">
            <img :src="cateImg" alt="" style="width: 100%" />
          </div>
          <div v-show="currentPage == 'user'">
            <div class="user_head">
              <div class="user_bg" :style="{ backgroundImage: 'url(' + urlbg + ')' }">
                <div class="user_card">
                  <div class="user_info">
                    <img :src="menuInfo.userDefaultAvatar" alt="" />
                    <div class="info">
                      <p class="nick_name">用户信息</p>
                      <p class="phone">123456</p>
                    </div>
                  </div>
                  <div class="num_wrapper">
                    <div class="num_wrap_item">
                      <p class="num_item_bold">0</p>
                      <p class="num_title">余额</p>
                    </div>
                    <div class="num_wrap_item">
                      <p class="num_item_bold">0</p>
                      <p class="num_title">积分</p>
                    </div>
                    <div class="num_wrap_item">
                      <p class="num_item_bold">0</p>
                      <p class="num_title">优惠券</p>
                    </div>
                    <div class="num_wrap_item">
                      <p class="num_item_bold">0</p>
                      <p class="num_title">收藏</p>
                    </div>
                  </div>
                </div>
                <div class="order_wrap">
                  <div class="order_wrap_tit">
                    <span class="weight_600">订单中心</span>
                    <div>
                      <span class="font_sm">查看全部</span>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                  </div>
                  <div class="order_wrap_list">
                    <div class="order_list_item">
                      <img src="@/assets/imgs/fukuan.png" alt="" />
                      <p>待付款</p>
                    </div>
                    <div class="order_list_item">
                      <img src="@/assets/imgs/fahuo.png" alt="" />
                      <p>待发货</p>
                    </div>
                    <div class="order_list_item">
                      <img src="@/assets/imgs/shouhuo.png" alt="" />
                      <p>待收货</p>
                    </div>
                    <div class="order_list_item">
                      <img src="@/assets/imgs/pingjia.png" alt="" />
                      <p>待评价</p>
                    </div>
                    <div class="order_list_item">
                      <img src="@/assets/imgs/tuikuan.png" alt="" />
                      <p>售后/退款</p>
                    </div>
                  </div>
                </div>
                <!-- <div
                  class="slider_img cur_pointer"
                  :class="{ select_ctive: shows == 5 }"
                  @click="handleMessage('userBanner')"
                >
                  <el-carousel height="69px" :autoplay="true">
                    <el-carousel-item v-for="(item, index) in dataList[4]" :key="index">
                      <img :src="item.pic" alt="" style="height: 69px; display: block; margin: auto" />
                    </el-carousel-item>
                  </el-carousel>
                </div> -->
                <br>
                <div
                  class="user_mens cur_pointer"
                  :class="{ select_ctive: shows == 6 }"
                  @click="handleMessage('userMenu')"
                >
                  <div class="menu_title">我的服务</div>
                  <div class="list_box">
                    <div class="list_box_item" v-for="(item, index) in dataList[2]" :key="index">
                      <img :src="item.pic" alt="" />
                      <p>{{ item.name }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex_between">
          <div class="right-box" v-if="typeName">
            <div class="title-bar">模块配置</div>
            <div class="mobile-config">
              <div v-for="(item, index) in menuList" :key="index" class="item">
                <div
                  class="move-icon"
                  draggable="true"
                  @dragstart="handleDragStart($event, item)"
                  @dragover.prevent="handleDragOver($event, item)"
                  @dragenter="handleDragEnter($event, item)"
                  @dragend="handleDragEnd($event, item)"
                >
                  <span class="iconfont icondrag2"></span>
                </div>
                <div class="picBox" v-if="typeName !== 'indexNews'">
                  <div class="img-box flex justify-center align-center" @click="modalPicTap('1', 'duo', index, true)">
                    <img :src="item.pic" alt="" v-if="item.pic" />
                    <div v-else class="upLoad">
                      <i class="el-icon-camera cameraIconfont" />
                    </div>
                  </div>
                </div>
                <div
                  v-if="index > 0 && typeName !== 'indexTabNav'"
                  class="delect-btn"
                  @click.stop="bindDelete(item, index)"
                >
                  <i class="el-icon-circle-close"></i>
                </div>
                <div class="info">
                  <div v-if="typeName !== 'userBanner'" class="info-item">
                    <span>标题</span>
                    <div class="input-box">
                      <el-input
                        v-if="typeName !== 'indexNews'"
                        v-model="item.name"
                        :placeholder="'请填写' + item.name"
                        maxlength="4"
                      />
                      <el-input v-else v-model="item.info" :placeholder="'请填写' + item.info" />
                    </div>
                  </div>
                  <div class="info-item" v-if="addUrlStatus && typeName !== 'indexTabNav'">
                    <span>链接</span>
                    <div class="input-box" @click="getLink(index)">
                      <el-input v-model="item.url" placeholder="请填写链接" />
                    </div>
                  </div>
                  <div class="info-item" v-if="typeName == 'indexTabNav'">
                    <span>简介</span>
                    <div class="input-box">
                      <el-input v-model="item.info" placeholder="请填写简介" />
                    </div>
                  </div>
                  <div class="info-item">
                    <span>状态</span>
                    <div class="input-box">
                      <el-switch
                        v-model="item.status"
                        :active-value="true"
                        :inactive-value="false"
                        active-text="显示"
                        inactive-text="隐藏"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="add-btn mb20 mt20">
                <el-button @click="addBox" type="primary">添加数据</el-button>
              </div>
            </div>
          </div>
          <linkaddress ref="linkaddres" @linkUrl="linkUrl"></linkaddress>
          <div class="tip" v-if="!typeName && tip == true && cate == false">请选择左侧可操作可编辑区域</div>
          <div v-if="typeName == '' && currentPage == 'cate' && cate == true" class="cate_box_style">
            <div class="title-bar">模块配置</div>
            <div style="margin: 26px 0 26px">
              页面风格：
              <el-radio-group v-model="active" @change="switchTab">
                <el-radio label="1">样式1</el-radio>
                <el-radio label="2">样式2</el-radio>
                <el-radio label="3">样式3</el-radio>
                <el-radio label="4">样式4</el-radio>
              </el-radio-group>
            </div>
            <div>
              <div>
                左侧一级菜单：
                <el-switch v-model="radio" :disabled="active == '1' || active == '2'"></el-switch>
              </div>
            </div>
          </div>
        </div>
        <div v-show="mockGoods" class="cate_box_style">
          <div class="title-bar">模块配置</div>
          <div style="margin: 26px 0 26px">
            <el-radio-group v-model="active1" @change="switchKind">
              <el-radio label="0">样式1</el-radio>
              <el-radio label="1">样式2</el-radio>
              <el-radio label="2">样式3</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="footer_btn">
        <el-button
          type="primary"
          @click="saveConfig"
          v-if="!mockGoods"
          v-hasPermi="[
            'platform:page:layout:index',
            'platform:page:layout:index:menu:save',
            'platform:page:layout:index:banner:save',
            'platform:page:layout:index:banner:save',
            'platform:page:layout:user:menu:save',
          ]"
          >保存</el-button
        >
      </div>
    </el-card>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { designListApi, SaveDataApi, goodDesignList, getDataApi } from '@/api/systemGroup';
import ClipboardJS from 'clipboard';
import { configSaveUniq, configGetUniq } from '@/api/systemConfig';
import linkaddress from '@/components/linkaddress';
import { checkPermi } from '@/utils/permission'; // 权限判断函数
import { Debounce } from '@/utils/validate';
export default {
  name: 'index',
  data() {
    return {
      iframeUrl: '',
      menuList: [],
      menuInfo: {},
      typeName: '',
      currentPage: 'home',
      modelBanner: [
        'https://image.java.crmeb.net/crmebimage/maintain/2021/07/06/c99ee385e94d4711a0ea4be72169a86euwmzuhxbb2.jpg',
      ],
      urlbg: require('@/assets/imgs/user_bg.png'),
      //  indexTab: [],
      dataList: [],
      addUrlStatus: true,
      infoStatus: false,
      showStatus: false,
      shows: 0,
      indextTabMenu: [],
      tabActive: 0,
      cate: false,
      tip: false,
      mockGoods: false,
      mockGoodsImg: require('@/assets/theme/goodsList1.png'),
      showTabNav: true,
      cateArr: [
        { img: require('@/assets/imgs/moren.png'), tit: '默认模板' },
        { img: require('@/assets/imgs/youxuan.png'), tit: '模板1' },
        { img: require('@/assets/imgs/haowu.png'), tit: '模板2' },
        { img: require('@/assets/imgs/shengxian.png'), tit: '模板3' },
      ],
      cateImg: '',
      active: 1,
      active1: 0,
      disabled: false,
      radio: true,
      logoUrl: '',
      newsInfo: '',
      listActive: 0,
      tabList: ['首页', '个人中心'],
      itemIndex: 0,
    };
  },
  components: {
    linkaddress,
  },
  created() {
    this.iframeUrl = `https://app.beta.java.crmeb.net?type=iframeVisualizing`;
  },
  mounted() {
    //监听子页面给当前页面传值
    window.addEventListener('message', this.handleMessage, false);
    this.designList();
    this.$set(this, 'typeName', '');
    this.$set(this, 'tip', true);
    this.$nextTick(function () {
      const clipboard = new ClipboardJS('.copy-data');
      clipboard.on('success', () => {
        this.$message.success('复制成功');
      });
    });
  },
  methods: {
    checkPermi,
    addBox() {
      if (this.menuList.length >= 10 && this.typeName == 'indexMenu') {
        this.$message.warning('设置数据不能超过10条');
      } else if (this.typeName == 'indexTabNav' && this.menuList.length >= 4) {
        this.addUrlStatus = false;
        this.infoStatus = true;
        this.$message.warning('设置数据不能超过4条');
      } else {
        const indexMenu = JSON.parse(JSON.stringify(this.menuList[0]));
        indexMenu.id = null;
        indexMenu.name = '';
        indexMenu.url = '';
        indexMenu.info = '';
        indexMenu.pic = '';
        this.menuList.push(indexMenu);
      }
    },
    // 获取列表值；
    designList() {
      designListApi().then((res) => {
        this.menuInfo = res;
        this.$set(this, 'logoUrl', res.indexLogo);
        let newArr = [];
        let indexMenu = res.indexMenu.filter((item, index, arr) => {
          return item.status == true;
        });
        let indexBanner = res.indexBanner.filter((item, index, arr) => {
          return item.status == true;
        });
        let userMenu = res.userMenu.filter((item, index, arr) => {
          return item.status == true;
        });
        let indexNews = res.indexNews;
        let userBanner = res.userBanner.filter((item, index, arr) => {
          return item.status == true;
        });
        newArr.push(indexMenu, indexBanner, userMenu, indexNews, userBanner);
        this.dataList = newArr;
        this.$set(this, 'newsInfo', indexNews[0] ? indexNews[0].title : '这是一个新闻标题');
      });
      // goodDesignList({ gid: 70 }).then((response) => {
      //   let list = response.list;
      //   let arr = [];
      //   let arr1 = [];
      //   list.forEach((item) => {
      //     let obj = {};
      //     obj.value = JSON.parse(item.value);
      //     obj.id = item.id;
      //     obj.gid = item.gid;
      //     obj.status = item.status;
      //     arr.push(obj);
      //   });
      //   arr.forEach((item1) => {
      //     let obj1 = {};
      //     obj1.pic = item1.value.fields[0].value;
      //     obj1.name = item1.value.fields[1].value;
      //     obj1.info = item1.value.fields[2].value;
      //     obj1.type = item1.value.fields[3].value;
      //     obj1.id = item1.id;
      //     obj1.gid = item1.gid;
      //     // obj1.show = '1';
      //     obj1.status = item1.status;
      //     arr1.push(obj1);
      //     this.indextTabMenu = arr1;
      //     let indexTab = arr1.filter((item, index, arr) => {
      //       return item.status == true;
      //     });
      //     this.indexTab = indexTab;
      //   });
      //   //
      // });
    },
    //
    handleMessage(event) {
      this.typeName = event;
      switch (event) {
        case 'indexMenu':
          this.menuList = this.menuInfo.indexMenu;
          this.shows = 3;
          this.mockGoods = false;
          break;
        case 'indexBanner':
          this.menuList = this.menuInfo.indexBanner;
          this.shows = 1;
          this.mockGoods = false;
          break;
        case 'userMenu':
          this.menuList = this.menuInfo.userMenu;
          this.shows = 6;
          this.mockGoods = false;
          break;
        case 'indexNews':
          this.menuList = this.menuInfo.indexNews;
          this.shows = 2;
          this.mockGoods = false;
          break;
        case 'userBanner':
          this.menuList = this.menuInfo.userBanner;
          this.shows = 5;
          this.mockGoods = false;
          break;
        case 'indexTabNav':
          this.menuList = this.indextTabMenu;
          this.shows = 4;
          this.mockGoods = false;
          break;
        case 'goodsMock':
          this.mockGoods = true;
          this.typeName = '';
          this.tip = false;
          this.shows = 7;
          break;
      }
    },
    switchNav(index) {
      this.tabActive = index;
    },
    // 点击商品图
    modalPicTap(tit, num, i, boolean) {
      const _this = this;
      this.$modalUpload(
        function (img) {
          if (tit === '1' && num === 'duo') {
            _this.menuList[i].pic = img[0].sattDir;
          }
        },
        tit,
        'content',
        true,
      );
    },
    // 删除
    bindDelete(item, index) {
      this.menuList.splice(index, 1);
    },
    saveConfig: Debounce(function () {
      switch (this.typeName) {
        case 'indexMenu':
          this.saveData('indexMenu', '/admin/platform/page/layout/index/menu/save');
          break;
        case 'indexBanner':
          this.saveData('indexBanner', '/admin/platform/page/layout/index/banner/save');
          break;
        case 'userMenu':
          this.saveData('userMenu', '/admin/platform/page/layout/user/menu/save');
          break;
        case 'indexNews':
          this.saveData('indexNews', '/admin/platform/page/layout/index/news/save');
          break;
        case 'userBanner':
          this.saveData('userBanner', '/admin/platform/page/layout/user/banner/save');
          break;
        case 'indexTabNav':
          this.saveData('indexTable', '/admin/platform/page/layout/index/table/save');
      }
    }),
    saveData(param, url) {
      let tArr = this.menuList.filter((item, index, arr) => {
        return item.status === true;
      });
      if (this.typeName === 'indexMenu' && tArr.length < 5) {
        this.$message.warning('设置数据不能小于5条');
      } else if (this.typeName === 'indexTabNav' && tArr.length < 2) {
        this.$message.warning('设置数据不能小于2条');
      } else if (this.typeName === 'indexNews' && tArr.length < 1) {
        this.$message.warning('设置数据不能小于1条');
      } else {
        SaveDataApi({ [param]: this.changeIndex(this.menuList) }, url).then((res) => {
          this.$message.success('保存成功');
          this.designList();
        });
      }
    },
    changeIndex(array) {
      array.map((item, index) => {
        item.sort = index;
      });
      return array;
    },
    // 移动
    handleDragStart(e, item) {
      this.dragging = item;
    },
    handleDragEnd(e, item) {
      this.dragging = null;
    },
    handleDragOver(e) {
      e.dataTransfer.dropEffect = 'move';
    },
    handleDragEnter(e, item) {
      e.dataTransfer.effectAllowed = 'move';
      if (item === this.dragging) {
        return;
      }
      const newItems = [...this.menuList];
      const src = newItems.indexOf(this.dragging);
      const dst = newItems.indexOf(item);
      newItems.splice(dst, 0, ...newItems.splice(src, 1));
      this.menuList = newItems;
    },
    showCurrent(name) {
      this.currentPage = name;
      this.$set(this, 'typeName', '');
      this.$set(this, 'tip', true);
      this.$set(this, 'cate', false);
    },
    showTip() {
      this.$message.warning('暂不支持此操作');
    },
    cateNav() {
      this.currentPage = 'cate';
      this.$set(this, 'typeName', '');
      this.$set(this, 'cate', true);
      this.$set(this, 'mockGoods', false);
      this.getConfig();
    },
    switchTab(index) {
      this.active = index;
      switch (index) {
        case 1:
          this.cateImg = require('@/assets/imgs/moren.png');
          break;
        case 2:
          this.cateImg = require('@/assets/imgs/youxuan.png');
          break;
        case 3:
          this.cateImg = require('@/assets/imgs/haowu.png');
          break;
        case 4:
          this.cateImg = require('@/assets/imgs/shengxian.png');
          break;
      }
    },
    switchKind(index) {
      this.active1 = index;
      switch (index) {
        case '0':
          this.$set(this, 'mockGoodsImg', require('@/assets/theme/goodsList1.png'));
          this.$set(this, 'showTabNav', true);
          break;
        case '1':
          this.$set(this, 'mockGoodsImg', require('@/assets/theme/goodsList2.png'));
          this.$set(this, 'showTabNav', false);
          break;
        case '2':
          this.$set(this, 'mockGoodsImg', require('@/assets/theme/goodsList3.png'));
          this.$set(this, 'showTabNav', false);
          break;
      }
    },
    save: Debounce(function () {
      let data = {
        category_page_config: this.active,
        is_show_category: this.radio,
      };
      SaveDataApi(data, '/admin/page/layout/category/config/save').then((res) => {
        this.$message.success('保存成功');
      });
    }),
    getConfig() {
      getDataApi().then((res) => {
        this.$set(this, 'active', res.categoryConfig);
        this.$set(this, 'radio', res.isShowCategory === 'true' ? true : false);
        this.switchTab(this.active);
      });
    },
    kindSave: Debounce(function () {
      let data = {
        key: 'homePageSaleListStyle',
        value: Number(this.active1) + 1,
      };
      configSaveUniq(data).then((res) => {
        this.$message.success('保存成功');
      });
    }),
    kindGet() {
      configGetUniq({ key: 'homePageSaleListStyle' }).then((res) => {
        let conque = res - 1;
        this.$set(this, 'active1', conque.toString());
        if (res == '1') {
          this.$set(this, 'mockGoodsImg', require('@/assets/theme/goodsList1.png')); //showTabNav
          this.$set(this, 'showTabNav', true);
        } else if (res == '2') {
          this.$set(this, 'mockGoodsImg', require('@/assets/theme/goodsList2.png'));
          this.$set(this, 'showTabNav', false);
        } else if (res == '3') {
          this.$set(this, 'mockGoodsImg', require('@/assets/theme/goodsList3.png'));
          this.$set(this, 'showTabNav', false);
        }
      });
    },
    getLink(index) {
      this.itemIndex = index;
      this.$refs.linkaddres.dialogVisible = true;
    },
    ProductNavTab(index) {
      this.listActive = index;
      if (index === 0) {
        this.showCurrent('home');
      } else {
        this.showCurrent('user');
      }
    },
    linkUrl(e) {
      this.menuList[this.itemIndex].url = e;
    },
  },
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  display: none;
}
.flex {
  display: flex;
  flex-wrap: nowrap;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.align-center {
  align-items: center;
}
.iframe-box {
  width: 375px;
  height: 700px;
  border-radius: 4px;
  box-shadow: 0 0 7px #cccccc;
  margin-right: 50px;
}
.leftModel {
  width: 375px;
  height: 658px;
  border-radius: 4px;
  border: 1px solid #cccccc;
  margin: 30px 50px 0 0;
  background: #f5f5f5;
  margin-bottom: 27px;
  position: relative;
  overflow: auto;
}
.current_home {
  width: 100%;
  height: 647px;
  overflow-y: scroll;
}
.model_header {
  width: 375px;
  height: 54px;
  background: #e93323;
  padding: 13px 14px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.model_header img {
  width: 58px;
  height: 20px;
  display: block;
  margin-right: 11px;
}
.model_header .header_search {
  width: 375px;
  height: 28px;
  line-height: 28px;
  padding: 0 0 0 14px;
  background: #f7f7f7;
  border: 1px solid #f1f1f1;
  border-radius: 14px;
  color: #bbb;
  font-size: 18px;
}
.model_banner {
  width: 375px;
  height: 139px;
  padding: 0 15px;
  box-sizing: border-box;
  background: linear-gradient(180deg, #e93323 0%, #f5f5f5 100%);
}
.model_news {
  margin: 12px auto 12px;
  padding: 0 6px 0;
  width: 345px;
  height: 36px;
  box-sizing: border-box;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #fff;
  line-height: 34px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.model_nav {
  width: 345px;
  // height: 158px;
  box-sizing: border-box;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #fff;
  margin: 0 auto 12px;
  display: flex;
  flex-wrap: wrap;
}
.model_nav_item {
  width: 20%;
  height: 59px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
}
.model_nav_item img {
  width: 40px;
  height: 40px;
}
.model_nav_item p {
  font-size: 12px;
  color: #454545;
  padding-top: 3px;
}
.tab_nav_bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  white-space: nowrap;
  margin: 15px 0 15px;
  padding: 5px 15px 5px;
  border: 1px solid #f5f5f5;
  cursor: pointer;
}
.nav_bd_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.item_txt {
  color: #282828;
}
.item_label {
  width: 62px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2px;
  font-size: 12px;
  border-radius: 8px;
  color: #999;
}
.label_active {
  .item_txt {
    color: #e93323 !important;
  }
  .item_label {
    color: #fff;
    background: linear-gradient(90deg, #ff7931 0%, #e93323 100%);
  }
}
.cate_box_style {
  margin-top: 30px;
  width: 100%;
  font-size: 14px;
}
.shop_cart {
  opacity: 0.6;
}
.moni_goods {
  width: 100%;
  font-size: 20px;
  img {
    display: block;
    width: 100%;
  }
}
.user_head {
  width: 375px;
  height: 262px;
  background: linear-gradient(180deg, #e93323 0%, #f5f5f5 100%);
  cursor: not-allowed;
}
.user_card {
  position: relative;
  width: 100%;
  margin: 0 auto;
  padding: 17px 0 14px 0;
}
.user_info {
  display: flex;
}
.user_info img {
  width: 59px;
  height: 59px;
  border-radius: 50%;
}
.user_info .info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 9px;
  padding: 7px 0;
}
.nick_name {
  color: #fff;
  font-size: 15px;
}
.phone {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}
.num_wrapper {
  margin-top: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
}
.num_wrap_item {
  width: 25%;
  text-align: center;
}
.num_item_bold {
  font-size: 20px;
  font-weight: bold;
}
.num_title {
  margin-top: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}
.order_wrap {
  background-color: #fff;
  border-radius: 6px;
  padding: 14px 7px;
  width: 345px;
  height: 112px;
  margin: auto;
}
.order_wrap_tit {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #282828;
  margin-bottom: 19px;
  padding: 0 7px;
}
.weight_600 {
  font-weight: 600;
}
.font_sm {
  font-size: 12px;
}
.order_wrap_list {
  display: flex;
  justify-content: space-between;
}
.order_list_item {
  width: 20%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.order_list_item img {
  width: 24px;
  display: block;
  margin-bottom: 7px;
}
.order_list_item p {
  font-size: 13px;
  color: gray；;
}
.slider_img {
  margin: 10px auto;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #f5f5f5;
  width: 345px;
  height: 69px;
}
.user_mens {
  margin: 0 auto;
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 6px;
  width: 345px;
}
.menu_title {
  padding: 15px 15px 20px;
  font-size: 15px;
  color: #282828;
  font-weight: 600;
}
.list_box {
  display: flex;
  flex-wrap: wrap;
}
.list_box_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  width: 25%;
  margin-bottom: 23px;
  font-size: 13px;
  color: #333;
}
.list_box_item img {
  width: 26px;
  height: 26px;
  display: block;
  margin-bottom: 9px;
}
.user_bg {
  width: 100%;
  height: 208px;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0 15px 0;
}

.move-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 80px;
  cursor: move;
  color: #d8d8d8;
  .icondrag2 {
    font-size: 25px;
    cursor: move;
  }
}
.select_ctive {
  border: 1px dashed #666;
  box-sizing: border-box;
}
.upLoad {
  width: 58px;
  height: 58px;
  line-height: 58px;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.02);
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex_between {
  flex: 1;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.tip {
  width: 100%;
  text-align: center;
  font-size: 24px;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.right-box {
  width: 70%;
  min-width: 400px;
  border-radius: 4px;
  height: 700px;
  overflow-y: scroll;
  padding: 0 10px;
  margin-top: 30px;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #535353;
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px #fff;
    border-radius: 4px;
    background: #fff;
  }
}

.title-bar {
  width: 100%;
  height: 38px;
  line-height: 38px;
  padding-left: 10px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 2px;
    height: 16px;
    background: #1890ff;
  }
}

.mobile-config {
  padding: 0 15px 20px 0px;
  .item {
    position: relative;
    display: flex;
    margin-top: 20px;
    border: 1px dashed #ddd;
    padding: 15px 15px 0 0;
  }
  .picBox {
    cursor: pointer;
    position: relative;
  }
  .delect-btn {
    position: absolute;
    right: -12px;
    top: -16px;
    color: #999999;
    z-index: 11;
    .el-icon-circle-close {
      font-size: 21px;
      color: #999;
      cursor: pointer;
    }
  }
  .img-box {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    img {
      width: 100%;
    }
  }
  .info {
    flex: 1;
    margin-left: 12px;
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    span {
      width: 40px;
      font-size: 13px;
    }
    .input-box {
      flex: 1;
    }
  }
}
.cur_pointer {
  cursor: pointer;
}
.link-item {
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  .title {
    font-size: 14px;
    color: #2d8cf0;
  }
  .txt {
    margin: 5px 0;
    font-size: 12px;
    span {
      color: #333;
    }
    p {
      display: inline-block;
      color: #19be6b;
      margin-right: 10px;
      span {
        color: #333;
      }
      &.red {
        color: #f00;
      }
    }
  }
  .tips {
    font-size: 12px;
    color: #999;
    .copy {
      padding: 3px 5px;
      border: 1px solid #cccccc;
      border-radius: 5px;
      color: #333;
      cursor: pointer;
      margin-left: 5px;
      &:hover {
        border-color: #2d8cf0;
        color: #2d8cf0;
      }
    }
  }
}
.on {
  border: 1px solid #1db0fc;
}
.image {
  width: 100%;
  display: block;
}
// .card_cate{
//   border:2px solid #e6ebf5;
// }
.card_bt {
  padding: 14px;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  span {
    font-size: 18px;
    font-weight: 600;
  }
}
.tab_view {
  width: 200px;
  border-right: 1px solid #eee;
  margin-right: 40px;
  display: flex;
  flex-direction: column;
  .cell_item {
    height: 50px;
    font-size: 14px;
    line-height: 50px;
    text-align: left;
    padding-left: 30px;
    cursor: pointer;
  }
}
.tab_active {
  background: #f6fbff;
  color: #1890ff;
  border-right: 1px solid #1890ff;
}
.footer_btn {
  border-top: 1px solid #eee;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
