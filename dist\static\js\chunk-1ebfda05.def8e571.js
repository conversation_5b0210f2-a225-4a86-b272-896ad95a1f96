(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1ebfda05"],{"0f53":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入角色昵称",clearable:""},model:{value:e.listPram.roleName,callback:function(t){e.$set(e.listPram,"roleName",t)},expression:"listPram.roleName"}})],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary"},nativeOn:{click:function(t){return e.handleGetRoleList(t)}}},[e._v("查询")]),e._v(" "),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),e._v(" "),i("el-form",{attrs:{inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:admin:role:save"],expression:"['platform:admin:role:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handlerOpenEdit(0)}}},[e._v("添加角色")])],1)],1),e._v(" "),i("el-table",{attrs:{data:e.listData.list,size:"mini","header-cell-style":{fontWeight:"bold",background:"#f8f8f9",color:"#515a6e",height:"40px"}}},[i("el-table-column",{attrs:{label:"角色编号",prop:"id",width:"120"}}),e._v(" "),i("el-table-column",{attrs:{label:"角色昵称",prop:"roleName","min-width":"130"}}),e._v(" "),i("el-table-column",{attrs:{label:"创建时间",prop:"createTime","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:"更新时间",prop:"updateTime","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:"状态",prop:"status",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["platform:admin:role:update:status"])?[i("el-switch",{staticStyle:{width:"40px"},attrs:{"active-value":!0,"inactive-value":!1},on:{change:function(i){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(i){e.$set(t.row,"status",i)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:admin:role:update"],expression:"['platform:admin:role:update']"}],attrs:{size:"small",type:"text"},on:{click:function(i){return e.handlerOpenEdit(1,t.row)}}},[e._v("编辑")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:admin:role:delete"],expression:"['platform:admin:role:delete']"}],attrs:{size:"small",type:"text"},on:{click:function(i){return e.handlerOpenDel(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),i("el-pagination",{attrs:{"current-page":e.listPram.page,"page-sizes":e.constants.page.limit,layout:e.constants.page.layout,total:e.listData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),i("el-dialog",{attrs:{visible:e.editDialogConfig.visible,title:0===e.editDialogConfig.isCreate?"创建身份":"编辑身份","destroy-on-close":"","close-on-click-modal":!1,width:"500px"},on:{"update:visible":function(t){return e.$set(e.editDialogConfig,"visible",t)}}},[e.editDialogConfig.visible?i("edit",{ref:"editForm",attrs:{"is-create":e.editDialogConfig.isCreate,"edit-data":e.editDialogConfig.editData},on:{hideEditDialog:e.hideEditDialog}}):e._e()],1)],1)},n=[],l=i("cc5e"),r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{ref:"pram",attrs:{model:e.pram,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",{attrs:{label:"角色名称",prop:"roleName",rules:[{required:!0,message:"请填写角色名称",trigger:["blur","change"]}]}},[i("el-input",{attrs:{placeholder:"身份名称"},model:{value:e.pram.roleName,callback:function(t){e.$set(e.pram,"roleName",t)},expression:"pram.roleName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"状态"}},[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:e.pram.status,callback:function(t){e.$set(e.pram,"status",t)},expression:"pram.status"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"菜单权限"}},[i("el-checkbox",{on:{change:function(t){return e.handleCheckedTreeExpand(t,"menu")}},model:{value:e.menuExpand,callback:function(t){e.menuExpand=t},expression:"menuExpand"}},[e._v("展开/折叠")]),e._v(" "),i("el-checkbox",{on:{change:function(t){return e.handleCheckedTreeConnect(t,"menu")}},model:{value:e.menuCheckStrictly,callback:function(t){e.menuCheckStrictly=t},expression:"menuCheckStrictly"}},[e._v("父子联动")]),e._v(" "),i("el-tree",{ref:"menu",staticClass:"tree-border",attrs:{data:e.menuOptions,"show-checkbox":"","node-key":"id","check-strictly":!e.menuCheckStrictly,"empty-text":"加载中，请稍候",props:e.defaultProps}})],1),e._v(" "),i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:admin:role:update","platform:admin:role:save"],expression:"['platform:admin:role:update', 'platform:admin:role:save']"}],attrs:{type:"primary"},on:{click:function(t){return e.handlerSubmit("pram")}}},[e._v(e._s(0===e.isCreate?"确定":"更新"))]),e._v(" "),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)],1)},s=[],o=i("61f7"),u={name:"roleEdit",props:{isCreate:{type:Number,required:!0},editData:{type:Object,default:null}},data:function(){return{pram:{roleName:null,rules:"",status:null,id:null},menuExpand:!1,menuNodeAll:!1,menuOptions:[],menuCheckStrictly:!0,currentNodeId:[],defaultProps:{children:"childList",label:"name"},menuIds:[]}},mounted:function(){this.initEditData(),this.getCacheMenu()},methods:{close:function(){this.$emit("hideEditDialog")},initEditData:function(){var e=this;if(1===this.isCreate){var t=this.editData,i=t.roleName,a=t.status,n=t.id;this.pram.roleName=i,this.pram.status=a,this.pram.id=n;var r=this.$loading({lock:!0,text:"Loading"});l["c"](n).then((function(t){e.menuOptions=t.menuList,e.checkDisabled(e.menuOptions),r.close(),e.getTreeId(t.menuList),e.$nextTick((function(){e.menuIds.forEach((function(t,i){var a=e.$refs.menu.getNode(t);a.isLeaf&&e.$refs.menu.setChecked(a,!0)}))}))}))}},handlerSubmit:Object(o["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(e){var i=t.getMenuAllCheckedKeys().toString();t.pram.rules=i,0===t.isCreate?t.handlerSave():t.handlerEdit()}}))})),handlerSave:function(){var e=this;l["a"](this.pram).then((function(t){e.$message.success("创建身份成功"),e.$emit("hideEditDialog")}))},handlerEdit:function(){var e=this;l["f"](this.pram).then((function(t){e.$message.success("更新身份成功"),e.$emit("hideEditDialog")}))},rulesSelect:function(e){this.pram.rules=e},handleCheckedTreeExpand:function(e,t){if("menu"==t)for(var i=this.menuOptions,a=0;a<i.length;a++)this.$refs.menu.store.nodesMap[i[a].id].expanded=e},handleCheckedTreeNodeAll:function(e,t){"menu"==t&&this.$refs.menu.setCheckedNodes(e?this.menuOptions:[])},handleCheckedTreeConnect:function(e,t){"menu"==t&&(this.menuCheckStrictly=!!e)},getMenuAllCheckedKeys:function(){var e=this.$refs.menu.getCheckedKeys(),t=this.$refs.menu.getHalfCheckedKeys();return e.unshift.apply(e,t),e},getCacheMenu:function(){var e=this;if(0===this.isCreate){var t=this.$loading({lock:!0,text:"Loading"});l["e"]().then((function(i){e.menuOptions=i,e.checkDisabled(e.menuOptions),t.close()}))}},getTreeId:function(e){for(var t in e)e[t].checked&&this.menuIds.push(e[t].id),e[t].childList&&this.getTreeId(e[t].childList)},checkDisabled:function(e){var t=this;e.forEach((function(e){9!==e.id&&8!==e.id&&10!==e.id||(e.disabled=!0,e.childList.forEach((function(e){e.disabled=!0,t.$nextTick((function(){var i=t.$refs.menu.getNode(e.id);i.isLeaf&&t.$refs.menu.setChecked(i,!0)}))})))}))}}},c=u,d=i("2877"),m=Object(d["a"])(c,r,s,!1,null,"6a66e720",null),h=m.exports,f=i("e350"),p={components:{edit:h},data:function(){return{constants:this.$constants,listData:{list:[]},listPram:{createTime:null,updateTime:null,level:null,page:1,limit:this.$constants.page.limit[0],roleName:null,rules:null,status:null},menuList:[],editDialogConfig:{visible:!1,isCreate:0,editData:{}}}},mounted:function(){this.handleGetRoleList()},methods:{checkPermi:f["a"],handlerOpenDel:function(e){var t=this;this.$modalSure("确认删除当前数据").then((function(){l["b"](e.id).then((function(e){t.$message.success("删除数据成功"),t.handleGetRoleList()}))})).catch((function(){}))},handleGetRoleList:function(){var e=this;l["d"](this.listPram).then((function(t){e.listData=t}))},handlerOpenEdit:function(e,t){this.editDialogConfig.editData=1===e?t:{},this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},hideEditDialog:function(){this.editDialogConfig.visible=!1,this.handleGetRoleList()},handleSizeChange:function(e){this.listPram.limit=e,this.handleGetRoleList(this.listPram)},handleCurrentChange:function(e){this.listPram.page=e,this.handleGetRoleList(this.listPram)},handleStatusChange:function(e){var t=this;l["g"](e).then((function(e){t.$message.success("更新状态成功"),t.handleGetRoleList()}))},resetQuery:function(){this.listPram.roleName="",this.handleGetRoleList()}}},v=p,g=Object(d["a"])(v,a,n,!1,null,"2c4e1f30",null);t["default"]=g.exports},cc5e:function(e,t,i){"use strict";i.d(t,"a",(function(){return n})),i.d(t,"b",(function(){return l})),i.d(t,"c",(function(){return r})),i.d(t,"d",(function(){return s})),i.d(t,"f",(function(){return o})),i.d(t,"g",(function(){return u})),i.d(t,"e",(function(){return c}));var a=i("b775");function n(e){var t={level:e.level,roleName:e.roleName,status:e.status,rules:e.rules};return Object(a["a"])({url:"/admin/platform/role/save",method:"POST",data:t})}function l(e){return Object(a["a"])({url:"admin/platform/role/delete/".concat(e),method:"post"})}function r(e){return Object(a["a"])({url:"/admin/platform/role/info/".concat(e),method:"GET"})}function s(e){var t={createTime:e.createTime,updateTime:e.updateTime,level:e.level,page:e.page,limit:e.limit,roleName:e.roleName,rules:e.rules,status:e.status};return Object(a["a"])({url:"/admin/platform/role/list",method:"get",params:t})}function o(e){var t={id:e.id,roleName:e.roleName,rules:e.rules,status:e.status};return Object(a["a"])({url:"/admin/platform/role/update",method:"post",params:{id:e.id},data:t})}function u(e){return Object(a["a"])({url:"/admin/platform/role/updateStatus",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/admin/platform/menu/cache/tree",method:"get"})}}}]);