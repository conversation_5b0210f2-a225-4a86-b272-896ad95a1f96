(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-da4c0064"],{"641f":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"divBox"},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(a){t.activeName=a},expression:"activeName"}},[e("el-tab-pane",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:daily:statement:page:list"],expression:"['platform:finance:daily:statement:page:list']"}],attrs:{label:"日账单",name:"day"}}),t._v(" "),e("el-tab-pane",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:month:statement:page:list"],expression:"['platform:finance:month:statement:page:list']"}],attrs:{label:"月账单",name:"month"}})],1),t._v(" "),"day"===t.activeName?e("div",[e("el-date-picker",{staticClass:"selWidth",attrs:{align:"right","unlink-panels":"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",type:"daterange",placement:"bottom-end",placeholder:"自定义时间","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(a){t.timeVal=a},expression:"timeVal"}})],1):e("div",[e("el-date-picker",{attrs:{type:"monthrange",align:"right","unlink-panels":"","value-format":"yyyy-MM",format:"yyyy-MM","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份","picker-options":t.pickerOptionsYear},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(a){t.timeVal=a},expression:"timeVal"}})],1)],1),t._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"small","highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"90"}}),t._v(" "),e("el-table-column",{attrs:{prop:"dataDate",label:"day"===t.activeName?"日期":"月份","min-width":"150"}}),t._v(" "),e("el-table-column",{attrs:{prop:"orderPayAmount",label:"订单支付总金额","min-width":"120"}}),t._v(" "),e("el-table-column",{attrs:{prop:"handlingFee",label:"手续费","min-width":"150"}}),t._v(" "),e("el-table-column",{attrs:{prop:"rechargeAmount",label:"充值金额","min-width":"100"}}),t._v(" "),e("el-table-column",{attrs:{prop:"refundAmount",label:"退款金额","min-width":"100"}}),t._v(" "),e("el-table-column",{attrs:{prop:"incomeExpenditure",label:"平台收支","min-width":"100"}}),t._v(" "),e("el-table-column",{attrs:{label:"操作","min-width":"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.onDetails(a.row)}}},[t._v("详情")])]}}])})],1),t._v(" "),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),e("el-dialog",{attrs:{title:"day"===t.activeName?"日账单详情":"月账单详情",visible:t.dialogVisible,width:"1000px","before-close":t.handleClose,center:""},on:{"update:visible":function(a){t.dialogVisible=a}}},[e("el-row",{staticClass:"ivu-mt mt20",attrs:{align:"middle"}},[e("el-col",{attrs:{span:3}},[e("el-menu",{staticClass:"el-menu-vertical-demo",attrs:{"default-active":"0"}},[e("el-menu-item",{attrs:{name:t.accountDetails.dataDate}},[e("span",[t._v(t._s(t.accountDetails.dataDate))])])],1)],1),t._v(" "),e("el-col",{attrs:{span:21}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"grid-content"},[e("span",{staticClass:"title"},[t._v("订单收入总金额")]),t._v(" "),e("span",{staticClass:"color_gray"},[t._v("￥"+t._s(t.accountDetails.orderPayAmount))]),t._v(" "),e("span",{staticClass:"count font-red"},[t._v(t._s(t.accountDetails.totalOrderNum)+"笔")]),t._v(" "),e("div",{staticClass:"list"},[e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:13}},[t._v("平台手续费")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:11}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.handlingFee))])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:13}},[t._v("退款积分抵扣金额")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:11}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.refundReplaceIntegralPrice))])])],1)],1)]),t._v(" "),e("el-divider",{attrs:{direction:"vertical"}})],1),t._v(" "),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"grid-content"},[e("span",{staticClass:"title"},[t._v("充值金额")]),t._v(" "),e("span",{staticClass:"color_gray"},[t._v("￥"+t._s(t.accountDetails.rechargeAmount))]),t._v(" "),e("span",{staticClass:"count font-red"},[t._v(t._s(t.accountDetails.rechargeNum)+"笔")])]),t._v(" "),e("el-divider",{attrs:{direction:"vertical"}})],1),t._v(" "),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"grid-content"},[e("span",{staticClass:"title"},[t._v("支出总金额")]),t._v(" "),e("span",{staticClass:"color_gray"},[t._v("￥"+t._s(t.accountDetails.payoutAmount))]),t._v(" "),e("span",{staticClass:"count"},[t._v(t._s(t.accountDetails.payoutNum)+"笔")]),t._v(" "),e("div",{staticClass:"list"},[e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:13}},[t._v("支付积分抵扣金额")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:11}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.integralPrice))])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("商户分账金额")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.merchantTransferAmount))])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("一级佣金")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.firstBrokerage))])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("二级佣金")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.secondBrokerage))])])],1),t._v(" "),e("el-row",{staticClass:"item mb35"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("商户分账笔数")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_count"},[t._v(t._s(t.accountDetails.merchantTransferNum)+"笔")])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("平台退款金额")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.refundAmount))])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("平台代扣佣金")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_num"},[t._v("￥"+t._s(t.accountDetails.refundReplaceBrokerage))])])],1),t._v(" "),e("el-row",{staticClass:"item"},[e("el-col",{staticClass:"name",attrs:{span:12}},[t._v("退款笔数")]),t._v(" "),e("el-col",{staticClass:"cost",attrs:{span:12}},[e("span",{staticClass:"cost_count"},[t._v(t._s(t.accountDetails.refundNum)+"笔")])])],1)],1)]),t._v(" "),e("el-divider",{attrs:{direction:"vertical"}})],1),t._v(" "),e("el-col",{attrs:{span:5}},[e("div",{staticClass:"grid-content"},[e("span",{staticClass:"title"},[t._v(t._s("day"===t.activeName?"当日收支":"当月收支"))]),t._v(" "),e("span",{staticClass:"color_red"},[t._v("￥"+t._s(t.accountDetails.incomeExpenditure))])])])],1)],1),t._v(" "),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(a){t.dialogVisible=!1}}},[t._v("我知道了")])],1)],1)],1)},n=[],i=e("cd05"),l={shortcuts:[{text:"本月",onClick:function(t){t.$emit("pick",[new Date,new Date])}},{text:"今年至今",onClick:function(t){var a=new Date,e=new Date((new Date).getFullYear(),0);t.$emit("pick",[e,a])}},{text:"最近六个月",onClick:function(t){var a=new Date,e=new Date;e.setMonth(e.getMonth()-6),t.$emit("pick",[e,a])}}]},c={name:"statement",data:function(){return{timeVal:[],activeName:"day",listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,dateLimit:""},dialogVisible:!1,accountDetails:{},pickerOptions:this.$timeOptions,pickerOptionsYear:l}},mounted:function(){this.getList(1)},methods:{onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.getList(1)},handleClick:function(){this.tableFrom.dateLimit="",this.timeVal=[],this.getList(1)},onDetails:function(t){this.dialogVisible=!0,this.accountDetails=t},seachList:function(){this.handleClose(),this.getList(1)},getList:function(t){var a=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,"day"===this.activeName?Object(i["f"])(this.tableFrom).then((function(t){a.tableData.data=t.list,a.tableData.total=t.total,a.listLoading=!1})).catch((function(){a.listLoading=!1})):Object(i["j"])(this.tableFrom).then((function(t){a.tableData.data=t.list,a.tableData.total=t.total,a.listLoading=!1})).catch((function(){a.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList(1)},handleClose:function(){this.dialogVisible=!1},handleDelete:function(t,a){var e=this;this.$modalSure().then((function(){storeApi.brandDeleteApi(t).then((function(t){e.$message.success("删除成功"),e.$store.commit("merchant/SET_MerchantClassify",[]),e.getList(1)}))})).catch((function(){}))},onchangeIsShow:function(t){var a=this;activityApi.activitySwitchApi(t.id).then((function(t){a.$message.success("操作成功"),a.getList()}))}}},o=c,r=(e("6641"),e("2877")),m=Object(r["a"])(o,s,n,!1,null,"5703af43",null);a["default"]=m.exports},6641:function(t,a,e){"use strict";e("f90e")},cd05:function(t,a,e){"use strict";e.d(a,"i",(function(){return n})),e.d(a,"h",(function(){return i})),e.d(a,"g",(function(){return l})),e.d(a,"k",(function(){return c})),e.d(a,"c",(function(){return o})),e.d(a,"d",(function(){return r})),e.d(a,"e",(function(){return m})),e.d(a,"p",(function(){return u})),e.d(a,"m",(function(){return d})),e.d(a,"n",(function(){return p})),e.d(a,"o",(function(){return f})),e.d(a,"f",(function(){return v})),e.d(a,"j",(function(){return h})),e.d(a,"b",(function(){return _})),e.d(a,"a",(function(){return g}));var s=e("b775");function n(t){return Object(s["a"])({url:"/admin/platform/finance/merchant/closing/list",method:"get",params:t})}function i(t){return Object(s["a"])({url:"/admin/platform/finance/merchant/closing/remark",method:"post",data:t})}function l(t){return Object(s["a"])({url:"admin/platform/finance/merchant/closing/detail/".concat(t),method:"get"})}function c(t){return Object(s["a"])({url:"admin/platform/finance/merchant/closing/proof",method:"POST",data:t})}function o(t){return Object(s["a"])({url:"admin/platform/finance/merchant/closing/audit",method:"POST",data:t})}function r(t){return Object(s["a"])({url:"admin/platform/finance/merchant/closing/config",method:"get"})}function m(t){return Object(s["a"])({url:"admin/platform/finance/merchant/closing/config/edit",method:"post",data:t})}function u(t){return Object(s["a"])({url:"/admin/platform/finance/user/closing/remark",method:"post",data:t})}function d(t){return Object(s["a"])({url:"/admin/platform/finance/user/closing/audit",method:"POST",data:t})}function p(t){return Object(s["a"])({url:"/admin/platform/finance/user/closing/list",method:"get",params:t})}function f(t){return Object(s["a"])({url:"/admin/platform/finance/user/closing/proof",method:"POST",data:t})}function v(t){return Object(s["a"])({url:"admin/platform/finance/daily/statement/list",method:"get",params:t})}function h(t){return Object(s["a"])({url:"admin/platform/finance/month/statement/list",method:"get",params:t})}function _(t){return Object(s["a"])({url:"admin/platform/finance/funds/flow",method:"get",params:t})}function g(t){return s["a"].get("financial_record/export",t)}},f90e:function(t,a,e){}}]);