(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-86a39cb2"],{"4b47":function(t,e,r){},6606:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUAAAAAyCAIAAACib5WDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjE1NEJCMUE0NzZGNDExRTVBOTBBQTZFOEFEMjc4NTkzIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjE1NEJCMUE1NzZGNDExRTVBOTBBQTZFOEFEMjc4NTkzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MTU0QkIxQTI3NkY0MTFFNUE5MEFBNkU4QUQyNzg1OTMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MTU0QkIxQTM3NkY0MTFFNUE5MEFBNkU4QUQyNzg1OTMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4yWLBJAAABuklEQVR42uzcu0ocURzA4XWxMIWiQhJwtVhxMW0wEkWj+AwWgm9gJfgggpVPoElEUwUCKRNFJaQWsygWXvAKXlBZGw8KIiIJmWFnGPg+pjiryMIffpxzRLemUqnkUlUul0ulUg74f3kjAAEDAgYEDAIGBAwIGBAwCBgQMCBgEHAMlZub8BglJK825s/vHxzOfl4Ii9GR4devXhooZGYHPjo+mfk0f3l5FZ6wCC8NFDKzA+fz+aHB/scvDRQyE3BzU2N4DBEyeYQGBAxU5wi9sbm1+ut3W2shznucnp296Sx1tBeNGxINeG39z+jIcPy3+Tj3RcCQ9BG6ob7+fjE5NR2eaOugtdBi1pD0Dvzg6vo68hpIOeAXdXWR10CV1Pz9c6F/LC4P9PfGf5ufSysf+nqe/ZbPhYZq3YGfiHD7BdI/Qrv9QuYDdvsFd2B3YEjjDgxk+Aidu/sd1T9vueEUPTE+ZrhgBwai7sA7u3tPvhJtaz0/vzBrSDrg7ndvv377/vAX0dFs7+y+7+4ya0g64I72ov8iAndgQMCAgEHAgIABAYOAAQEDAgYEDAIGBAwIGBAwCBhIy60AAwBiy5esmSYLKgAAAABJRU5ErkJggg=="},a643:function(t,e,r){"use strict";r("4b47")},df42:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("el-row",{attrs:{gutter:30}},[i("el-col",t._b({staticClass:"left mb15 ml40"},"el-col",t.grid,!1),[i("div",[i("img",{staticClass:"top",attrs:{src:r("f0da")}}),t._v(" "),i("img",{staticClass:"bottom",attrs:{src:r("6606")}}),t._v(" "),i("div",{staticStyle:{background:"#f4f5f9","min-height":"438px",position:"absolute",top:"63px",width:"320px"}}),t._v(" "),i("div",{staticClass:"textbot"},[t._l(t.list,(function(e,r){return i("div",{key:r,staticClass:"li",class:{active:e===t.formValidate}},[i("div",[i("div",{staticClass:"add",on:{click:function(i){return t.add(e,r)}}},[i("i",{staticClass:"el-icon-plus"}),t._v(" "),i("div",{staticClass:"arrow"})]),t._v(" "),i("div",{staticClass:"tianjia"},t._l(e.sub_button,(function(e,a){return i("div",{key:a,staticClass:"addadd menuBox",class:{active:e===t.formValidate},on:{click:function(i){return t.gettem(e,a,r)}}},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.name,placement:"top-start"}},[i("el-button",[t._v(t._s(e.name||"二级菜单"))])],1)],1)})),0)]),t._v(" "),i("div",{staticClass:"text menuBox",on:{click:function(i){return t.gettem(e,r,null)}}},[i("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.name,placement:"top-start"}},[i("el-button",[t._v(t._s(e.name||"一级菜单"))])],1)],1)])})),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.list.length<3,expression:"list.length < 3"}],staticClass:"li"},[i("div",{staticClass:"text",on:{click:t.addtext}},[i("i",{staticClass:"el-icon-plus"})])])],2)])]),t._v(" "),i("el-col",{attrs:{xl:11,lg:12,md:22,sm:22,xs:22}},[null!==t.checkedMenuId?i("div",[i("div",{staticClass:"dividerTitle acea-row row-between row-bottom"},[i("span",{staticClass:"title"},[t._v("菜单信息")]),t._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:wechat:public:customize:menu:delete"],expression:"['platform:wechat:public:customize:menu:delete']"}],attrs:{slot:"extra",size:"small",type:"danger"},on:{click:t.deltMenus},slot:"extra"},[t._v("删除")]),t._v(" "),i("el-divider")],1),t._v(" "),i("el-col",{staticClass:"userAlert",attrs:{span:24}},[i("div",{staticClass:"box-card right"},[i("el-alert",{staticClass:"mb15",attrs:{title:"已添加子菜单，仅可设置菜单名称",type:"success","show-icon":""}}),t._v(" "),i("el-form",{ref:"formValidate",staticClass:"mt20",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"菜单名称",prop:"name"}},[i("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写菜单名称"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"规则状态",prop:"type"}},[i("el-select",{staticClass:"spwidth",attrs:{placeholder:"请选择规则状态"},model:{value:t.formValidate.type,callback:function(e){t.$set(t.formValidate,"type",e)},expression:"formValidate.type"}},[i("el-option",{attrs:{value:"click",label:"关键字"}},[t._v("关键字")]),t._v(" "),i("el-option",{attrs:{value:"view",label:"跳转网页"}},[t._v("跳转网页")]),t._v(" "),i("el-option",{attrs:{value:"miniprogram",label:"小程序"}},[t._v("小程序")])],1)],1),t._v(" "),"click"===t.formValidate.type?i("div",[i("el-form-item",{attrs:{label:"关键字",prop:"key"}},[i("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写关键字"},model:{value:t.formValidate.key,callback:function(e){t.$set(t.formValidate,"key",e)},expression:"formValidate.key"}})],1)],1):t._e(),t._v(" "),"miniprogram"===t.formValidate.type?i("div",[i("el-form-item",{attrs:{label:"appid",prop:"appid"}},[i("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写appid"},model:{value:t.formValidate.appid,callback:function(e){t.$set(t.formValidate,"appid",e)},expression:"formValidate.appid"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"备用网页",prop:"url"}},[i("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写备用网页"},model:{value:t.formValidate.url,callback:function(e){t.$set(t.formValidate,"url",e)},expression:"formValidate.url"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"小程序路径",prop:"pagepath"}},[i("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写小程序路径"},model:{value:t.formValidate.pagepath,callback:function(e){t.$set(t.formValidate,"pagepath",e)},expression:"formValidate.pagepath"}})],1)],1):t._e(),t._v(" "),"view"===t.formValidate.type?i("div",[i("el-form-item",{attrs:{label:"跳转地址",prop:"url"}},[i("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写跳转地址"},model:{value:t.formValidate.url,callback:function(e){t.$set(t.formValidate,"url",e)},expression:"formValidate.url"}})],1)],1):t._e()],1)],1)])],1):t._e(),t._v(" "),t.isTrue?i("el-col",{attrs:{span:24}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:wechat:public:customize:menu:save"],expression:"['platform:wechat:public:customize:menu:save']"}],staticStyle:{display:"block",margin:"10px auto"},attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submenus("formValidate")}}},[t._v("保存并发布")])],1):t._e()],1)],1)],1)],1)},a=[],n=r("ffd2"),o=r("61f7");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},n=a.iterator||"@@iterator",o=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(B){u=function(t,e,r){return t[e]=r}}function d(t,e,r,a){var n=e&&e.prototype instanceof p?e:p,o=Object.create(n.prototype),s=new M(a||[]);return i(o,"_invoke",{value:A(t,r,s)}),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(B){return{type:"throw",arg:B}}}t.wrap=d;var f={};function p(){}function m(){}function v(){}var g={};u(g,n,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(C([])));b&&b!==e&&r.call(b,n)&&(g=b);var w=v.prototype=p.prototype=Object.create(g);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function a(i,n,o,l){var c=h(t[i],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==s(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,o,l)}),(function(t){a("throw",t,o,l)})):e.resolve(d).then((function(t){u.value=t,o(u)}),(function(t){return a("throw",t,o,l)}))}l(c.arg)}var n;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){a(t,r,e,i)}))}return n=n?n.then(i,i):i()}})}function A(t,e,r){var i="suspendedStart";return function(a,n){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===a)throw n;return _()}for(r.method=a,r.arg=n;;){var o=r.delegate;if(o){var s=I(o,r);if(s){if(s===f)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===i)throw i="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i="executing";var l=h(t,e,r);if("normal"===l.type){if(i=r.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i="completed",r.method="throw",r.arg=l.arg)}}}function I(t,e){var r=e.method,i=t.iterator[r];if(void 0===i)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,I(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var a=h(i,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,f;var n=a.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function V(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(V,this),this.reset(!0)}function C(t){if(t){var e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function e(){for(;++i<t.length;)if(r.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=void 0,e.done=!0,e};return a.next=a}}return{next:_}}function _(){return{value:void 0,done:!0}}return m.prototype=v,i(w,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:m,configurable:!0}),m.displayName=u(v,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,c,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},k(x.prototype),u(x.prototype,o,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,i,a,n){void 0===n&&(n=Promise);var o=new x(d(e,r,i,a),n);return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(w),u(w,c,"Generator"),u(w,n,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},t.values=C,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(r,i){return o.type="throw",o.arg=t,e.next=r,i&&(e.method="next",e.arg=void 0),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a],o=n.completion;if("root"===n.tryLoc)return i("end");if(n.tryLoc<=this.prev){var s=r.call(n,"catchLoc"),l=r.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return i(n.catchLoc,!0);if(this.prev<n.finallyLoc)return i(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return i(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return i(n.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,f):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var a=i.arg;E(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:C(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function c(t,e,r,i,a,n,o){try{var s=t[n](o),l=s.value}catch(c){return void r(c)}s.done?e(l):Promise.resolve(l).then(i,a)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(i,a){var n=t.apply(e,r);function o(t){c(n,i,a,o,s,"next",t)}function s(t){c(n,i,a,o,s,"throw",t)}o(void 0)}))}}var d={name:"WechatMenus",data:function(){return{grid:{xl:8,lg:8,md:8,sm:8,xs:24},grid2:{xl:16,lg:16,md:16,sm:16,xs:24},modal2:!1,formValidate:{name:"",type:"click",appid:"",url:"",key:"",pagepath:"",id:0},ruleValidate:{name:[{required:!0,message:"请填写菜单名称",trigger:"blur"}],key:[{required:!0,message:"请填写关键字",trigger:"blur"}],appid:[{required:!0,message:"请填写appid",trigger:"blur"}],pagepath:[{required:!0,message:"请填写小程序路径",trigger:"blur"}],url:[{required:!0,message:"请填写跳转地址",trigger:"blur"}],type:[{required:!0,message:"请选择规则状态",trigger:"change"}]},parentMenuId:null,list:[],checkedMenuId:null,isTrue:!1}},mounted:function(){if(this.getMenus(),!this.list.length)return this.formValidate;this.formValidate=this.list[this.activeClass]},methods:{defaultMenusData:function(){return{type:"click",name:"",sub_button:[]}},defaultChildData:function(){return{type:"click",name:""}},getMenus:function(){var t=this;Object(n["i"])().then(function(){var e=u(l().mark((function e(r){var i;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=r.menu,t.list=i.button;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},submenus:Object(o["a"])((function(t){var e=this;this.isTrue&&!this.checkedMenuId&&0!==this.checkedMenuId?this.putData():this.$refs[t].validate((function(t){if(t)e.putData();else if(!e.check())return!1}))})),putData:function(){var t=this,e={button:this.list};Object(n["h"])(e).then(function(){var e=u(l().mark((function e(r){return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$message.success("提交成功"),t.checkedMenuId=null,t.formValidate={},t.isTrue=!1;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},gettem:function(t,e,r){this.checkedMenuId=e,this.formValidate=t,this.parentMenuId=r,this.isTrue=!0},add:function(t,e){if(!this.check())return!1;if(t.sub_button.length<5){var r=this.defaultChildData(),i=t.sub_button.length;t.sub_button.push(r),this.formValidate=r,this.checkedMenuId=i,this.parentMenuId=e,this.isTrue=!0}},addtext:function(){if(!this.check())return!1;var t=this.defaultMenusData(),e=this.list.length;this.list.push(t),this.formValidate=t,this.checkedMenuId=e,this.parentMenuId=null,this.isTrue=!0},check:function(){var t=/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;return null===this.checkedMenuId||(!this.isTrue||(this.formValidate.name?"click"!==this.formValidate.type||this.formValidate.key?"view"!==this.formValidate.type||t.test(this.formValidate.url)?!!("miniprogram"!==this.formValidate.type||this.formValidate.appid&&this.formValidate.pagepath&&this.formValidate.url)||(this.$message.warning("请填写完整小程序配置!"),!1):(this.$message.warning("请输入正确的跳转地址!"),!1):(this.$message.warning("请输入关键字!"),!1):(this.$message.warning("请输入按钮名称!"),!1)))},deltMenus:function(){var t=this;this.isTrue?this.$modalSure().then((function(){t.del()})).catch((function(){})):this.$message.warning("请选择菜单!")},del:function(){null===this.parentMenuId?this.list.splice(this.checkedMenuId,1):this.list[this.parentMenuId].sub_button.splice(this.checkedMenuId,1),this.parentMenuId=null,this.formValidate={name:"",type:"click",appid:"",url:"",key:"",pagepath:"",id:0},this.isTrue=!1,this.modal2=!1,this.checkedMenuId=null,this.$refs["formValidate"].resetFields(),this.submenus("formValidate")}}},h=d,f=(r("a643"),r("2877")),p=Object(f["a"])(h,i,a,!1,null,"6bb62910",null);e["default"]=p.exports},f0da:function(t,e,r){t.exports=r.p+"static/img/mobilehead.1c931282.png"}}]);