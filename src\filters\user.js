// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

//会员过滤器

/**
 * 用户注册类型
 */
export function registerTypeFilter(status) {
  const statusMap = {
    wechat: '公众号',
    routine: '小程序',
    h5: 'H5',
    iosWx: '微信ios',
    androidWx: '微信安卓',
    ios: 'ios'
  };
  return statusMap[status];
}


/**
 * 用户类型
 */
export function filterIsPromoter(status) {
  const statusMap = {
    true: '推广员',
    false: '普通用户',
  };
  return statusMap[status];
}

/**
 * 标签
 */
export function tagFilter(status) {
  if (!status) {
    return '-';
  }
  let arr = JSON.parse(localStorage.getItem('tagAllList'));
  let obj = {};
  for (let i in arr) {
    obj[arr[i].id] = arr[i];
  }
  let strArr = status.split(',');
  let newArr = [];
  for (let item of strArr) {
    if (obj[item]) {
      newArr.push(obj[item].name);
    }
  }
  return newArr.join(',');
}
