(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e9da187e"],{"2eb3":function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return l})),a.d(t,"b",(function(){return o})),a.d(t,"e",(function(){return i})),a.d(t,"m",(function(){return s})),a.d(t,"l",(function(){return m})),a.d(t,"i",(function(){return u})),a.d(t,"f",(function(){return c})),a.d(t,"g",(function(){return d})),a.d(t,"h",(function(){return p})),a.d(t,"j",(function(){return f})),a.d(t,"k",(function(){return h})),a.d(t,"a",(function(){return v}));var n=a("b775");function r(e){var t={id:e.id};return Object(n["a"])({url:"/admin/platform/admin/delete",method:"GET",params:t})}function l(e){return Object(n["a"])({url:"/admin/platform/admin/list",method:"GET",params:e})}function o(e){var t={account:e.account,level:e.level,pwd:e.pwd,realName:e.realName,roles:e.roles.join(","),status:e.status,phone:e.phone};return Object(n["a"])({url:"/admin/platform/admin/save",method:"POST",data:t})}function i(e){var t={account:e.account,phone:e.phone,pwd:e.pwd,roles:e.roles,realName:e.realName,status:e.status,id:e.id,isDel:e.isDel};return Object(n["a"])({url:"/admin/platform/admin/update",method:"POST",data:t})}function s(e){return Object(n["a"])({url:"/admin/platform/admin/updateStatus",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/admin/system/admin/update/isSms",method:"get",params:e})}function u(e){var t={menuType:e.menuType,name:e.name};return Object(n["a"])({url:"/admin/platform/menu/list",method:"get",params:t})}function c(e){var t=e;return Object(n["a"])({url:"/admin/platform/menu/add",method:"post",data:t})}function d(e){return Object(n["a"])({url:"/admin/platform/menu/delete/".concat(e),method:"post"})}function p(e){return Object(n["a"])({url:"/admin/platform/menu/info/".concat(e),method:"get"})}function f(e){var t=e;return Object(n["a"])({url:"/admin/platform/menu/update",method:"post",data:t})}function h(e){return Object(n["a"])({url:"/admin/platform/log/sensitive/list",method:"get",params:e})}function v(e){var t={password:e.pwd,realName:e.realName};return Object(n["a"])({url:"/admin/platform/login/admin/update",method:"POST",data:t})}},"54f4":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{ref:"tableheader",staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],attrs:{model:e.queryParams,inline:!0}},[a("el-form-item",{attrs:{label:"菜单名称"}},[a("el-input",{attrs:{placeholder:"请输入菜单名称",clearable:"",size:"small"},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{attrs:{placeholder:"菜单状态",clearable:"",size:"small"},model:{value:e.queryParams.menuType,callback:function(t){e.$set(e.queryParams,"menuType",t)},expression:"queryParams.menuType"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:list"],expression:"['platform:menu:list']"}],attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),e._v(" "),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:add"],expression:"['platform:menu:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-sort",size:"mini"},on:{click:e.toggleExpandAll}},[e._v("展开/折叠")])],1)],1)],1),e._v(" "),e.refreshTable?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"table",attrs:{data:e.menuList,"row-key":"id",height:e.tableHeight,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"name",label:"菜单名称","show-overflow-tooltip":!0,"min-width":"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"icon",label:"图标",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("i",{class:"el-icon-"+e.row.icon,staticStyle:{"font-size":"20px"}})]}}],null,!1,244404100)}),e._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"perms",label:"权限标识","show-overflow-tooltip":!0,"min-width":"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"component",label:"组件路径","show-overflow-tooltip":!0,"min-width":"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"isShow",label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:t.row.isShow?"":"danger"}},[e._v(e._s(t.row.isShow?"显示":"隐藏"))])]}}],null,!1,2038216616)}),e._v(" "),a("el-table-column",{attrs:{prop:"menuType",label:"类型",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return["M"==t.row.menuType?a("span",{staticClass:"type_tag one"},[e._v("目录")]):"C"==t.row.menuType?a("span",{staticClass:"type_tag two"},[e._v("菜单")]):a("span",{staticClass:"type_tag three",attrs:{type:"info"}},[e._v("按钮")])]}}],null,!1,4965977)}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:update"],expression:"['platform:menu:update']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:add"],expression:"['platform:menu:add']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleAdd(t.row)}}},[e._v("新增")]),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:delete"],expression:"['platform:menu:delete']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}],null,!1,2644624230)})],1):e._e(),e._v(" "),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"680px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上级菜单"}},[a("treeselect",{attrs:{options:e.menuOptions,normalizer:e.normalizer,"show-count":!0,placeholder:"选择上级菜单"},model:{value:e.form.pid,callback:function(t){e.$set(e.form,"pid",t)},expression:"form.pid"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"菜单类型",prop:"menuType"}},[a("el-radio-group",{model:{value:e.form.menuType,callback:function(t){e.$set(e.form,"menuType",t)},expression:"form.menuType"}},[a("el-radio",{attrs:{label:"M"}},[e._v("目录")]),e._v(" "),a("el-radio",{attrs:{label:"C"}},[e._v("菜单")]),e._v(" "),a("el-radio",{attrs:{label:"A"}},[e._v("按钮")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},["A"!=e.form.menuType?a("el-form-item",{attrs:{label:"菜单图标"}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"请选择菜单图标"},model:{value:e.form.icon,callback:function(t){e.$set(e.form,"icon",t)},expression:"form.icon"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-circle-plus-outline"},on:{click:e.addIcon},slot:"append"})],1)],1)],1):e._e()],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"菜单名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入菜单名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示排序",prop:"sort"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),e._v(" "),"A"!==e.form.menuType?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{prop:"component"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("el-tooltip",{attrs:{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"}},[a("i",{staticClass:"el-icon-question"})]),e._v("\n                组件路径\n              ")],1),e._v(" "),a("el-input",{attrs:{placeholder:"请输入组件路径"},model:{value:e.form.component,callback:function(t){e.$set(e.form,"component",t)},expression:"form.component"}})],1)],1):e._e(),e._v(" "),a("el-col",{attrs:{span:24}},["M"!=e.form.menuType?a("el-form-item",[a("el-input",{attrs:{placeholder:"请输入权限标识",maxlength:"100"},model:{value:e.form.perms,callback:function(t){e.$set(e.form,"perms",t)},expression:"form.perms"}}),e._v(" "),a("span",{attrs:{slot:"label"},slot:"label"},[a("el-tooltip",{attrs:{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",placement:"top"}},[a("i",{staticClass:"el-icon-question"})]),e._v("\n                权限字符\n              ")],1)],1):e._e()],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",[a("span",{attrs:{slot:"label"},slot:"label"},[a("el-tooltip",{attrs:{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"}},[a("i",{staticClass:"el-icon-question"})]),e._v("\n                显示状态\n              ")],1),e._v(" "),a("el-radio-group",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:show:status"],expression:"['platform:menu:show:status']"}],model:{value:e.form.isShow,callback:function(t){e.$set(e.form,"isShow",t)},expression:"form.isShow"}},e._l(e.showStatus,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:menu:update"],expression:"['platform:menu:update']"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),e._v(" "),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)],1)},r=[],l=a("2eb3"),o=a("ca17"),i=a.n(o),s=(a("542c"),a("61f7")),m={name:"Menu",components:{Treeselect:i.a},data:function(){return{tableHeight:0,listLoading:!0,showSearch:!0,menuList:[],menuOptions:[],title:"",open:!1,isExpandAll:!1,refreshTable:!0,queryParams:{name:"",menuType:""},form:{},menuDataList:[],rules:{name:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],sort:[{required:!0,message:"菜单顺序不能为空",trigger:"blur"}]},statusOptions:[{value:"M",label:"目录"},{value:"C",label:"菜单"},{value:"A",label:"按钮"}],showStatus:[{label:"显示",value:!0},{label:"隐藏",value:!1}],name:""}},mounted:function(){var e=this;this.$nextTick((function(){var t=e.$refs.tableheader.offsetHeight;e.tableHeight=e.$selfUtil.getTableHeight(t)}))},created:function(){this.getList(1)},methods:{addIcon:function(){var e=this;e.$modalIcon((function(t){e.form.icon=t}))},getList:function(e){var t=this;this.listLoading=!0,this.queryParams.name=encodeURIComponent(this.queryParams.name),Object(l["i"])(this.queryParams).then((function(e){var a={},n=[];e.forEach((function(e){a=e,a.parentId=e.pid,a.children=[],n.push(a)})),t.menuDataList=n,t.menuList=t.handleTree(n,"menuId"),t.getTreeselect(),t.listLoading=!1}))},normalizer:function(e){return e.children&&!e.children.length&&delete e.children,{id:e.id?e.id:0,label:e.name?e.name:"主目录",children:e.children}},getTreeselect:function(){this.menuOptions=[];var e={menuId:0,menuName:"主类目",children:[]};e.children=this.handleTree(this.menuDataList,"menuId"),this.menuOptions.push(e)},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={menuId:"",parentId:0,name:"",icon:"",menuType:"M",sort:0,isShow:!0,component:"",perms:""},this.resetForm("form")},handleQuery:function(){this.getList(1)},resetQuery:function(){this.queryParams={name:"",menuType:""},this.handleQuery()},handleAdd:function(e){this.reset(),null!=e&&e.id?this.form.pid=e.id:this.form.pid=0,this.open=!0,this.title="添加菜单"},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},handleUpdate:function(e){var t=this,a=this.$loading({lock:!0,text:"Loading"});this.reset(),this.getTreeselect(),Object(l["h"])(e.id).then((function(e){t.form=e,t.open=!0,t.title="修改菜单",a.close()}))},submitForm:Object(s["a"])((function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?Object(l["j"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList(1)})):Object(l["f"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList(1)})))}))})),handleDelete:function(e){var t=this;this.$modal.confirm('是否确认删除名称为"'+e.name+'"的数据项？').then((function(){return Object(l["g"])(e.id)})).then((function(){t.getList(1),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},u=m,c=(a("a235"),a("2877")),d=Object(c["a"])(u,n,r,!1,null,null,null);t["default"]=d.exports},a235:function(e,t,a){"use strict";a("f704")},f704:function(e,t,a){}}]);