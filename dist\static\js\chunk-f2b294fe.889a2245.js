(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f2b294fe"],{"86a6":function(t,a,e){"use strict";e("de9c")},cd05:function(t,a,e){"use strict";e.d(a,"i",(function(){return s})),e.d(a,"h",(function(){return l})),e.d(a,"g",(function(){return n})),e.d(a,"k",(function(){return o})),e.d(a,"c",(function(){return r})),e.d(a,"d",(function(){return c})),e.d(a,"e",(function(){return u})),e.d(a,"p",(function(){return m})),e.d(a,"m",(function(){return d})),e.d(a,"n",(function(){return f})),e.d(a,"o",(function(){return g})),e.d(a,"f",(function(){return p})),e.d(a,"j",(function(){return v})),e.d(a,"b",(function(){return h})),e.d(a,"a",(function(){return b}));var i=e("b775");function s(t){return Object(i["a"])({url:"/admin/platform/finance/merchant/closing/list",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/admin/platform/finance/merchant/closing/remark",method:"post",data:t})}function n(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/detail/".concat(t),method:"get"})}function o(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/proof",method:"POST",data:t})}function r(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/audit",method:"POST",data:t})}function c(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/config",method:"get"})}function u(t){return Object(i["a"])({url:"admin/platform/finance/merchant/closing/config/edit",method:"post",data:t})}function m(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/remark",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/audit",method:"POST",data:t})}function f(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/list",method:"get",params:t})}function g(t){return Object(i["a"])({url:"/admin/platform/finance/user/closing/proof",method:"POST",data:t})}function p(t){return Object(i["a"])({url:"admin/platform/finance/daily/statement/list",method:"get",params:t})}function v(t){return Object(i["a"])({url:"admin/platform/finance/month/statement/list",method:"get",params:t})}function h(t){return Object(i["a"])({url:"admin/platform/finance/funds/flow",method:"get",params:t})}function b(t){return i["a"].get("financial_record/export",t)}},de9c:function(t,a,e){},ef91:function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"divBox"},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("div",{staticClass:"container"},[e("el-form",{attrs:{size:"small",inline:"","label-width":"100px"}},[e("el-form-item",{staticClass:"width100",staticStyle:{display:"block"},attrs:{label:"时间选择："}},[e("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(a){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(a){t.$set(t.tableFrom,"dateLimit",a)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(a,i){return e("el-radio-button",{key:i,attrs:{label:a.val}},[t._v(t._s(a.text))])})),1),t._v(" "),e("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(a){t.timeVal=a},expression:"timeVal"}})],1),t._v(" "),e("el-form-item",{attrs:{label:"商户名称："}},[e("merchant-name",{on:{getMerId:t.getMerId}})],1),t._v(" "),e("el-form-item",{attrs:{label:"审核状态："}},[e("el-radio-group",{attrs:{type:"button"},on:{change:function(a){return t.getList(1)}},model:{value:t.tableFrom.auditStatus,callback:function(a){t.$set(t.tableFrom,"auditStatus",a)},expression:"tableFrom.auditStatus"}},[e("el-radio-button",{attrs:{label:""}},[t._v("全部 ")]),t._v(" "),e("el-radio-button",{attrs:{label:"0"}},[t._v("待审核")]),t._v(" "),e("el-radio-button",{attrs:{label:"1"}},[t._v("审核通过")]),t._v(" "),e("el-radio-button",{attrs:{label:"2"}},[t._v("审核失败")])],1)],1),t._v(" "),e("el-form-item",{attrs:{label:"到账状态："}},[e("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(a){return t.getList(1)}},model:{value:t.tableFrom.accountStatus,callback:function(a){t.$set(t.tableFrom,"accountStatus",a)},expression:"tableFrom.accountStatus"}},t._l(t.arrivalStatusList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),e("el-form-item",{attrs:{label:"结算类型："}},[e("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(a){return t.getList(1)}},model:{value:t.tableFrom.closingType,callback:function(a){t.$set(t.tableFrom,"closingType",a)},expression:"tableFrom.closingType"}},t._l(t.closingTypeList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),e("br"),t._v(" "),e("el-form-item",{staticClass:"width100",attrs:{label:"结算单号："}},[e("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入结算单号",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.getList(1)}},model:{value:t.tableFrom.closingNo,callback:function(a){t.$set(t.tableFrom,"closingNo",a)},expression:"tableFrom.closingNo"}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:merchant:closing:page:list"],expression:"['platform:finance:merchant:closing:page:list']"}],staticClass:"el-button-solt",attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:function(a){return t.getList(1)}},slot:"append"})],1)],1),t._v(" "),e("br")],1)],1)]),t._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",staticStyle:{width:"100%"},attrs:{"tooltip-effect":"dark",data:t.tableData.data}},[e("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),t._v(" "),e("el-table-column",{attrs:{prop:"closingNo",label:"结算单号","min-width":"100"}}),t._v(" "),e("el-table-column",{attrs:{prop:"merName",label:"商户名称","min-width":"120"}}),t._v(" "),e("el-table-column",{attrs:{prop:"amount",label:"金额","min-width":"120"}}),t._v(" "),e("el-table-column",{attrs:{label:"审核员姓名","min-width":"120","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("filterEmpty")(a.row.auditName)))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"结算类型","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("closingTypeFilter")(a.row.closingType)))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"审核状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(0==a.row.auditStatus?"待审核":1==a.row.auditStatus?"审核通过":"审核失败"))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"到账状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(1==a.row.accountStatus?"已转账":"未转账"))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"平台备注","min-width":"120","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("filterEmpty")(a.row.platformMark)))])]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"createTime",label:"申请时间","min-width":"120","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:merchant:closing:detail"],expression:"['platform:finance:merchant:closing:detail']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.closingDetail(a.row.closingNo,1)}}},[t._v("转账详情")]),t._v(" "),e("el-button",{directives:[{name:"show",rawName:"v-show",value:0===a.row.auditStatus,expression:"scope.row.auditStatus === 0"},{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:merchant:closing:audit"],expression:"['platform:finance:merchant:closing:audit']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.closingDetail(a.row.closingNo,2)}}},[t._v("审核")]),t._v(" "),e("el-button",{directives:[{name:"show",rawName:"v-show",value:1===a.row.auditStatus&&0===a.row.accountStatus,expression:"scope.row.auditStatus === 1 && scope.row.accountStatus === 0"},{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:merchant:closing:proof"],expression:"['platform:finance:merchant:closing:proof']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.closingDetail(a.row.closingNo,3)}}},[t._v("转账")]),t._v(" "),e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:finance:merchant:closing:remark"],expression:"['platform:finance:merchant:closing:remark']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.onRemark(a.row)}}},[t._v("备注")])]}}])})],1),t._v(" "),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),e("el-drawer",{attrs:{direction:"rtl",visible:t.dialogVisible,size:"700px"},on:{"update:visible":function(a){t.dialogVisible=a},close:t.close}},[e("div",{staticClass:"title",attrs:{slot:"title"},slot:"title"},[t._v("结算详情")]),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"box-container"},[e("div",{staticClass:"acea-row"},[e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("商户名称：")]),t._v(t._s(t.closingData.merName))]),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("商户流水金额：")]),t._v(t._s(t.closingData.amount))]),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("商户余额：")]),t._v(t._s(t.closingData.balance))]),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("商户收款方式：")]),t._v(t._s(t._f("closingTypeFilter")(t.closingData.closingType))+"\n        ")]),t._v(" "),"bank"===t.closingData.closingType?[e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("开户银行：")]),t._v(t._s(t.closingData.closingBank))]),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("银行账号：")]),t._v(t._s(t.closingData.closingBankCard))]),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("开户户名：")]),t._v(t._s(t.closingData.closingName))])]:t._e(),t._v(" "),"wechat"===t.closingData.closingType?e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("微信号：")]),t._v(t._s(t.closingData.wechatNo)+"\n        ")]):t._e(),t._v(" "),"alipay"===t.closingData.closingType?e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("支付宝账号：")]),t._v(t._s(t.closingData.alipayAccount)+"\n        ")]):t._e(),t._v(" "),"bank"!==t.closingData.closingType?e("div",{staticClass:"list sp100 acea-row"},[e("label",{staticClass:"name"},[t._v("收款二维码：")]),t._v(" "),e("div",{staticClass:"demo-image__preview"},[e("el-image",{attrs:{src:t.closingData.paymentCode,"preview-src-list":[t.closingData.paymentCode]}})],1)]):t._e(),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("审核状态：")]),t._v(t._s(0==t.closingData.auditStatus?"待审核":1==t.closingData.auditStatus?"已审核":"审核失败")+"\n        ")]),t._v(" "),1==t.closingData.auditStatus?e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("审核时间：")]),t._v(t._s(t._f("filterEmpty")(t.closingData.auditTime))+"\n        ")]):t._e(),t._v(" "),t.closingData.closingProof?e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("结算凭证：")]),t._v(" "),e("div",{staticClass:"acea-row"},t._l(JSON.parse(t.closingData.closingProof),(function(a,i){return e("div",{key:i,staticClass:"pictrue"},[e("img",{attrs:{src:a},on:{click:function(e){return t.getPicture(a)}}})])})),0)]):t._e(),t._v(" "),1==t.closingData.auditStatus&&t.closingData.closingTime?e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("结算时间：")]),t._v(t._s(t.closingData.closingTime)+"\n        ")]):t._e(),t._v(" "),2==t.closingData.auditStatus&&t.closingData.refusalReason?e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("审核未通过原因：")]),t._v(t._s(t.closingData.refusalReason)+"\n        ")]):t._e(),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("平台备注：")]),t._v(t._s(t._f("filterEmpty")(t.closingData.platformMark))+"\n        ")]),t._v(" "),e("div",{staticClass:"list sp100"},[e("label",{staticClass:"name"},[t._v("商户备注：")]),t._v(t._s(t._f("filterEmpty")(t.closingData.mark)))])],2),t._v(" "),1!==t.isShow?e("div",{staticClass:"from-foot-btn fix btn-shadow"},[e("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px"}},[2===t.isShow&&0==t.closingData.auditStatus?[e("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[e("el-radio-group",{model:{value:t.ruleForm.auditStatus,callback:function(a){t.$set(t.ruleForm,"auditStatus",a)},expression:"ruleForm.auditStatus"}},[e("el-radio",{attrs:{label:1}},[t._v("通过")]),t._v(" "),e("el-radio",{attrs:{label:2}},[t._v("拒绝")])],1)],1),t._v(" "),2===t.ruleForm.auditStatus?e("el-form-item",{attrs:{label:"原因",prop:"refusalReason"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入原因"},model:{value:t.ruleForm.refusalReason,callback:function(a){t.$set(t.ruleForm,"refusalReason",a)},expression:"ruleForm.refusalReason"}})],1):t._e()]:t._e(),t._v(" "),3===t.isShow&&1===t.closingData.auditStatus&&0===t.closingData.accountStatus?e("el-form-item",{attrs:{label:"转账凭证：",prop:"closingProof"}},[e("div",{staticClass:"acea-row"},[t.ruleForm.closingProof.length>0?e("div",{staticClass:"acea-row"},t._l(t.ruleForm.closingProof,(function(a,i){return e("div",{key:i,staticClass:"pictrue"},[e("img",{attrs:{src:a},on:{click:function(e){return t.getPicture(a)}}}),t._v(" "),e("i",{staticClass:"el-icon-error btndel",on:{click:function(a){return t.handleRemove(i)}}})])})),0):t._e(),t._v(" "),e("el-upload",{directives:[{name:"show",rawName:"v-show",value:t.ruleForm.closingProof.length<6,expression:"ruleForm.closingProof.length < 6"}],staticClass:"upload-demo",attrs:{action:"","http-request":t.handleUploadForm,headers:t.myHeaders,"show-file-list":!1,multiple:""}},[e("div",{staticClass:"upLoadPicBox"},[e("div",{staticClass:"upLoad"},[e("i",{staticClass:"el-icon-upload2"})])])])],1)]):t._e(),t._v(" "),e("el-form-item",[e("el-button",{on:{click:t.close}},[t._v("取 消")]),t._v(" "),e("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.onSubmit("ruleForm")}}},[t._v(t._s(t.loadingBtn?"提交中 ...":"确 定"))])],1)],2)],1):t._e()])]),t._v(" "),t.pictureVisible?e("el-dialog",{attrs:{visible:t.pictureVisible,width:"700px"},on:{"update:visible":function(a){t.pictureVisible=a}}},[e("img",{staticClass:"pictures",attrs:{src:t.pictureUrl}})]):t._e()],1)},s=[],l=e("cd05"),n=e("6ed5"),o=e("5f87"),r=e("785a"),c={name:"transferAccount",data:function(){return{myHeaders:{"X-Token":Object(o["a"])()},isShow:0,loadingBtn:!1,rules:{auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],refusalReason:[{required:!0,message:"请填写拒绝原因",trigger:"blur"}],closingProof:[{required:!0,message:"请上传结算凭证",type:"array",trigger:"change"}]},tableData:{data:[],total:0},arrivalStatusList:[{label:"已到账",value:1},{label:"未到账",value:0}],closingTypeList:[{label:"银行卡",value:"bank"},{label:"微信",value:"wechat"},{label:"支付宝",value:"alipay"}],listLoading:!0,tableFrom:{dateLimit:"",page:1,limit:20,closingNo:"",auditStatus:"",accountStatus:"",merId:""},timeVal:[],fromList:this.$constants.fromList,loading:!1,dialogVisible:!1,pictureVisible:!1,closingData:{},baseInfoform:{amount:0,mark:"",transferType:""},merchantList:[],search:{limit:10,page:1,keywords:""},ruleForm:{refusalReason:"",auditStatus:1,id:"",closingProof:[]},localImg:"",closingNo:""}},components:{merchantName:n["a"]},mounted:function(){this.getList(1)},methods:{getMerId:function(t){this.tableFrom.merId=t,this.getList(1)},onRemark:function(t){var a=this;this.$prompt("备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入备注",inputType:"textarea",inputValue:t.platformMark,inputPlaceholder:"请输入备注",inputValidator:function(t){if(!t)return"请输入备注"}}).then((function(e){var i=e.value;Object(l["h"])({closingNo:t.closingNo,remark:i}).then((function(t){a.$message({type:"success",message:"提交成功"}),a.getList("")}))})).catch((function(){a.$message({type:"info",message:"取消输入"})}))},onSubmit:function(t){var a=this;if(2===this.isShow)this.$refs[t].validate((function(t){if(!t)return!1;a.loadingBtn=!0;var e={closingNo:a.closingNo,refusalReason:a.ruleForm.refusalReason,auditStatus:a.ruleForm.auditStatus};Object(l["c"])(e).then((function(t){a.$message.success("操作成功"),a.dialogVisible=!1,a.getList(1),a.close(),a.loadingBtn=!1})).catch((function(t){a.loadingBtn=!1}))}));else{this.loadingBtn=!0;var e={closingNo:this.closingNo,closingProof:JSON.stringify(this.ruleForm.closingProof)};this.$refs[t].validate((function(t){t&&Object(l["k"])(e).then((function(t){a.$message.success("操作成功"),a.dialogVisible=!1,a.getList(1),a.close(),a.loadingBtn=!1})).catch((function(t){a.loadingBtn=!1}))}))}},handleUploadForm:function(t){var a=this,e=new FormData,i={model:"finance",pid:0};e.append("multipart",t.file);var s=this.$loading({lock:!0,text:"上传中，请稍候...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Object(r["d"])(e,i).then((function(t){s.close(),a.$message.success("上传成功"),a.ruleForm.closingProof.push(t.url)})).catch((function(t){s.close()}))},handleRemove:function(t){this.ruleForm.closingProof.splice(t,1)},close:function(){this.dialogVisible=!1,this.$refs["ruleForm"].resetFields(),this.ruleForm.closingProof=[]},closingDetail:function(t,a){var e=this;this.closingNo=t,this.isShow=a,this.dialogVisible=!0,this.loading=!0,Object(l["g"])(t).then((function(t){e.closingData=t,e.loading=!1})).catch((function(t){e.loading=!1}))},getPicture:function(t){this.pictureVisible=!0,this.pictureUrl=t},selectChange:function(t){this.tableFrom.dateLimit=t,this.timeVal=[],this.getList(1)},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.getList(1)},exportRecord:function(){var t=this;Object(l["transferRecordsExportApi"])(this.tableFrom).then((function(a){var e=t.$createElement;t.$msgbox({title:"提示",message:e("p",null,[e("span",null,'文件正在生成中，请稍后点击"'),e("span",{style:"color: teal"},"导出记录"),e("span",null,'"查看~ ')]),confirmButtonText:"我知道了"}).then((function(t){}))})).catch((function(a){t.$message.error(a.message)}))},getExportFileList:function(){this.$refs.exportList.exportFileList()},getList:function(t){var a=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(l["i"])(this.tableFrom).then((function(t){a.tableData.data=t.list,a.tableData.total=t.total,a.listLoading=!1})).catch((function(t){a.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList("")},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList("")},handleClose:function(){this.dialogLogistics=!1}}},u=c,m=(e("86a6"),e("2877")),d=Object(m["a"])(u,i,s,!1,null,"e5ed93fc",null);a["default"]=d.exports}}]);