(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1ee279de"],{"60e4":function(e,t,i){},a02e:function(e,t,i){"use strict";i("60e4")},eeb4:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("el-form",{ref:"signForm",staticClass:"demo-ruleForm",attrs:{model:e.signForm,rules:e.rules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"是否开启签到：",prop:"isSwitch"}},[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},model:{value:e.signForm.isSwitch,callback:function(t){e.$set(e.signForm,"isSwitch",t)},expression:"signForm.isSwitch"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"每日签到奖励：",prop:"integral"}},[i("div",{staticClass:"acea-row"},[i("el-checkbox",{model:{value:e.signForm.isIntegral,callback:function(t){e.$set(e.signForm,"isIntegral",t)},expression:"signForm.isIntegral"}},[e._v("赠送积分值")]),e._v(" "),i("el-input-number",{staticClass:"ml40",attrs:{min:0,max:9999,step:1,label:"积分值"},model:{value:e.signForm.integral,callback:function(t){e.$set(e.signForm,"integral",t)},expression:"signForm.integral"}})],1)]),e._v(" "),i("el-form-item",{attrs:{prop:"experience"}},[i("div",{staticClass:"acea-row"},[i("el-checkbox",{model:{value:e.signForm.isExperience,callback:function(t){e.$set(e.signForm,"isExperience",t)},expression:"signForm.isExperience"}},[e._v("赠送经验值")]),e._v(" "),i("el-input-number",{staticClass:"ml40",attrs:{min:0,max:9999,step:1,label:"经验值"},model:{value:e.signForm.experience,callback:function(t){e.$set(e.signForm,"experience",t)},expression:"signForm.experience"}})],1)]),e._v(" "),i("el-form-item",{attrs:{label:"签到规则说明：",prop:"name"}},[i("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",rows:2,placeholder:"请输入内容"},model:{value:e.signForm.signRuleDescription,callback:function(t){e.$set(e.signForm,"signRuleDescription",t)},expression:"signForm.signRuleDescription"}}),e._v(" "),i("el-button",{attrs:{size:"small"},on:{click:function(t){e.signForm.signRuleDescription=e.defaultDescription}}},[e._v("使用默认说明")])],1),e._v(" "),i("el-form-item",{staticClass:"title",attrs:{label:"连续签到奖励："}},[i("el-alert",{attrs:{closable:!1,title:" 连续签到奖励断签后会重新计算连续签到天数，达到连续天数后即可获得连续奖励",type:"warning"}})],1),e._v(" "),i("el-form-item",[e.checkPermi(["platform:sign:edit:base:config"])?i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("signForm")}}},[e._v("提交")]):e._e()],1)],1),e._v(" "),e.checkPermi(["platform:sign:add:config"])?i("el-button",{staticClass:"mt20 mb15",attrs:{type:"primary"},on:{click:e.addSign}},[e._v("新增连续签到奖励")]):e._e(),e._v(" "),i("el-table",{attrs:{data:e.signConfigList,border:""}},[i("el-table-column",{attrs:{label:"连续天数","min-width":"90",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{directives:[{name:"show",rawName:"v-show",value:t.row.show,expression:"scope.row.show"}],attrs:{placeholder:"请输入内容"},model:{value:t.row.day,callback:function(i){e.$set(t.row,"day",i)},expression:"scope.row.day"}}),e._v(" "),i("span",{directives:[{name:"show",rawName:"v-show",value:!t.row.show,expression:"!scope.row.show"}]},[e._v(e._s(t.row.day))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"连续奖励","min-width":"800",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"acea-row row-center-wrapper"},[i("el-checkbox",{attrs:{disabled:!t.row.show},model:{value:t.row.isIntegral,callback:function(i){e.$set(t.row,"isIntegral",i)},expression:"scope.row.isIntegral"}},[e._v("赠送积分值")]),e._v(" "),i("el-input-number",{staticClass:"mr20",attrs:{disabled:!t.row.show,min:0,max:9999,step:1,label:"积分值"},model:{value:t.row.integral,callback:function(i){e.$set(t.row,"integral",i)},expression:"scope.row.integral"}}),e._v(" "),i("el-checkbox",{staticClass:"ml40 mr20",attrs:{disabled:!t.row.show},model:{value:t.row.isExperience,callback:function(i){e.$set(t.row,"isExperience",i)},expression:"scope.row.isExperience"}},[e._v("赠送经验值")]),e._v(" "),i("el-input-number",{attrs:{disabled:!t.row.show,min:0,max:9999,step:1,label:"经验值"},model:{value:t.row.experience,callback:function(i){e.$set(t.row,"experience",i)},expression:"scope.row.experience"}})],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.checkPermi(["platform:sign:edit:award:config"])?i("el-button",{attrs:{type:"text"},on:{click:function(i){return e.editSign(t.row)}}},[e._v("编辑")]):e._e(),e._v(" "),e.checkPermi(["platform:sign:add:config"])?i("el-button",{attrs:{type:"text"},on:{click:function(i){return e.saveSign(t.row)}}},[e._v("保存")]):e._e(),e._v(" "),e.checkPermi(["platform:sign:delete:config"])?i("el-button",{attrs:{type:"text"},on:{click:function(i){return e.delSign(t.row)}}},[e._v("删除")]):e._e()]}}])})],1)],1)],1)},s=[],r=i("b7be"),o=i("e350"),a={day:0,experience:0,integral:0,id:0,isExperience:!0,isIntegral:!0,show:!0},l={name:"index",data:function(){return{signForm:{day:0,experience:0,id:0,integral:0,isSwitch:!1,isExperience:!0,isIntegral:!0,signRuleDescription:""},defaultDescription:"1.每天签到可以获得每天签到奖励",rules:{isIntegral:[{required:!0,message:"请输入积分值",trigger:"blur"}],experience:[{required:!0,message:"请输入经验值",trigger:"blur"}]},signConfigList:[],showTab:!1}},mounted:function(){this.getSignConfig()},methods:{checkPermi:o["a"],getSignConfig:function(){var e=this;Object(r["l"])().then((function(t){e.signForm=t.baseSignConfig,e.$set(e,"signConfigList",t.signConfigList),e.$set(e.signForm,"signRuleDescription",t.signRuleDescription)}))},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;Object(r["i"])(t.signForm).then((function(){t.$message.success("操作成功"),t.getSignConfig()}))}))},addSign:function(){this.signConfigList.push(Object.assign({},a))},editSign:function(e){this.$set(e,"show",!0)},delSign:function(e){var t=this;this.$modalSure("删除当前数据?").then((function(){Object(r["j"])(e.id).then((function(e){t.$message.success("删除成功"),t.getSignConfig()}))})).catch((function(){}))},saveSign:function(e){var t=this;e.id?Object(r["k"])(e).then((function(){t.$set(e,"show",!1),t.$message.success("操作成功"),t.getSignConfig()})):Object(r["h"])(e).then((function(){t.$set(e,"show",!1),t.$message.success("操作成功"),t.getSignConfig()}))}}},c=l,u=(i("a02e"),i("2877")),g=Object(u["a"])(c,n,s,!1,null,"ab23d23a",null);t["default"]=g.exports}}]);