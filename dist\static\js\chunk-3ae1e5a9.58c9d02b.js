(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3ae1e5a9"],{"40d2":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div")},l=[],n=i("2877"),s={},o=Object(n["a"])(s,a,l,!1,null,null,null);e["a"]=o.exports},d1da:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"container"},[i("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[i("el-form-item",{attrs:{label:"关键字"}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入id，名称，描述",clearable:"",size:"small"},model:{value:t.listPram.keywords,callback:function(e){t.$set(t.listPram,"keywords",e)},expression:"listPram.keywords"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handlerSearch},slot:"append"})],1)],1),t._v(" "),t.selectModel?i("el-form-item",[i("el-button",{attrs:{type:"primary",disabled:!t.selectedConfigData.id},on:{click:t.handlerConfimSelect}},[t._v("确定选择")])],1):t._e()],1)],1),t._v(" "),t.selectModel?t._e():i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:form:save"],expression:"['platform:system:form:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerEditData({},0)}}},[t._v("创建表单")])],1),t._v(" "),i("el-table",{staticClass:"table",attrs:{data:t.dataList.list,"highlight-current-row":t.selectModel,size:"mini","header-cell-style":{fontWeight:"bold"}},on:{"current-change":t.handleCurrentRowChange}},[i("el-table-column",{attrs:{label:"ID",prop:"id",width:"80"}}),t._v(" "),i("el-table-column",{attrs:{label:"名称",prop:"name","min-width":"180"}}),t._v(" "),i("el-table-column",{attrs:{label:"描述",prop:"info","min-width":"220"}}),t._v(" "),i("el-table-column",{attrs:{label:"更新时间",prop:"updateTime","min-width":"200"}}),t._v(" "),t.selectModel?t._e():i("el-table-column",{attrs:{label:"操作","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:system:form:update"],expression:"['platform:system:form:update']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return t.handlerEditData(e.row,1)}}},[t._v("编辑")])]}}],null,!1,3256910730)})],1),t._v(" "),i("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":[10,20,30,40],layout:t.constants.page.layout,total:t.dataList.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),i("el-dialog",{attrs:{visible:t.editDialogConfig.visible,fullscreen:"",title:0===t.editDialogConfig.isCreate?"创建表单":"编辑表单","destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?i("edit",{attrs:{"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.editData},on:{hideDialog:t.handlerHide}}):t._e()],1)],1)},l=[],n=i("92c6"),s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("config-list",{attrs:{"edit-data":t.editData,"is-create":t.isCreate},on:{getFormConfigDataResult:t.handlerGetFormConfigData}})],1)},o=[],r=i("5abd"),d={components:{configList:r["a"]},props:{editData:{type:Object,default:{}},isCreate:{type:Number,default:0}},data:function(){return{}},methods:{handlerGetFormConfigData:function(t){t.id?this.handlerEdit(t):this.handlerSave(t)},handlerSave:function(t){var e=this;n["d"](t).then((function(t){e.$message.success("创建表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))},handlerEdit:function(t){var e=this;n["a"](t).then((function(t){e.$message.success("编辑表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))}}},c=d,h=i("2877"),u=Object(h["a"])(c,s,o,!1,null,"6841b549",null),m=u.exports,f={components:{edit:m},props:{selectModel:{type:Boolean,default:!1}},data:function(){return{constants:this.$constants,listPram:{keywords:null,page:1,limit:10},editDialogConfig:{visible:!1,editData:{},isCreate:0},dataList:{list:[],total:0},selectedConfigData:{}}},mounted:function(){this.handlerGetList(this.listPram)},methods:{handlerSearch:function(){this.listPram.page=1,this.handlerGetList(this.listPram)},handlerGetList:function(t){var e=this;n["c"](t).then((function(t){e.dataList=t}))},handlerEditData:function(t,e){this.editDialogConfig.editData=0===e?{}:t,this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},handlerHide:function(){this.editDialogConfig.editData={},this.editDialogConfig.isCreate=0,this.editDialogConfig.visible=!1,this.handlerGetList(this.listPram)},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetList(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetList(this.listPram)},handleCurrentRowChange:function(t){this.selectedConfigData=t},handlerConfimSelect:function(){this.$emit("selectedRowData",this.selectedConfigData)}}},g=f,p=Object(h["a"])(g,a,l,!1,null,"12386e80",null);e["default"]=p.exports}}]);